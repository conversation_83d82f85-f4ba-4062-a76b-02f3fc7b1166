# ====================== 文档检查API测试 ======================
echo -e "${BLUE}=== 文档检查API测试 ===${NC}"
echo -e "${YELLOW}正在测试文档检查接口...${NC}"

# 测试网络是否连通
echo -e "${YELLOW}正在测试网络是否连通...${NC}"
# 测试直连
echo -e "${YELLOW}正在测试直连...${NC}"
curl -v -X POST \
    -F "file=@/test.txt;type=text/plain" \
    -F "accessMode=STANDALONE" \
    -F "checkUnitId=BALANCEABILITY" \
    -F "fileType=HTML" \
    -F "extension={\"title\":\"请在此输入标题\",\"mainSubmit\":[],\"copySubmit\":[]}" \
    http://article-auto-compose-server:8080/ai-doc-compose/api/v1/compose'
echo

# 测试文档为test.txt，请确保该文件存在
echo -e "${YELLOW}正在测试文档检查代理接口...${NC}"
curl -v -X POST \
    -F "file=@/test.txt;type=text/plain" \
    -F "accessMode=STANDALONE" \
    -F "checkUnitId=BALANCEABILITY" \
    -F "fileType=HTML" \
    -F "extension={\"title\":\"请在此输入标题\",\"mainSubmit\":[],\"copySubmit\":[]}" \
    http://localhost/ai-doc-compose/api/v1/compose

echo
echo -e "${GREEN}=== 文档检查API测试结束 ===${NC}"