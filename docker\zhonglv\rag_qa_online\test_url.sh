#!/bin/bash

# RAG QA Online 接口测试脚本

# 设置颜色变量
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 默认设置
DEFAULT_HOST="localhost"
DEFAULT_PORT="8085"
DEFAULT_ENDPOINT="/Rag/QAOnlineSSE"
DEFAULT_QUERY="研发项目立项有哪些环节"

# 帮助函数
show_help() {
    echo -e "${YELLOW}RAG QA Online 接口测试脚本${NC}"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --host HOST       设置主机地址 (默认: $DEFAULT_HOST)"
    echo "  -p, --port PORT       设置端口号 (默认: $DEFAULT_PORT)"
    echo "  -q, --query QUERY     设置查询内容 (默认: $DEFAULT_QUERY)"
    echo "  --help                显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -h localhost -p 7890 -q \"AI是什么\""
}

# 解析命令行参数
HOST=$DEFAULT_HOST
PORT=$DEFAULT_PORT
QUERY=$DEFAULT_QUERY

while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -h|--host)
            HOST="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -q|--query)
            QUERY="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}错误: 未知选项 $key${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 构建URL
URL="http://${HOST}:${PORT}${DEFAULT_ENDPOINT}"

# 构建请求数据
REQUEST_DATA="{\"text\": \"${QUERY}\", \"history\": []}"

# 打印请求信息
echo -e "${YELLOW}测试 RAG QA Online 接口${NC}"
echo -e "URL: ${GREEN}${URL}${NC}"
echo -e "请求数据: ${GREEN}${REQUEST_DATA}${NC}"
echo -e "${YELLOW}开始请求...${NC}"

# 发送请求
curl -X POST \
     -H "Content-Type: application/json" \
     -d "${REQUEST_DATA}" \
     "${URL}"

echo -e "\n${GREEN}请求完成${NC}"

# 测试另外一个端口 (7890)
if [ "$PORT" != "7890" ]; then
    echo -e "\n${YELLOW}是否要测试端口7890? (y/n)${NC}"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        URL="http://${HOST}:7890${DEFAULT_ENDPOINT}"
        echo -e "${YELLOW}测试 RAG QA Online 接口${NC}"
        echo -e "URL: ${GREEN}${URL}${NC}"
        echo -e "请求数据: ${GREEN}${REQUEST_DATA}${NC}"
        echo -e "${YELLOW}开始请求...${NC}"
        
        curl -X POST \
             -H "Content-Type: application/json" \
             -d "${REQUEST_DATA}" \
             "${URL}"
        
        echo -e "\n${GREEN}请求完成${NC}"
    fi
fi

# 增加更多的测试示例
echo -e "\n${YELLOW}是否要测试更多查询示例? (y/n)${NC}"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    # 从client.py中获取的示例查询
    EXAMPLE_QUERIES=(
        "planing是什么"
        "ai agent是什么"
        "意图识别有哪些场景"
        "员工退休后办理哪些手续"
        "什么是人工智能安全治理框架"
        "广州、深圳的住宿标准"
        "移动党校的位置在哪"
    )
    
    for query in "${EXAMPLE_QUERIES[@]}"; do
        echo -e "\n${YELLOW}测试查询: ${GREEN}${query}${NC}"
        REQUEST_DATA="{\"text\": \"${query}\", \"history\": []}"
        
        curl -X POST \
             -H "Content-Type: application/json" \
             -d "${REQUEST_DATA}" \
             "http://${HOST}:${PORT}${DEFAULT_ENDPOINT}"
        
        echo -e "\n${GREEN}请求完成${NC}"
        echo -e "${YELLOW}按任意键继续，按Ctrl+C退出...${NC}"
        read -r -n 1
    done
fi

echo -e "\n${GREEN}测试脚本执行完毕${NC}" 