# Spring Boot 应用 Jenkins 部署指南

本文档提供使用 Jenkins 部署 Spring Boot 应用的详细步骤和配置说明，使用企业内部基础镜像。

## 准备工作

### 1. Jenkins 配置

#### 1.1 安装必要插件

确保 Jenkins 安装了以下插件：
- Pipeline
- Credentials Plugin
- Config File Provider
- Copy Artifact Plugin

#### 1.2 配置凭据

在 Jenkins 中添加部署服务器的用户名和密码凭据：
1. 访问 `Jenkins管理 -> 凭据 -> 系统 -> 全局凭据`
2. 点击 `添加凭据`
3. 选择类型为 `Username with password`
4. ID 设置为 `server-credentials`（或Jenkinsfile中指定的其他ID）
5. 填写远程服务器的用户名和密码

#### 1.3 配置管理文件

在 Jenkins 中添加以下托管文件：
1. 访问 `Jenkins管理 -> 管理文件`
2. 创建以下文件：
   - ID: `{应用名称}-{环境}-properties`，类型: `Properties file`，内容：Spring Boot 应用配置文件
   - ID: `deploy-app-script`，类型: `Custom file`，内容：deploy_app.sh 部署脚本
   - ID: `spring-boot-dockerfile`，类型: `Custom file`，内容：用于构建应用的Dockerfile

### 2. 服务器环境准备

#### 2.1 安装sshpass

部署脚本使用sshpass进行密码认证，确保Jenkins服务器上已安装sshpass：

**在Ubuntu/Debian上：**
```
sudo apt-get update
sudo apt-get install -y sshpass
```

**在CentOS/RHEL上：**
```
sudo yum install -y sshpass
```

### 3. 创建 Jenkins Pipeline 项目

1. 在 Jenkins 中创建新的 Pipeline 项目
2. 配置 Pipeline 从 SCM 获取，选择 Git，并指定到包含 Jenkinsfile 的仓库
3. 保存配置

## 部署参数

Jenkins Pipeline 接受以下参数，可通过"Build with Parameters"指定：

### 基础参数
- `APP_NAME`: 应用名称，默认值为'spring-boot-app'
- `MODUL_NAME`: 模块名称，默认值为'backend'
- `HOST_PORT`: 宿主机映射端口，默认值为'8080'
- `BUILD_NUMBER`: 构建版本号，默认值为'1.0.0'
- `ENV`: 部署环境，可选值为'dev', 'test', 'prod'

### 服务器与认证参数
- `REMOTE_SERVER`: 远程服务器地址
- `REMOTE_USER`: 远程服务器用户名，默认值为'jenkins'
- `CREDENTIALS_ID`: 凭据ID，默认值为'server-credentials'

### 管理文件参数
- `DEPLOY_SCRIPT_ID`: 部署脚本ID，默认值为'deploy-app-script'
- `APP_PROPERTIES_ID`: 应用配置文件ID，默认为空，将使用`{应用名称}-{环境}-properties`格式
- `DOCKERFILE_ID`: Dockerfile ID，默认值为'spring-boot-dockerfile'

## 部署脚本说明

### deploy_app.sh

部署脚本`deploy_app.sh`负责在远程服务器上完成应用的部署。脚本使用命令行参数：

```bash
./deploy_app.sh -a <应用名称> -m <模块名称> -p <主机端口> -b <构建版本号> -d <Dockerfile路径>
```

参数说明：
- `-a, --app`: 应用名称
- `-m, --module`: 模块名称
- `-p, --port`: 主机端口
- `-b, --build`: 构建版本号
- `-d, --dockerfile`: Dockerfile路径
- `-h, --help`: 显示帮助信息

## Dockerfile 说明

Dockerfile通过Jenkins的Managed Files功能管理，基于企业内部镜像，具有以下特点：
- 基于企业内部 OpenJDK 17 基础镜像
- 创建了应用目录结构：/app, /app/logs, /app/config, /app/files
- 创建了 wensi 用户和组，并设置适当的权限
- 配置了中文环境
- 设置了应用启动命令

## 部署流程说明

本部署方案实现了以下功能：

1. 参数化构建，允许指定应用名称、模块名称、端口等参数
2. 支持多环境部署（dev, test, prod）
3. 使用 Jenkins 的 Managed Files 管理配置文件、Dockerfile和部署脚本
4. 自动将应用 JAR 包、配置文件和 Dockerfile 打包并上传到目标服务器
5. 在目标服务器上执行部署脚本，实现容器化部署
6. 使用sshpass实现自动化密码认证

## 部署目录结构

在目标服务器上，应用将被部署到以下目录：
```
/data/{模块名称}/{应用名称}/
  ├── config/    # 应用配置文件
  ├── logs/      # 应用日志
  └── files/     # 应用数据文件
```

## 启动部署

点击 "Build with Parameters"，填写参数后即可启动部署流程。

## 故障排查

如果部署失败，可以查看以下位置的日志：
1. Jenkins Pipeline 构建日志
2. 目标服务器上的 /data/{模块名称}/{应用名称}/deploy.log
3. sshpass相关错误：检查是否正确安装了sshpass，以及凭据是否正确 