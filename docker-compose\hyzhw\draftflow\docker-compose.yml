version: '3.5'

services:
  poc-intelligence-view-write:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0
    container_name: poc-intelligence-view-write
    ports:
      - "20002:80"
    volumes:
      - ./poc-intelligence-view-write/config/nginx.conf:/etc/nginx/nginx.conf
      - ./poc-intelligence-view-write/shells/test.txt:/test.txt
      - ./poc-intelligence-view-write/shells/test.sh:/test.sh
      - ./poc-intelligence-view-write/logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    command: ["/bin/bash", "-c", "chmod -R 755 /usr/share/nginx/html && nginx -g 'daemon off;'"]  
    networks:
      - jiutianwensi-network  
    restart: always
  poc-intelligence-view:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
    container_name: poc-intelligence-view
    ports:
      - "20003:80"
    volumes:
      - ./poc-intelligence-view/config/nginx.conf:/etc/nginx/nginx.conf
      - ./poc-intelligence-view/shells/test.txt:/test.txt
      - ./poc-intelligence-view/shells/test.sh:/test.sh
      - ./poc-intelligence-view/logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    command: ["/bin/bash", "-c", "chmod -R 755 /usr/share/nginx/html && nginx -g 'daemon off;'"]  
    networks:
      - jiutianwensi-network  
    restart: always
  nginx-common:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4
    container_name: nginx-common
    ports:
      - "20004:80"
    volumes:
      - ./nginx-common/config/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx-common/shells/test.txt:/test.txt
      - ./nginx-common/shells/test.sh:/test.sh
      - ./nginx-common/logs:/var/log/nginx
      - ./nginx-common/html:/usr/share/nginx/html
    environment:
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always      
  ai-doc-poc:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT
    container_name: ai-doc-poc
    ports:
      - "20005:8080"
    volumes:
      - ./ai-doc-poc/logs:/app/logs
      - ./ai-doc-poc/config:/app/config
      - ./ai-doc-poc/shells:/app/shells
    environment:
      - JAVA_OPTS=-Xms512m -Xmx1g
    networks:
      - jiutianwensi-network
    restart: always
  ai-redactor:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18
    container_name: ai-redactor
    ports:
      - "20006:8000"
    volumes:
      - ./ai-redactor/config/application.properties:/app/ai_hegao_plus_app/application.properties
      - ./ai-redactor/logs:/app/log
      - ./ai-redactor/shells:/app/shells
    environment:
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always 
  article-auto-compose-server:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT
    container_name: article-auto-compose-server
    ports:
      - "20007:8080"
    volumes:
      - ./article-auto-compose-server/logs:/app/logs
      - ./article-auto-compose-server/config:/app/config
      - ./article-auto-compose-server/shells:/app/shells
    environment:
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always      
  ai-writer-nostream:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python:v528
    container_name: ai-writer-nostream
    ports:
      - "20008:8080"
    volumes:
      - ./ai-writer-nostream/config/application.properties:/app/temp/web_source_code/backend/application.properties
      - ./ai-writer-nostream/logs:/app/temp/web_source_code/log
      - ./ai-writer-nostream/shells:/app/shells
    environment:
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always
  filebeat-draftflow:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
    container_name: filebeat-draftflow
    user: root
    volumes:
      - ./filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./ai-doc-poc/logs:/data/ai-doc-poc/logs
      - ./ai-redactor/logs:/data/ai-redactor/logs
      - ./ai-writer-nostream/logs:/data/ai-writer-nostream/logs
      - ./article-auto-compose-server/logs:/data/article-auto-compose-server/logs
      - ./poc-intelligence-view-write/logs:/data/poc-intelligence-view-write/logs
      - ./poc-intelligence-view/logs:/data/poc-intelligence-view/logs 
    environment:
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always
networks:
  jiutianwensi-network:
    name: jiutianwensi-network