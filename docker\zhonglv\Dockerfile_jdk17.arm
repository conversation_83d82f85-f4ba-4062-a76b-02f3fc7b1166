#设置基础镜像☆☆☆☆☆☆☆☆☆☆根据工程运行需要配置带有相应环境的运行用途镜像☆☆☆☆☆☆☆☆☆☆
FROM m.daocloud.io/docker.io/arm64v8/openjdk:17-jdk

# 创建必要的目录
RUN mkdir -p /app /app/logs /app/config /app/files /app/shells /home/<USER>

# 创建用户和组
RUN groupadd -g 1000 wensi && \
    useradd -u 1000 -d /home/<USER>/bin/bash -g wensi wensi

# 设置语言环境
ENV LANG=zh_CN.UTF-8 LC_ALL=zh_CN.UTF-8

# 设置目录权限
RUN chmod -Rf 755 /app/ && \
    chown -R wensi:wensi /app/ && \
    chown -R wensi:wensi /home/<USER>/

WORKDIR /app

# 复制应用文件
COPY app.jar /app/app.jar
COPY config/ /app/config/
COPY shells/ /app/shells/

RUN tree /app

# 创建启动脚本
RUN echo '#!/bin/bash' > /app/shells/start.sh && \
    echo 'java -jar /app/app.jar --spring.config.location=/app/config/ --server.port=8080 --server.servlet.context-path=/ --server.context.path=/' >> /app/shells/start.sh && \
    chmod +x /app/shells/start.sh

USER wensi
CMD ["/bin/bash", "/app/shells/start.sh"]