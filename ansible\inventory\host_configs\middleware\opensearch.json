{"middleware_name": "opensearch", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "public.ecr.aws/opensearchproject/opensearch:2.9.0", "host_port": "9200", "container_port": "9200", "host_transport_port": "9300", "container_transport_port": "9300", "username": "admin", "password": "admin", "host_data_dir": "{{base_dir}}/opensearch/data", "host_logs_dir": "{{base_dir}}/opensearch/log", "host_config_dir": "{{base_dir}}/opensearch/config", "container_data_dir": "/usr/share/opensearch/data", "container_logs_dir": "/usr/share/opensearch/logs", "container_config_dir": "/usr/share/opensearch/config", "restart_script": "{{base_dir}}/opensearch/restart.sh", "test_script": "{{base_dir}}/opensearch/test_curl.sh", "env_vars": [{"name": "discovery.type", "value": "single-node"}, {"name": "plugins.security.disabled", "value": "true"}, {"name": "bootstrap.memory_lock", "value": "true"}, {"name": "OPENSEARCH_JAVA_OPTS", "value": "-Xms2g -Xmx2g"}], "config": {"cluster.name": "es-docker-cluster", "node.name": "node-1", "network.host": "0.0.0.0", "discovery.type": "single-node", "xpack.security.enabled": "true", "http.cors.enabled": "true", "http.cors.allow-origin": "*"}, "test_commands": ["curl -s -u admin:admin https://localhost:9200/ | grep -q '\"status\":\"success\"' && echo \"OpenSearch 运行正常\" || echo \"OpenSearch 运行异常\""]}