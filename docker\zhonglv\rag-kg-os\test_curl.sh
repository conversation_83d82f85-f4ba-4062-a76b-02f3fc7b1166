#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# ====================== API测试命令 ======================
echo -e "${BLUE}=== 文档生成API测试 ===${NC}"

echo -e "${GREEN}=== 带extension的请求测试结束 ===${NC}"
PORT="8099"
# OpenSearch认证信息
OS_USER="admin"
OS_PASS="admin"
OS_HOST="*********"
OS_PORT="9200"

# ====================== 知识图谱分类变更API测试 ======================
echo -e "${BLUE}=== 知识图谱分类变更API测试 ===${NC}"
echo -e "${YELLOW}正在测试分类更新API...${NC}"

echo "curl -X POST \"http://1********:$PORT/kg/change/category\" \
  -H \"Content-Type: application/json\" \
  -d '{\"groupId\":\"10001\",\"projectId\":\"1\",\"mark\":\"biaozhizyx\",\"type\":\"update\",\"categoryList\":[{\"oldCategory\":\"一级分类2\",\"newCategory\":\"一级分类2-new\"}]}'"

curl -X POST "http://1********:$PORT/kg/change/category" \
  -H "Content-Type: application/json" \
  -d '{"groupId":"10001","projectId":"1","mark":"biaozhizyx","type":"update","categoryList":[{"oldCategory":"一级分类2","newCategory":"一级分类2-new"}]}'

echo
echo -e "${YELLOW}正在测试分类删除API...${NC}"

echo "curl -X POST \"http://1********:$PORT/kg/change/category\" \
  -H \"Content-Type: application/json\" \
  -d '{\"groupId\":\"10001\",\"projectId\":\"1\",\"mark\":\"biaozhizyx\",\"type\":\"del\",\"category\":\"一级分类1\"}'"

curl -X POST "http://1********:$PORT/kg/change/category" \
  -H "Content-Type: application/json" \
  -d '{"groupId":"10001","projectId":"1","mark":"biaozhizyx","type":"del","category":"一级分类1"}'

echo
echo -e "${GREEN}=== 知识图谱分类变更API测试结束 ===${NC}"

# ====================== 知识图谱管理API测试 ======================
echo -e "${BLUE}=== 知识图谱管理API测试 ===${NC}"
echo -e "${YELLOW}正在测试知识图谱添加API...${NC}"

echo "curl -X POST \"http://1********:$PORT/kg/aud\" \
  -H \"Content-Type: application/json\" \
  -d '{\"name\":\"testkgnamezyx\",\"mark\":\"biaozhizyx\",\"groupId\":\"10001\",\"projectId\":\"1\",\"type\":\"add\"}'"

curl -X POST "http://1********:$PORT/kg/aud" \
  -H "Content-Type: application/json" \
  -d '{"name":"testkgnamezyx","mark":"biaozhizyx","groupId":"10001","projectId":"1","type":"add"}'

echo
echo -e "${YELLOW}正在测试知识图谱删除API...${NC}"

echo "curl -X POST \"http://1********:$PORT/kg/aud\" \
  -H \"Content-Type: application/json\" \
  -d '{\"name\":\"testkgnamezyx\",\"mark\":\"biaozhizyx\",\"groupId\":\"10001\",\"projectId\":\"1\",\"type\":\"del\"}'"

curl -X POST "http://1********:$PORT/kg/aud" \
  -H "Content-Type: application/json" \
  -d '{"name":"testkgnamezyx","mark":"biaozhizyx","groupId":"10001","projectId":"1","type":"del"}'

echo
echo -e "${GREEN}=== 知识图谱管理API测试结束 ===${NC}"

# ====================== QA批量添加API测试 ======================
echo -e "${BLUE}=== QA批量添加API测试 ===${NC}"
echo -e "${YELLOW}正在测试QA批量添加API...${NC}"

echo "curl -X POST \"http://1********:$PORT/qa/add/batch\" \
  -H \"Content-Type: application/json\" \
  -d '{\"mark\":\"biaozhizyx\",\"groupId\":\"10001\",\"projectId\":\"1\",\"batchQuestionParams\":[{\"categories\":[\"一级分类1\",\"一级分类1/二级分类1\"],\"question\":\"标准问题batch1\",\"similarQuestions\":[\"相似问题01\",\"相似问题02\"],\"answerList\":[\"答案1\",\"答案2\",\"答案3\"],\"keywordList\":[\"关键词1\",\"关键词2\"],\"startTime\":\"2024-05-10 16:00:00\",\"endTime\":\"2024-05-12 16:00:00\",\"reference\":[\"参考出处1\",\"参考出处2\",\"参考出处3\"]}]}'"

curl -X POST "http://1********:$PORT/qa/add/batch" \
  -H "Content-Type: application/json" \
  -d '{"mark":"biaozhizyx","groupId":"10001","projectId":"1","batchQuestionParams":[{"categories":["一级分类1","一级分类1/二级分类1"],"question":"标准问题batch1","similarQuestions":["相似问题01","相似问题02"],"answerList":["答案1","答案2","答案3"],"keywordList":["关键词1","关键词2"],"startTime":"2024-05-10 16:00:00","endTime":"2024-05-12 16:00:00","reference":["参考出处1","参考出处2","参考出处3"]}]}'

echo
echo -e "${YELLOW}正在测试带文档的QA批量添加API...${NC}"

echo "curl -X POST \"http://1********:$PORT/qa/add/batch\" \
  -H \"Content-Type: application/json\" \
  -d '{\"groupId\":\"10001\",\"projectId\":\"1\",\"mark\":\"biaozhizyx\",\"batchQuestionParams\":[{\"categories\":[\"一级分类1\",\"一级分类1/二级分类1\"],\"question\":\"题1doc\",\"similarQuestions\":[\"相似问题01\",\"相似问题02\"],\"answerList\":[\"答案1\",\"答案2\",\"答案3\"],\"keywordList\":[\"关键词1\",\"关键词2\"],\"startTime\":\"2024-05-10 16:00:00\",\"endTime\":\"2024-05-12 16:00:00\",\"reference\":[\"参考出处1\",\"参考出处2\",\"参考出处3\"],\"documentList\":[{\"uuid\":\"02edb8334548459ba602162de400dcae\",\"name\":\"文档解析.txt\",\"upTime\":\"2024-05-10 10:05:38\"}]}]}'"

curl -X POST "http://1********:$PORT/qa/add/batch" \
  -H "Content-Type: application/json" \
  -d '{"groupId":"10001","projectId":"1","mark":"biaozhizyx","batchQuestionParams":[{"categories":["一级分类1","一级分类1/二级分类1"],"question":"题1doc","similarQuestions":["相似问题01","相似问题02"],"answerList":["答案1","答案2","答案3"],"keywordList":["关键词1","关键词2"],"startTime":"2024-05-10 16:00:00","endTime":"2024-05-12 16:00:00","reference":["参考出处1","参考出处2","参考出处3"],"documentList":[{"uuid":"02edb8334548459ba602162de400dcae","name":"文档解析.txt","upTime":"2024-05-10 10:05:38"}]}]}'

echo
echo -e "${GREEN}=== QA批量添加API测试结束 ===${NC}"

# ====================== QA管理API测试 ======================
echo -e "${BLUE}=== QA管理API测试 ===${NC}"
echo -e "${YELLOW}正在测试QA添加API...${NC}"

echo "curl -X POST \"http://1********:$PORT/qa/aud\" \
  -H \"Content-Type: application/json\" \
  -d '{\"mark\":\"biaozhizyx\",\"groupId\":\"10001\",\"projectId\":\"1\",\"type\":\"add\",\"categories\":[\"一级分类\",\"一级分类/二级分类\",\"一级分类/二级分类/三级分类\"],\"question\":\"问题新增001\",\"similarQuestions\":[\"相似问题1\",\"相似问题2\"],\"answerList\":[\"答案1\",\"答案2\",\"答案3\"],\"keywordList\":[\"关键词1\",\"关键词2\"],\"startTime\":\"2024-05-10 16:00:00\",\"endTime\":\"2024-05-12 16:00:00\",\"reference\":[\"参考出处1\",\"参考出处2\",\"参考出处3\"]}'"

curl -X POST "http://1********:$PORT/qa/aud" \
  -H "Content-Type: application/json" \
  -d '{"mark":"biaozhizyx","groupId":"10001","projectId":"1","type":"add","categories":["一级分类","一级分类/二级分类","一级分类/二级分类/三级分类"],"question":"问题新增001","similarQuestions":["相似问题1","相似问题2"],"answerList":["答案1","答案2","答案3"],"keywordList":["关键词1","关键词2"],"startTime":"2024-05-10 16:00:00","endTime":"2024-05-12 16:00:00","reference":["参考出处1","参考出处2","参考出处3"]}'

echo
echo -e "${YELLOW}正在测试QA更新API...${NC}"

echo "curl -X POST \"http://1********:$PORT/qa/aud\" \
  -H \"Content-Type: application/json\" \
  -d '{\"mark\":\"biaozhizyx\",\"groupId\":\"10001\",\"projectId\":\"1\",\"type\":\"update\",\"categories\":[\"一级分类\",\"一级分类/二级分类\",\"一级分类/二级分类/三级分类\"],\"oldQuestion\":\"问题新增001\",\"question\":\"问题新增001更新版\",\"similarQuestions\":[\"相似问题1\",\"相似问题2\"],\"answerList\":[\"答案1\",\"答案2\",\"答案3\"],\"keywordList\":[\"关键词1\",\"关键词2\"],\"startTime\":\"2024-05-10 16:00:00\",\"endTime\":\"2024-05-12 16:00:00\",\"reference\":[\"参考出处1\",\"参考出处2\",\"参考出处3\"]}'"

curl -X POST "http://1********:$PORT/qa/aud" \
  -H "Content-Type: application/json" \
  -d '{"mark":"biaozhizyx","groupId":"10001","projectId":"1","type":"update","categories":["一级分类","一级分类/二级分类","一级分类/二级分类/三级分类"],"oldQuestion":"问题新增001","question":"问题新增001更新版","similarQuestions":["相似问题1","相似问题2"],"answerList":["答案1","答案2","答案3"],"keywordList":["关键词1","关键词2"],"startTime":"2024-05-10 16:00:00","endTime":"2024-05-12 16:00:00","reference":["参考出处1","参考出处2","参考出处3"]}'

echo
echo -e "${YELLOW}正在测试QA删除API...${NC}"

echo "curl -X POST \"http://1********:$PORT/qa/aud\" \
  -H \"Content-Type: application/json\" \
  -d '{\"mark\":\"biaozhizyx\",\"groupId\":\"10001\",\"projectId\":\"1\",\"type\":\"del\",\"question\":\"问题新增001更新版\"}'"

curl -X POST "http://1********:$PORT/qa/aud" \
  -H "Content-Type: application/json" \
  -d '{"mark":"biaozhizyx","groupId":"10001","projectId":"1","type":"del","question":"问题新增001更新版"}'

echo
echo -e "${YELLOW}正在测试QA批量删除API...${NC}"

echo "curl -X POST \"http://1********:$PORT/qa/aud\" \
  -H \"Content-Type: application/json\" \
  -d '{\"mark\":\"biaozhizyx\",\"groupId\":\"10001\",\"projectId\":\"1\",\"type\":\"batchDel\",\"questionList\":[\"batchTest020\",\"batchTest021\",\"batchTest022\"]}'"

curl -X POST "http://1********:$PORT/qa/aud" \
  -H "Content-Type: application/json" \
  -d '{"mark":"biaozhizyx","groupId":"10001","projectId":"1","type":"batchDel","questionList":["batchTest020","batchTest021","batchTest022"]}'

echo
echo -e "${GREEN}=== QA管理API测试结束 ===${NC}"

# ====================== 查询索引API测试 ======================
echo -e "${BLUE}=== 查询索引API测试 ===${NC}"
echo -e "${YELLOW}正在测试OpenSearch索引查询API...${NC}"

echo "curl -ku '$OS_USER:$OS_PASS' -XGET \"http://$OS_HOST:$OS_PORT/10001-1-biaozhizyx/_search?pretty\""

curl -ku "$OS_USER:$OS_PASS" -XGET "http://$OS_HOST:$OS_PORT/10001-1-biaozhizyx/_search?pretty"

echo
echo -e "${YELLOW}正在测试OpenSearch索引条件查询API...${NC}"

echo "curl -ku '$OS_USER:$OS_PASS' -H \"Content-Type:application/json\" -XGET \"http://$OS_HOST:$OS_PORT/10001-1-biaozhizyx/_search?pretty\" -d '{\"query\":{\"term\":{\"question\":\"标准问题batch1\"}}}'"

curl -ku "$OS_USER:$OS_PASS" -H "Content-Type:application/json" -XGET "http://$OS_HOST:$OS_PORT/10001-1-biaozhizyx/_search?pretty" -d '{"query":{"term":{"question":"标准问题batch1"}}}'

echo
echo -e "${YELLOW}正在测试查看所有索引API...${NC}"

echo "curl -ku '$OS_USER:$OS_PASS' -XGET \"http://$OS_HOST:$OS_PORT/_cat/indices?v\" | grep \"10001-1-biaozhizyx\""

curl -ku "$OS_USER:$OS_PASS" -XGET "http://$OS_HOST:$OS_PORT/_cat/indices?v" | grep "10001-1-biaozhizyx"

echo
echo -e "${GREEN}=== 查询索引API测试结束 ===${NC}"


