#!/bin/bash

# 设置ES用户名和密码
ES_USERNAME="elastic"
ES_PASSWORD="Elastic20250417@#"

# 创建必要目录结构
mkdir -p /data/filebeat/{config,data,logs}
mkdir -p /data/nginx/log
mkdir -p /data/embedding/embedding-service-bge-m3/logs
mkdir -p /data/embedding/embedding-service-bge-large-zh/logs
mkdir -p /data/embedding/bge-reranker-v2-m3/logs

# 停止并删除旧的filebeat容器
echo "停止并删除旧的filebeat容器"
docker rm -f filebeat 2>/dev/null || true

# 清理filebeat数据目录（确保版本兼容性）
echo "清理filebeat数据目录"
rm -rf /data/filebeat/data/*
rm -rf /data/filebeat/logs/*

# 设置目录权限
echo "设置目录权限"
chmod -R 777 /data/filebeat/data
chmod -R 777 /data/filebeat/logs
chmod -R 755 /data/filebeat/config
chmod -R 755 /data/nginx/log
chmod -R 755 /data/hegao/ai-doc-poc/logs/ai-doc
chmod -R 755 /data/embedding/embedding-service-bge-m3/logs
chmod -R 755 /data/embedding/embedding-service-bge-large-zh/logs
chmod -R 755 /data/embedding/bge-reranker-v2-m3/logs

# 拉取Filebeat镜像 - 使用与ES相同的主版本
docker pull docker.elastic.co/beats/filebeat:7.17.14

# 创建Filebeat配置文件 - 使用容器内的路径，而不是主机路径
cat > /data/filebeat/config/filebeat.yml <<EOF
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /data/nginx/log/*.log
    - /data/nginx/log/*.log.*
  fields:
    type: nginx
  fields_under_root: true
  tags: ["nginx"]
  
# 向量模型日志配置
- type: log
  enabled: true
  paths:
    - /data/embedding/embedding-service-bge-m3/logs/embedding_log_*
  fields:
    type: bge-m3
  fields_under_root: true
  tags: ["embedding", "bge-m3"]
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
  processors:
    - decode_json_fields:
        fields: ["message"]
        process_array: false
        max_depth: 3
        target: ""
        overwrite_keys: true
        when:
          contains:
            message: "{"

- type: log
  enabled: true
  paths:
    - /data/embedding/embedding-service-bge-large-zh/logs/embedding_log_*
  fields:
    type: bge-large-zh
  fields_under_root: true
  tags: ["embedding", "bge-large-zh"]
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
  processors:
    - decode_json_fields:
        fields: ["message"]
        process_array: false
        max_depth: 3
        target: ""
        overwrite_keys: true
        when:
          contains:
            message: "{"

- type: log
  enabled: true
  paths:
    - /data/embedding/bge-reranker-v2-m3/logs/rerank_log_*
  fields:
    type: bge-reranker
  fields_under_root: true
  tags: ["embedding", "bge-reranker"]
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
  processors:
    - decode_json_fields:
        fields: ["message"]
        process_array: false
        max_depth: 3
        target: ""
        overwrite_keys: true
        when:
          contains:
            message: "{"

# 增加debug输出
logging.level: debug
logging.to_files: true
logging.files:
  path: /usr/share/filebeat/logs
  name: filebeat
  keepfiles: 7

# 禁用索引模板
setup.template.enabled: false

# 配置Elasticsearch输出
output.elasticsearch:
  hosts: ["elasticsearch:9200"]  # 使用容器名称连接
  username: "${ES_USERNAME}"
  password: "${ES_PASSWORD}"
  indices:
    - index: "nginx-%{+yyyy}"
      when.equals:
        type: "nginx"
    - index: "bge-m3-%{+yyyy}"
      when.equals:
        type: "bge-m3"
    - index: "bge-large-zh-%{+yyyy}"
      when.equals:
        type: "bge-large-zh"
    - index: "bge-reranker-%{+yyyy}"
      when.equals:
        type: "bge-reranker"
  ssl.enabled: false

# 开启监控
monitoring:
  enabled: true
  elasticsearch:
    username: "${ES_USERNAME}"
    password: "${ES_PASSWORD}"

# 禁用自动索引生命周期管理
setup.ilm.enabled: false
EOF


# 在启动容器前测试连接ES
echo "测试ES连接..."
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/_cat/indices?v" || echo "⚠️ 无法连接到Elasticsearch，请检查连接设置"

# 启动Filebeat容器 - 连接到elastic网络并使用root用户
docker run -d \
  --name filebeat \
  --user root \
  --network elasticsearch_kibana_elastic \
  -e "ES_USERNAME=${ES_USERNAME}" \
  -e "ES_PASSWORD=${ES_PASSWORD}" \
  -v /data/filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro \
  -v /data/filebeat/data:/usr/share/filebeat/data \
  -v /data/filebeat/logs:/usr/share/filebeat/logs \
  -v /data/nginx/log:/data/nginx/log:ro \
  -v /data/hegao/ai-doc-poc/logs/ai-doc:/data/ai-doc-poc/logs:ro \
  -v /data/embedding/embedding-service-bge-m3/logs:/data/embedding/embedding-service-bge-m3/logs:ro \
  -v /data/embedding/embedding-service-bge-large-zh/logs:/data/embedding/embedding-service-bge-large-zh/logs:ro \
  -v /data/embedding/bge-reranker-v2-m3/logs:/data/embedding/bge-reranker-v2-m3/logs:ro \
  --restart always \
  docker.elastic.co/beats/filebeat:7.17.14 \
  filebeat -e --strict.perms=false

echo "等待Filebeat处理日志 (10秒)..."
sleep 10

# 检查Filebeat日志
echo "=========================================Filebeat日志 Nginx (最近20行)====================================="
docker logs filebeat --tail 20  | grep -E 'nginx'

echo "=========================================Filebeat日志 BGE-M3 (最近20行)==============================="
docker logs filebeat --tail 20  | grep -E 'bge-m3'

echo "Filebeat日志 BGE-Large-ZH (最近20行)=========================================="
docker logs filebeat --tail 20  | grep -E 'bge-large-zh'

echo "=========================================Filebeat日志 BGE-Reranker (最近20行)==============================="
docker logs filebeat --tail 20  | grep -E 'bge-reranker'
echo "=========================================================================================================="

# 检查索引是否创建
echo "##############################检查Nginx ES索引15分钟前的:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/_cat/indices?v" | grep -E 'nginx'

echo "##############################检查BGE-M3 ES索引:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/_cat/indices?v" | grep -E 'bge-m3'

echo "##############################检查BGE-Large-ZH ES索引:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/_cat/indices?v" | grep -E 'bge-large-zh'

echo "##############################检查BGE-Reranker ES索引:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/_cat/indices?v" | grep -E 'bge-reranker'

# 查询Nginx 15分钟前的索引
echo "##############################查询Nginx 15分钟前的数据:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/_cat/indices?v" | grep -E 'nginx' | awk '{print $3}' | while read index; do
    curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/${index}/_search?q=*&sort=@timestamp:desc&size=1" | grep -E 'nginx'
done  

# 查询BGE-M3 15分钟前的索引
echo "-----------------------------------查询BGE-M3 15分钟前的数据:-----------------------------------"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/_cat/indices?v" | grep -E 'bge-m3' | awk '{print $3}' | while read index; do
    curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/${index}/_search?q=*&sort=@timestamp:desc&size=1" | grep -E 'bge-m3'
done

# 查询BGE-Large-ZH 15分钟前的索引
echo "-----------------------------------查询BGE-Large-ZH 15分钟前的数据:-----------------------------------"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/_cat/indices?v" | grep -E 'bge-large-zh' | awk '{print $3}' | while read index; do
    curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/${index}/_search?q=*&sort=@timestamp:desc&size=1" | grep -E 'bge-large-zh'
done

# 查询BGE-Reranker 15分钟前的索引
echo "-----------------------------------查询BGE-Reranker 15分钟前的数据:-----------------------------------"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/_cat/indices?v" | grep -E 'bge-reranker' | awk '{print $3}' | while read index; do
    curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://localhost:9200/${index}/_search?q=*&sort=@timestamp:desc&size=1" | grep -E 'bge-reranker'
done





echo "Filebeat已启动，正在收集日志并发送到Elasticsearch"
echo ""
echo "重要：正确的路径映射关系"
echo "- 主机路径 /data/nginx/log 映射到容器内 /data/nginx/log"
echo "- 主机路径 /data/embedding/*/logs 映射到容器内 /data/embedding/*/logs"
echo ""
echo "如有问题，请尝试运行以下命令进一步调试:"
echo "  docker logs -f filebeat"
echo "  curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET 'http://localhost:9200/_cat/indices?v'"