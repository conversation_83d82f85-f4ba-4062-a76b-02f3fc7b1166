# 接口验证
echo "接口验证: curl -X POST http://localhost:8080/ai-compose-poc/api/v1/compose"
curl -X POST http://localhost:8080/ai-compose-poc/api/v1/compose

#开始时间
start_time=$(date +%s)

curl -v -X POST http://127.0.0.1:8080/ai-compose-poc/api/v1/compose \
-H 'Content-Type: application/json' \
-d '{
    "composeUnitId": "glmwriter",
    "text": "请以《中国移动公司关于开展成本管理专项活动通知》为主题生成一篇通知类型的公文。字数要求100字以上"
}'
#结束时间
end_time=$(date +%s)
#计算执行时间
duration=$((end_time - start_time))
echo -e "${GREEN}耗时: ${duration} 秒${NC}"
