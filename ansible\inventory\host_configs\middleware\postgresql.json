{"middleware_name": "postgresql", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "m.daocloud.io/docker.io/postgres:12.18", "host_port": "5432", "container_port": "5432", "username": "admin", "password": "PgSQL@2025", "database": "postgres", "host_data_dir": "{{base_dir}}/postgresql/data", "host_logs_dir": "{{base_dir}}/postgresql/logs", "host_config_dir": "{{base_dir}}/postgresql/config", "container_data_dir": "/var/lib/postgresql/data", "container_logs_dir": "/var/log/postgresql", "container_config_dir": "/etc/postgresql", "restart_script": "{{base_dir}}/postgresql/restart.sh", "test_script": "{{base_dir}}/postgresql/test_curl.sh", "env_vars": [{"name": "POSTGRES_USER", "value": "admin"}, {"name": "POSTGRES_PASSWORD", "value": "PgSQL@2025"}, {"name": "POSTGRES_DB", "value": "postgres"}], "test_commands": ["docker exec -it postgresql psql -U admin -d postgres -c \"SELECT version();\""]}