#!/bin/bash
# apply_service.sh - 用于部署单个K8s服务的工具函数

# 获取当前目录的绝对路径
CURRENT_DIR=$(cd "$(dirname "$0")" && pwd)
echo "当前脚本目录: ${CURRENT_DIR}"

function create_storage_class() {
    YML_FILE_PATH=$1
    NAMESPACE=${2:-"oa-llm"}
    CONFIG_FILE_PATH=$3
    STORAGE_CLASS=$(jq -r '.["storage-config"]["storage-class"]' ${CONFIG_FILE_PATH})
    ENABLED=$(jq -r '.["storage-config"]["enabled"]' ${CONFIG_FILE_PATH})
    NFS_SERVER=$(jq -r '.["storage-config"]["server"]' ${CONFIG_FILE_PATH})
    NFS_PATH=$(jq -r '.["storage-config"]["path"]' ${CONFIG_FILE_PATH})

    if [ "$ENABLED" = "false" ]; then
        echo "storage-class未启用"
        return 0
    fi
         # 探测NFS服务器
    echo "探测NFS服务器 $NFS_SERVER..."
    
    # 检查NFS服务器是否可达
    echo "ping -c 1 $NFS_SERVER"
    if ! ping -c 1 $NFS_SERVER &>/dev/null; then
        echo "错误: NFS服务器 $NFS_SERVER 不可达"
        return 1
    fi
    
    # 检查NFS服务是否运行
    echo "rpcinfo -p $NFS_SERVER"
    if ! rpcinfo -p $NFS_SERVER &>/dev/null; then
        echo "错误: NFS服务器 $NFS_SERVER 的RPC服务未运行"
        return 1
    fi
    
    # 检查NFS导出
    echo "showmount -e $NFS_SERVER"
    if ! showmount -e $NFS_SERVER &>/dev/null; then
        echo "错误: 无法获取NFS服务器 $NFS_SERVER 的导出列表"
        return 1
    fi
    
    # 检查NFS路径是否已导出
    echo "showmount -e $NFS_SERVER | grep -q $NFS_PATH"
    if ! showmount -e $NFS_SERVER | grep -q "$NFS_PATH"; then
        echo "错误: NFS路径 $NFS_PATH 未在服务器 $NFS_SERVER 上导出"
        return 1
    fi
    
    echo "创建storage-class[$STORAGE_CLASS]"
    # 判断storage-class是否存在
    echo "kubectl get storageclass $STORAGE_CLASS -n $NAMESPACE"
    if ! kubectl get storageclass $STORAGE_CLASS -n $NAMESPACE &>/dev/null; then
        echo "kubectl apply -f ${YML_FILE_PATH} -n ${NAMESPACE}"
        kubectl apply -f ${YML_FILE_PATH} -n ${NAMESPACE}
    else
        echo "storage-class[$STORAGE_CLASS]已存在"
    fi
    echo "storage-class[$STORAGE_CLASS]部署完成"
}
function create_namespace() {
    NAMESPACE=${1:-"oa-llm"}
    CONFIG_FILE_PATH=$2
    if [ ! -f "$CONFIG_FILE_PATH" ]; then
        echo "错误: 配置文件不存在 ${CONFIG_FILE_PATH}"
        return 1
    fi
    echo "创建命名空间: $NAMESPACE"
    if ! kubectl get namespace $NAMESPACE &>/dev/null; then
        kubectl create namespace $NAMESPACE
    fi
    # 创建secret
   echo "创建imagepullsecret"
    # 从config.json文件中获取target-image-repo-url
   TARGET_IMAGE_REPO_URL=$(jq -r '.["base-config"]["target-image-repo-url"]' ${CONFIG_FILE_PATH})
    # 从config.json文件中获取target-image-repo-user
   TARGET_IMAGE_REPO_USER=$(jq -r '.["base-config"]["target-image-repo-user"]' ${CONFIG_FILE_PATH})
    # 从config.json文件中获取target-image-repo-password
   TARGET_IMAGE_REPO_PASSWORD=$(jq -r '.["base-config"]["target-image-repo-password"]' ${CONFIG_FILE_PATH})
    # 查看是否存在imagepullsecret
   echo "kubectl get secret oa-llm-imagepullsecret -n oa-llm"
   kubectl get secret oa-llm-imagepullsecret -n oa-llm
   if [ $? -ne 0 ]; then
        echo "oa-llm-imagepullsecret不存在"
        echo "kubectl create secret docker-registry oa-llm-imagepullsecret --docker-server=${TARGET_IMAGE_REPO_URL} --docker-username=${TARGET_IMAGE_REPO_USER} --docker-password=${TARGET_IMAGE_REPO_PASSWORD}  -n oa-llm"
        kubectl create secret docker-registry oa-llm-imagepullsecret --docker-server=${TARGET_IMAGE_REPO_URL} --docker-username=${TARGET_IMAGE_REPO_USER} --docker-password=${TARGET_IMAGE_REPO_PASSWORD}  -n oa-llm
   else
        echo "oa-llm-imagepullsecret已存在，不创建"
   fi
}

# 部署单个服务函数
function apply_service() {
    YML_FILE_PATH=$1
    SERVICE_NAME=$2
    # 测试脚本路径
    TEST_SCRIPT_PATH=$3
    # 命名空间，默认为oa-llm
    NAMESPACE=${4:-"oa-llm"}
    # 是否使用彩色输出，默认为false
    USE_COLOR=${5:-"false"}
    # 工具目录路径，默认为当前目录
    TOOLS_DIR=${6:-"${CURRENT_DIR}"}
    
    echo "使用工具目录: ${TOOLS_DIR}"
    
    # 加载其他工具函数
    echo "从以下路径加载工具函数:"
    echo "${TOOLS_DIR}/utils.sh"
    echo "${TOOLS_DIR}/check_k8s_resource.sh"
    echo "${TOOLS_DIR}/show_resource_status.sh"
    echo "${TOOLS_DIR}/wait_for_service.sh"
    echo "${TOOLS_DIR}/handle_service_timeout.sh"
    echo "${TOOLS_DIR}/view_pod_logs.sh"
    echo "${TOOLS_DIR}/enter_pod_container.sh"
    
    # 加载工具函数
    source "${TOOLS_DIR}/utils.sh"
    source "${TOOLS_DIR}/check_k8s_resource.sh"
    source "${TOOLS_DIR}/show_resource_status.sh"
    source "${TOOLS_DIR}/wait_for_service.sh"
    source "${TOOLS_DIR}/handle_service_timeout.sh"
    source "${TOOLS_DIR}/view_pod_logs.sh"
    source "${TOOLS_DIR}/enter_pod_container.sh"
    
    # 颜色定义
    if [ "$USE_COLOR" = "true" ]; then
        GREEN="\033[0;32m"
        BLUE="\033[0;34m"
        RED="\033[0;31m"
        YELLOW="\033[1;33m"
        NC="\033[0m" # 恢复默认颜色
        echo -e "${BLUE}正在部署服务：$SERVICE_NAME，文件路径：$YML_FILE_PATH${NC}"
    else
        echo "正在部署服务：$SERVICE_NAME，文件路径：$YML_FILE_PATH"
    fi
    
    # 询问是否部署，如果部署，则部署，否则退出
    read -p "是否确定部署服务:$SERVICE_NAME？(y/n): " APPLY_SERVICE 
    if [ "$APPLY_SERVICE" = "n" ]; then
        if [ "$USE_COLOR" = "true" ]; then
            echo -e "${YELLOW}退出部署${NC}"
        else
            echo "退出部署"
        fi
        return
    fi
    
    # 检查服务是否已存在
    if [ "$USE_COLOR" = "true" ]; then
        echo -e "${YELLOW}检查服务是否已存在...${NC}"
    else
        echo "检查服务是否已存在..."
    fi
    check_k8s_resource $SERVICE_NAME "$NAMESPACE"
    
    # 如果资源已存在，显示当前状态并询问是否重新安装
    if [ "$FOUND_RESOURCE" = "true" ]; then
        # 显示资源状态
        show_resource_status $SERVICE_NAME $RESOURCE_TYPE "$NAMESPACE"
        
        read -p "服务已存在，是否重新安装？(y/n): " REINSTALL
        if [ "$REINSTALL" = "y" ]; then
            if [ "$USE_COLOR" = "true" ]; then
                echo -e "${YELLOW}kubectl delete $RESOURCE_TYPE $SERVICE_NAME -n $NAMESPACE${NC}"
            else
                echo "kubectl delete $RESOURCE_TYPE $SERVICE_NAME -n $NAMESPACE"
            fi
            kubectl delete $RESOURCE_TYPE $SERVICE_NAME -n $NAMESPACE
        else
            if [ "$USE_COLOR" = "true" ]; then
                echo -e "${YELLOW}跳过部署${NC}"
            else
                echo "跳过部署"
            fi
            return
        fi
    fi
    
    if [ "$USE_COLOR" = "true" ]; then
        echo -e "${GREEN}kubectl apply -f ${YML_FILE_PATH}${NC}"
    else
        echo "kubectl apply -f ${YML_FILE_PATH}"
    fi
    kubectl apply -f ${YML_FILE_PATH}
    
    # 获取资源类型
    resource_type=""
    if kubectl get deployment $SERVICE_NAME -n $NAMESPACE &>/dev/null; then
        resource_type="deployment"
    elif kubectl get statefulset $SERVICE_NAME -n $NAMESPACE &>/dev/null; then
        resource_type="statefulset"
    elif kubectl get daemonset $SERVICE_NAME -n $NAMESPACE &>/dev/null; then
        resource_type="daemonset"
    fi
    
    # 等待服务启动
    if [[ -n "$resource_type" ]]; then
        # 等待服务启动（20秒超时）
        wait_for_service $SERVICE_NAME $resource_type "$NAMESPACE" 20
        
        # 如果服务未启动，处理超时
        if [ "$IS_RUNNING" = "false" ]; then
            handle_service_timeout $SERVICE_NAME $resource_type "$NAMESPACE"
        fi
    fi
    
    # 查看是否应用成功
    if [ "$USE_COLOR" = "true" ]; then
        echo -e "${YELLOW}检查应用最终状态...${NC}"
        echo -e "${BLUE}kubectl get pods -n $NAMESPACE | grep $SERVICE_NAME${NC}"
    else
        echo "检查应用最终状态..."
        echo "kubectl get pods -n $NAMESPACE | grep $SERVICE_NAME"
    fi
    kubectl get pods -n $NAMESPACE | grep $SERVICE_NAME
    if [ $? -ne 0 ]; then
        if [ "$USE_COLOR" = "true" ]; then
            echo -e "${RED}警告: 未找到相关Pod，服务可能未成功启动或Pod名称不包含服务名${NC}"
            echo -e "${YELLOW}尝试查看所有Pod:${NC}"
        else
            echo "警告: 未找到相关Pod，服务可能未成功启动或Pod名称不包含服务名"
            echo "尝试查看所有Pod:"
        fi
        kubectl get pods -n $NAMESPACE
    fi
    
    # 查看启动日志
    read -p "是否查看启动日志？(y/n): " VIEW_LOGS
    if [ "$VIEW_LOGS" = "y" ]; then
        # 获取相关的pod名称
        POD_NAME=$(kubectl get pods -n $NAMESPACE | grep $SERVICE_NAME | awk '{print $1}' | head -1)
        if [ -n "$POD_NAME" ]; then
            view_pod_logs "$POD_NAME" "$NAMESPACE"
        else
            if [ "$USE_COLOR" = "true" ]; then
                echo -e "${RED}未找到相关Pod，无法查看日志${NC}"
            else
                echo "未找到相关Pod，无法查看日志"
            fi
        fi
    fi
    
    # 询问是否进入容器
    read -p "是否进入容器？(y/n): " ENTER_CONTAINER
    if [ "$ENTER_CONTAINER" = "y" ]; then
        POD_NAME=$(kubectl get pods -n $NAMESPACE | grep $SERVICE_NAME | awk '{print $1}' | head -1)
        if [ -n "$POD_NAME" ]; then
            enter_pod_container "$POD_NAME" "$NAMESPACE"
        else
            if [ "$USE_COLOR" = "true" ]; then
                echo -e "${RED}未找到相关Pod，无法进入容器${NC}"
            else
                echo "未找到相关Pod，无法进入容器"
            fi
        fi
    fi
    
    # 询问是否测试服务
    read -p "是否测试服务？(y/n): " TEST_SERVICE
    if [ "$TEST_SERVICE" = "y" ]; then
        if [ -f "$TEST_SCRIPT_PATH" ]; then
            if [ "$USE_COLOR" = "true" ]; then
                echo -e "${YELLOW}测试服务：$SERVICE_NAME${NC}"
                echo -e "${BLUE}sh ${TEST_SCRIPT_PATH}${NC}"
            else
                echo "测试服务：$SERVICE_NAME"
                echo "sh ${TEST_SCRIPT_PATH}"
            fi
            sh ${TEST_SCRIPT_PATH}
            echo "测试完成，按Enter键继续..."
            read
        else
            if [ "$USE_COLOR" = "true" ]; then
                echo -e "${RED}警告: 测试脚本 ${TEST_SCRIPT_PATH} 不存在${NC}"
            else
                echo "警告: 测试脚本 ${TEST_SCRIPT_PATH} 不存在"
            fi
            echo "按Enter键继续..."
            read
        fi
    fi
    
    if [ "$USE_COLOR" = "true" ]; then
        echo -e "${GREEN}服务 ${SERVICE_NAME} 部署完成${NC}"
    else
        echo "服务 ${SERVICE_NAME} 部署完成"
    fi
}

# 如果是直接运行此脚本而非导入函数，则提供帮助信息
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "此脚本提供K8s服务部署功能，不应直接运行。"
    echo "请在其他脚本中导入此文件使用apply_service函数。"
    echo "用法示例: "
    echo "  source ./utils/apply_service.sh"
    echo "  apply_service \"路径/到/yml文件\" \"服务名称\" \"测试脚本路径\" \"命名空间\" \"是否使用彩色输出\" \"工具目录路径\""
fi 