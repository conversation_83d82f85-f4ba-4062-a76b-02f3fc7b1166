version: '3'

services:
  opensearch-dashboard:
    image: public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0
    container_name: opensearch-dashboard
    ports:
      - "5601:5601"
    volumes:
      - ${BASE_DIR:-/data/opensearch-dashboard}/logs:/usr/share/opensearch-dashboards/logs
    environment:
      - OPENSEARCH_HOSTS=https://opensearch:9200
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always
    depends_on:
      - opensearch

  # 以下是opensearch服务的配置，保持与opensearch本体服务一致
  opensearch:
    image: public.ecr.aws/opensearchproject/opensearch:2.9.0
    container_name: opensearch
    ports:
      - "9200:9200"
      - "9600:9600"
    volumes:
      # 使用docker卷挂载opensearch的数据和日志目录  
      - opensearch-data:/usr/share/opensearch/data
      - ${BASE_DIR:-/data/opensearch}/logs:/usr/share/opensearch/logs
    environment:
      - discovery.type=single-node
      - OPENSEAR<PERSON>_JVM_HEAP=2g
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=QmffyH1zxSWE5Nke
      - TZ=Asia/Shanghai
    ulimits:
      memlock:
        soft: -1
        hard: -1
    networks:
      - jiutianwensi-network
    restart: always
# 创建卷
volumes:
  opensearch-data:
networks:
  jiutianwensi-network:
    driver: bridge 
