#!/bin/bash
echo "===== 重启Filebeat并重置状态 ====="

# 停止并删除Filebeat容器
echo "1. 停止并删除Filebeat容器"
docker rm -f filebeat 2>/dev/null || true

# 清除Filebeat状态
echo "2. 清除Filebeat状态数据"
rm -rf /data/filebeat/data/*
rm -rf /data/filebeat/logs/*

# 生成新测试日志文件
echo "3. 生成新测试日志文件"
for logdir in "/data/znwd/rbac/logs" "/data/znwd/qa/logs" "/data/znwd/aiknowledge-controller/logs"; do
  if [ ! -d "$logdir" ]; then
    echo "创建目录 $logdir"
    mkdir -p $logdir
  fi
done

# 生成不同格式的测试日志
cat > /data/znwd/rbac/logs/rbac.log <<EOL
2025-04-17 05:30:00 [INFO] RBAC服务正常启动 - $(date +%s)
2025-04-17 05:30:01 [DEBUG] RBAC服务加载配置完成 - $(date +%s)
2025-04-17 05:30:02 [INFO] RBAC服务连接数据库成功 - $(date +%s)
EOL

cat > /data/znwd/qa/logs/spring.log <<EOL
2025-04-17 05:30:00 [INFO] QA服务正常启动 - $(date +%s)
2025-04-17 05:30:01 [DEBUG] QA服务加载配置完成 - $(date +%s)
2025-04-17 05:30:02 [INFO] QA服务连接数据库成功 - $(date +%s)
EOL

cat > /data/znwd/aiknowledge-controller/logs/application.log <<EOL
2025-04-17 05:30:00 [INFO] AI知识服务正常启动 - $(date +%s)
2025-04-17 05:30:01 [DEBUG] AI知识服务加载配置完成 - $(date +%s)
2025-04-17 05:30:02 [INFO] AI知识服务连接数据库成功 - $(date +%s)
EOL

# 同时生成JSON格式日志用于测试decode_json_fields处理器
cat > /data/znwd/rbac/logs/rbac_json.log <<EOL
{"@timestamp":"2025-04-17T05:31:00.000Z","level":"INFO","message":"这是一条JSON格式日志","service":"rbac","user_id":10001}
{"@timestamp":"2025-04-17T05:31:01.000Z","level":"DEBUG","message":"JSON格式日志字段测试","service":"rbac","user_id":10002}
{"@timestamp":"2025-04-17T05:31:02.000Z","level":"INFO","message":"JSON格式日志最后一条","service":"rbac","user_id":10003}
EOL

# 设置适当的权限
echo "4. 设置适当的权限"
chmod -R 755 /data/znwd
find /data/znwd -name "*.log" -exec chmod 644 {} \;

# 创建OpenSearch索引模板
echo "5. 创建OpenSearch索引模板"
curl -k -u admin:admin -X PUT "https://10.1.4.16:9200/_template/logs_template" -H 'Content-Type: application/json' -d'
{
  "index_patterns": ["rbac-index-*", "qa-index-*", "aiknowledge-index-*"],
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 0
  },
  "mappings": {
    "properties": {
      "@timestamp": { 
        "type": "date"
      },
      "message": {
        "type": "text"
      },
      "fields": {
        "properties": {
          "type": { "type": "keyword" },
          "index": { "type": "keyword" }
        }
      }
    }
  }
}'

# 重启Filebeat容器
echo "6. 启动Filebeat容器"
IMAGE_VERSION=8.18.0
IMAGE_NAME=docker.elastic.co/beats/filebeat

docker run -d \
  --name=filebeat \
  --user=root \
  --volume="/data/filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro" \
  --volume="/data/filebeat/data:/usr/share/filebeat/data:rw" \
  --volume="/data/filebeat/logs:/var/log/filebeat:rw" \
  --volume="/data/znwd/rbac/logs:/data/znwd/rbac/logs:ro" \
  --volume="/data/znwd/qa/logs:/data/znwd/qa/logs:ro" \
  --volume="/data/znwd/aiknowledge-controller/logs:/data/znwd/aiknowledge-controller/logs:ro" \
  $IMAGE_NAME:$IMAGE_VERSION \
  filebeat -e --strict.perms=false

# 等待Filebeat处理日志
echo "7. 等待10秒，让Filebeat处理日志..."
sleep 10

# 查看Filebeat日志
echo "8. Filebeat日志："
docker logs filebeat | tail -n 20

# 检查OpenSearch索引
echo "9. 查看OpenSearch索引："
today=$(date +"%Y-%m-%d")
curl -k -u admin:admin -X GET "https://10.1.4.16:9200/_cat/indices?v"

# 查看索引文档数
echo "10. 查看文档数："
for index in "rbac-index-$today" "qa-index-$today" "aiknowledge-index-$today"; do
  echo "索引 $index 的文档数："
  curl -k -s -u admin:admin -X GET "https://10.1.4.16:9200/$index/_count" | grep -o '"count":[0-9]*' || echo "索引不存在"
done

echo "===== 重启完成 =====" 