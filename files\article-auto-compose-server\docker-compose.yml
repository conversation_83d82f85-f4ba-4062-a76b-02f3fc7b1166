version: '3'

services:
  article-auto-compose-server:
    image: ai-writer/article-auto-compose-server:0.0.1-SNAPSHOT
    container_name: article-auto-compose-server
    ports:
      - "${PORT:-8094}:8080"
    volumes:
      - ${BASE_DIR:-/data/ai-writer}/article-auto-compose-server/config:/app/config
      - ${BASE_DIR:-/data/ai-writer}/article-auto-compose-server/logs:/app/logs
      - ${BASE_DIR:-/data/ai-writer}/article-auto-compose-server/shells:/app/shells
      - ${BASE_DIR:-/data/ai-writer}/article-auto-compose-server/app.jar:/app/app.jar
    environment:
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always
  
networks:
  jiutianwensi-network:
    external: true 