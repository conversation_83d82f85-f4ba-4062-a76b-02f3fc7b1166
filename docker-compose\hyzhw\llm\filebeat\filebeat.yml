filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /data/deepseek70b/init-logs/*.log
  fields:
    type: deepseek70b
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s
  harvester_limit: 5

# 使用Elasticsearch输出
output.elasticsearch:
  hosts: ["http://elasticsearch:9200"]
  username: "elastic"
  password: "elastic@123"
  indices:
    - index: "deepseek70b-%{+yyyy}"
      when.contains:
        fields.type: "deepseek70b"
  # 增加重试和错误处理
  bulk_max_size: 50
  worker: 1
  retry:
    max_retries: 3
    backoff:
      init: 1s
      max: 60s
      factor: 2.0

# 禁用索引模板
setup.template.enabled: false

# 禁用自动索引生命周期管理
setup.ilm.enabled: false

# Kibana配置
setup.kibana.host: "http://kibana:5601"

# 日志配置
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644