<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <title>文档预览</title>
  <script src="marked.min.js"></script>
  <style>
    body { font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif; max-width: 900px; margin: 0 auto; padding: 2em; }
    pre, code { background: #f6f8fa; }
    img { max-width: 100%; }
  </style>
</head>
<body>
  <div id="content">加载中...</div>
  <script>
    fetch('README.md')
      .then(r => r.text())
      .then(md => { document.getElementById('content').innerHTML = marked.parse(md); });
  </script>
</body>
</html>