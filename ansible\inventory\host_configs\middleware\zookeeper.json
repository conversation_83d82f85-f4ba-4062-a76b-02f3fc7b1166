{"middleware_name": "zookeeper", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "m.daocloud.io/docker.io/zookeeper:3.9.3", "host_port": "2181", "container_port": "2181", "host_data_dir": "{{base_dir}}/zookeeper/data", "container_data_dir": "/data", "restart_script": "{{base_dir}}/zookeeper/restart.sh", "test_script": "{{base_dir}}/zookeeper/test_curl.sh", "env_vars": [{"name": "TZ", "value": "Asia/Shanghai"}], "test_commands": ["docker exec -it zookeeper zkCli.sh -server localhost:2181 create /test 'hello'", "docker exec -it zookeeper zkCli.sh -server localhost:2181 ls /"]}