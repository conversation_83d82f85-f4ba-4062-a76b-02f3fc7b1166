#!/bin/bash
# 代理端口
set -e  # 遇到错误立即退出

# 获取脚本所在目录的绝对路径
CURRENT_DIR=$(cd "$(dirname "$0")" && pwd)
PARENT_DIR=$(dirname "${CURRENT_DIR}")

# 统一定义路径
CONFIG_FILE="${CURRENT_DIR}/config.json"
YML_DIR="${PARENT_DIR}/yml"  # 根据实际目录结构调整

# 检查依赖
command -v jq >/dev/null 2>&1 || { echo "错误: 需要安装jq"; exit 1; }
command -v kubectl >/dev/null 2>&1 || { echo "错误: 需要安装kubectl"; exit 1; }

# 读取配置
[ ! -f "${CONFIG_FILE}" ] && { echo "错误: 配置文件不存在 ${CONFIG_FILE}"; exit 1; }
REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})

# 获取本机IP (更可靠方式)
LOCAL_IP=$(hostname -I | awk '{print $1}')
echo "使用本机IP: ${LOCAL_IP}"

# 启动的端口转发进程ID列表
PIDS=()

# 循环处理每个服务
for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
    # 获取配置
    SERVICE_NAME=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
    YML_FILE_NAME=$(jq -r ".[\"replace-config\"][$i][\"yml-file-name\"]" ${CONFIG_FILE})
    TYPE=$(jq -r ".[\"replace-config\"][$i][\"type\"]" ${CONFIG_FILE})
    PROXY_PORT=$(jq -r ".[\"replace-config\"][$i][\"proxy-port\"]" ${CONFIG_FILE})
    
    # 检查必要参数
    if [ "${PROXY_PORT}" = "null" ] || [ -z "${PROXY_PORT}" ]; then
        echo "跳过 ${SERVICE_NAME}: 未配置proxy-port"
        continue
    fi
    
    # 读取YAML文件
    YML_FILE_PATH="${YML_DIR}/template/${TYPE}/${YML_FILE_NAME}"
    if [ ! -f "${YML_FILE_PATH}" ]; then
        echo "警告: YAML文件不存在 ${YML_FILE_PATH}"
        continue
    fi
    
    # 获取targetPort
    TARGET_PORT=$(grep -A5 "ports:" ${YML_FILE_PATH} | grep "targetPort:" | head -1 | awk '{print $2}')
    if [ -z "${TARGET_PORT}" ]; then
        echo "警告: ${SERVICE_NAME}服务的${YML_FILE_NAME}文件中没有找到targetPort"
        continue
    fi
    
    # 检查端口是否已被占用
    if netstat -tuln | grep -q ":${PROXY_PORT}"; then
        echo "警告: 端口 ${PROXY_PORT} 已被占用，跳过 ${SERVICE_NAME}"
        continue
    fi
    
    # 后台启动端口转发
    echo "将内部地址映射到K8s集群外: ${SERVICE_NAME}:${TARGET_PORT} -> ${LOCAL_IP}:${PROXY_PORT} "
    echo "kubectl port-forward svc/${SERVICE_NAME} ${PROXY_PORT}:${TARGET_PORT} -n oa-llm --address=0.0.0.0"
    kubectl port-forward svc/${SERVICE_NAME} ${PROXY_PORT}:${TARGET_PORT} -n oa-llm --address=0.0.0.0 &
    PIDS+=($!)
    echo "启动端口转发进程: PID ${PIDS[-1]}"
    
    # 稍等片刻，避免同时启动多个进程导致问题
    sleep 1
done

echo "所有端口转发已启动，按Ctrl+C终止"
echo "运行中的进程: ${PIDS[*]}"

# 等待所有后台进程
trap "kill ${PIDS[*]} 2>/dev/null" EXIT
wait


