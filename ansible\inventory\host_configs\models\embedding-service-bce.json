{"model_name": "embedding-service-bce", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "embedding/embedding-service-bce:x86-v1", "host_port": "8100", "container_port": "8080", "api_endpoint": "/Rag/BceEmbeddingService", "host_logs_dir": "{{base_dir}}/embedding/embedding-service-bce/logs", "host_shells_dir": "{{base_dir}}/embedding/embedding-service-bce/shells", "container_logs_dir": "/app/embedding_service/log", "container_shells_dir": "/app/shells", "restart_script": "{{base_dir}}/embedding/embedding-service-bce/restart.sh", "test_script": "{{base_dir}}/embedding/embedding-service-bce/test_curl.sh", "env_vars": [{"name": "TZ", "value": "Asia/Shanghai"}], "test_commands": ["curl -X POST \"http://localhost:8100/Rag/BceEmbeddingService\" -H 'Content-Type: application/json' -d '{\"text\":\"这是一段测试文本，用来验证embedding服务是否正常工作\"}'", "curl -X GET \"http://localhost:8100/health\""]}