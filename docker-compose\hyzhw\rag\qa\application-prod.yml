spring:
  datasource:
    url: **********************************************************************************************************************************
    username: admin
    password: PgSQL@2025

  data:
    redis:
      host: ********
      port: 6379
      password: admin123
      database: 2

ai-chat:
  url: http://aiknowledge-controller:8080
  appId: afsoprmv
logging:
  file:
    path: /app/logs
  pattern:
    console:
      console-pattern: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
      file-pattern: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"