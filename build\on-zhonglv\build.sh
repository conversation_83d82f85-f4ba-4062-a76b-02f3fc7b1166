# 在81主机上执行
# 创建/data/build目录
mkdir -p /data/test

# 创建/data/build/apps目录
mkdir -p /data/test

# 如ai-hegao-python目录存在，则删除
mkdir -p /data/test/ai-hegao-python

# 将/data/build/ai-hegao-python目录复制到/data/test/ai-hegao-python
cp -r /data/build/ai-hegao-python /data/test/ai-hegao-python

# 创建web_source_code目录
mkdir -p /data/test/ai-hegao-python/web_source_code

# 从容器中将ai-hegao-python目录复制到/data/test/ai-hegao-python
docker cp ai-hegao-python:/app/web_source_code/ /data/test/ai-hegao-python/web_source_code

# 制作镜像
cd /data/test/ai-hegao-python
docker build -t cis-hub-beijing-zs1.cmecloud.cn/jiutianwensi/ai-hegao-python:x86_64-1.0.0 .

# 启动容器
docker run -d --name ai-hegao-python-test -p 9097:8080 cis-hub-beijing-zs1.cmecloud.cn/jiutianwensi/ai-hegao-python:x86_64-1.0.0

# 测试curl
echo "curl -X POST "http://localhost:9097/spellcheck/oa" \
  -H "Content-Type: application/json" \
  -d '{"content":"请输入你的测试文笨。","type_list":[],"user_id":"123"}'"


# 登录镜像仓库
docker login --username=zglyjt --password=Znwd0415@ cis-hub-beijing-zs1.cmecloud.cn

# 推送镜像
docker push cis-hub-beijing-zs1.cmecloud.cn/jiutianwensi/ai-hegao-python:x86_64-1.0.0

# 将目录推送到************的/data/build目录
scp -r /data/test/ai-hegao-python wensi@************:/data/build
# 密码：HP7!y]ERc9_eiQ-M

# 在************主机上执行
ssh wensi@************
# 密码：HP7!y]ERc9_eiQ-M
cd /data/build/ai-hegao-python


# 在**********主机上执行
# 制作镜像
docker build -t cis-hub-beijing-zs1.cmecloud.cn/jiutianwensi/ai-hegao-python:arm64-1.0.0 .
# 启动容器
docker run -d --name ai-hegao-python-test -p 9097:8080 cis-hub-beijing-zs1.cmecloud.cn/jiutianwensi/ai-hegao-python:arm64-1.0.0

# 登录镜像仓库
docker login --username=zglyjt --password=Znwd0415@ officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn

# 推送镜像
docker push cis-hub-beijing-zs1.cmecloud.cn/jiutianwensi/ai-hegao-python:arm64-1.0.0


# 下载nginx镜像
docker pull m.daocloud.io/docker.io/nginx:1.27.4

docker tag m.daocloud.io/docker.io/nginx:1.27.4 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4  
docker tag m.daocloud.io/docker.io/nginx:1.27.4 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:x86_64-1.27.4  
# 登录镜像仓库
docker login --username=zglyjt --password=Znwd0415@ officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn

# 推送镜像
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:x86_64-1.27.4



# 删除Exited状态的容器
docker rm -f $(docker ps -a | grep Exited | awk '{print $1}')

# 删除none镜像
docker rmi $(docker images | grep '^<none>' | awk '{print $3}')

docker tag ai-writer-nostream-python:v1.0 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream:arm64-v2025.05.18
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream:arm64-v2025.05.18
#docker tag ai-writer-nostream-python:v1.0 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream:x86_64-v1.0
docker tag ai-hegao_plus:v1.0 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18

docker tag cis-hub-beijing-zs1.cmecloud.cn/jiutianwensi/poc-intelligence-view:arm64-1.0.0 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0

docker tag cis-hub-beijing-zs1.cmecloud.cn/jiutianwensi/poc-intelligence-view:x86_64-1.0.0 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:x86_64-1.0.0
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:x86_64-1.0.0


docker pull docker.elastic.co/elasticsearch/elasticsearch:7.17.14
docker tag docker.elastic.co/elasticsearch/elasticsearch:7.17.14 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14

docker pull docker.elastic.co/kibana/kibana:7.17.14
docker tag docker.elastic.co/kibana/kibana:7.17.14 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14 
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14 

docker pull docker.elastic.co/beats/filebeat:7.17.14
docker tag docker.elastic.co/beats/filebeat:7.17.14 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
docker tag officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14 10.7.202.240:8080/native_common/filebeat:arm64-7.17.14
docker push 10.7.202.240:8080/native_common/filebeat:arm64-7.17.14

docker tag officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14  10.7.202.240:8080/native_common/kibana:arm64-7.17.14
docker push 10.7.202.240:8080/native_common/kibana:arm64-7.17.14

docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream:arm64-v2025.05.18
docker tag officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream:arm64-v2025.05.18 10.7.202.240:8080/native_common/ai-writer-nostream:arm64-v2025.05.18
docker push 10.7.202.240:8080/native_common/ai-writer-nostream:arm64-v2025.05.18

docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/znwd/ai-writer-nostream-python-arm:v10.0
docker tag officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/znwd/ai-writer-nostream-python-arm:v10.0 10.7.202.240:8080/native_common/ai-writer-nostream-python-arm:v10.0
docker push 10.7.202.240:8080/native_common/ai-writer-nostream-python-arm:v10.0


docker pull m.daocloud.io/docker.io/minio/minio:RELEASE.2023-04-28T18-11-17Z
docker tag m.daocloud.io/docker.io/minio/minio:RELEASE.2023-04-28T18-11-17Z officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/minio:arm64-RELEASE.2023-04-28T18-11-17Z
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/minio:arm64-RELEASE.2023-04-28T18-11-17Z

docker pull m.daocloud.io/docker.io/redis:7.4.1
docker tag m.daocloud.io/docker.io/redis:7.4.1 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/redis:arm64-7.4.1
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/redis:arm64-7.4.1

docker pull public.ecr.aws/opensearchproject/opensearch:2.9.0
docker tag public.ecr.aws/opensearchproject/opensearch:2.9.0 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch:arm64-2.9.0
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch:arm64-2.9.0


docker pull public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0
docker tag public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch-dashboards:arm64-2.9.0
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch-dashboards:arm64-2.9.0

docker pull m.daocloud.io/docker.io/rabbitmq:3.12-management
docker tag m.daocloud.io/docker.io/rabbitmq:3.12-management officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/rabbitmq:arm64-3.12-management
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/rabbitmq:arm64-3.12-management

docker pull  m.daocloud.io/docker.io/postgres:12.18
docker tag  m.daocloud.io/docker.io/postgres:12.18 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/postgres:arm64-12.18
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/postgres:arm64-12.18













































