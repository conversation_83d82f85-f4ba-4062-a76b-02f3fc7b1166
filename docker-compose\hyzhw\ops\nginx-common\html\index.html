<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>智能公文系统 运维主页</title>
    <style>
        body { font-family: "微软雅黑", Arial, sans-serif; margin: 40px; background: #f7f7f7; }
        .container { max-width: 900px; margin: auto; background: #fff; padding: 32px 40px 40px 40px; border-radius: 12px; box-shadow: 0 2px 12px #0001; }
        h1 { color: #2c3e50; }
        .manual-links { margin-bottom: 32px; }
        .manual-links a { display: inline-block; margin: 0 16px 8px 0; color: #007bff; text-decoration: none; font-weight: bold; }
        .manual-links a:hover { text-decoration: underline; }
        #readme { margin-top: 40px; }
        .readme-title { font-size: 1.3em; margin-bottom: 16px; color: #34495e; }
    </style>
</head>
<body>
<div class="container">
    <h1>智能公文系统 运维主页</h1>
    <div class="manual-links">
        <h2>操作手册下载</h2>
        <a href="操作手册/AI核稿操作手册.docx" download>AI核稿操作手册.docx</a>
        <a href="操作手册/国务院公文测试案例.docx" download>国务院公文测试案例.docx</a>
        <a href="操作手册/国务院部门文测试案例.docx" download>国务院部门文测试案例.docx</a>
    </div>
    <div id="readme">
        <div class="readme-title">运维手册（README.md）</div>
        <div id="readme-content" style="background:#f9f9f9;padding:24px;border-radius:8px;overflow:auto;"></div>
    </div>
</div>
<!-- 引入 marked.js 用于渲染 markdown -->
<script src="marked.min.js"></script>
<script>
fetch('README.md')
  .then(res => res.text())
  .then(md => {
    document.getElementById('readme-content').innerHTML = marked.parse(md);
  })
  .catch(() => {
    document.getElementById('readme-content').innerHTML = '<em>未找到 README.md 文件</em>';
  });
</script>
</body>
</html> 