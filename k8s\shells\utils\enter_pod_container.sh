#!/bin/bash

# 进入Pod容器
# 参数: $1 - Pod名称, $2 - 命名空间
function enter_pod_container() {
    local pod_name=$1
    local namespace=$2
    
    if [ -n "$pod_name" ]; then
        # 检查Pod是否处于Running状态
        if kubectl get pod $pod_name -n $namespace | grep -q "Running"; then
            # 检查Pod中的容器数量
            CONTAINERS=($(kubectl get pod $pod_name -n $namespace -o jsonpath='{.spec.containers[*].name}'))
            CONTAINER_COUNT=${#CONTAINERS[@]}
            
            if [ $CONTAINER_COUNT -gt 1 ]; then
                echo "Pod $pod_name 包含多个容器："
                for (( i=0; i<$CONTAINER_COUNT; i++ )); do
                    echo "[$i] ${CONTAINERS[$i]}"
                done
                read -p "请选择要进入的容器序号: " CONTAINER_INDEX
                if [[ $CONTAINER_INDEX =~ ^[0-9]+$ ]] && [ $CONTAINER_INDEX -lt $CONTAINER_COUNT ]; then
                    CONTAINER_NAME=${CONTAINERS[$CONTAINER_INDEX]}
                    echo "kubectl exec -it $pod_name -c $CONTAINER_NAME -n $namespace -- /bin/bash"
                    kubectl exec -it $pod_name -c $CONTAINER_NAME -n $namespace -- /bin/bash
                else
                    echo "无效的选择，跳过进入容器"
                fi
            else
                echo "kubectl exec -it $pod_name -n $namespace -- /bin/bash"
                kubectl exec -it $pod_name -n $namespace -- /bin/bash
            fi
        else
            echo "警告: Pod $pod_name 不处于Running状态，无法进入容器"
            kubectl get pod $pod_name -n $namespace
        fi
    else
        echo "Pod名称为空，无法进入容器"
    fi
}

# 导出函数
export -f enter_pod_container 