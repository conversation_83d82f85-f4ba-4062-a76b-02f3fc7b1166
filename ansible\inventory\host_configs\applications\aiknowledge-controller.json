{"app_name": "aiknowledge-controller", "module_name": "znwd", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "1.0-SNAPSHOT", "image_name": "znwd/aiknowledge-controller:1.0-SNAPSHOT", "container_port": "8080", "host_port": "8082", "app_data_dir": "/app/files", "app_logs_dir": "/app/logs", "app_config_dir": "/app/config", "host_data_dir": "{{base_dir}}/znwd/aiknowledge-controller/data", "host_logs_dir": "{{base_dir}}/znwd/aiknowledge-controller/log", "host_config_dir": "{{base_dir}}/znwd/aiknowledge-controller/config", "restart_script": "{{base_dir}}/znwd/aiknowledge-controller/restart.sh", "test_script": "{{base_dir}}/znwd/aiknowledge-controller/test_curl.sh", "runtime": "java17", "env_vars": [{"name": "JAVA_OPTS", "value": "-Xms512m -Xmx1g"}], "external_dependencies": [{"type": "middleware", "name": "elasticsearch", "url": "elasticsearch:9200"}, {"type": "middleware", "name": "opensearch", "url": "opensearch:9200"}, {"type": "service", "name": "embedding-service-bge-m3", "url": "http://embedding-service-bge-m3:8080"}], "test_commands": ["curl -s http://localhost:8082/actuator/health | grep UP"]}