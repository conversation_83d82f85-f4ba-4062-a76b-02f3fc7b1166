version: '3'

services:
  ai-knowledge-controller:
    image: znwd/ai-knowledge-controller:v1
    container_name: ai-knowledge-controller
    ports:
      - "8097:8080"
    volumes:
      - ${BASE_DIR:-/data/znwd}/ai-knowledge-controller/config/application.properties:/app/config/
      - ${BASE_DIR:-/data/znwd}/ai-knowledge-controller/logs:/app/logs
      - ${BASE_DIR:-/data/znwd}/ai-knowledge-controller/shells:/app/shells
      - ${BASE_DIR:-/data/znwd}/ai-knowledge-controller/app.jar:/app/app.jar
    environment:
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always
  
networks:
  jiutianwensi-network:
    external: true 