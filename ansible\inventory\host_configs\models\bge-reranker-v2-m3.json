{"model_name": "bge-reranker-v2-m3", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "embedding/bge-reranker-v2-m3:x86.python3.10.v1", "host_port": "8103", "container_port": "8080", "api_endpoint": "/Rag/BgeRerankV2M3Service", "base_model": "BAAI/bge-reranker-v2-m3", "host_logs_dir": "{{base_dir}}/embedding/bge-reranker-v2-m3/logs", "host_shells_dir": "{{base_dir}}/embedding/bge-reranker-v2-m3/shells", "container_logs_dir": "/app/logs", "container_shells_dir": "/app/shells", "restart_script": "{{base_dir}}/embedding/bge-reranker-v2-m3/restart.sh", "test_script": "{{base_dir}}/embedding/bge-reranker-v2-m3/test_curl.sh", "env_vars": [{"name": "TZ", "value": "Asia/Shanghai"}], "test_commands": ["curl -X POST \"http://localhost:8103/Rag/BgeRerankV2M3Service\" -H 'Content-Type: application/json' -d '{\"query\":\"如何使用BGE-Reranker?\",\"recalls\":[{\"recall_id\": \"1\", \"query\": \"什么是BGE?\", \"answer\": \"BGE (BAAI General Embedding) 是北京智源人工智能研究院开发的通用embedding模型，可以用于文本向量化。\"},{\"recall_id\": \"2\", \"answer\": \"BGE-Reranker是一个重排序模型，用于对检索结果进行精排序，提高匹配质量。\"},{\"recall_id\": \"3\", \"query\": \"如何使用BGE模型\", \"answer\": \"可以通过FlagEmbedding库使用BGE模型，支持文本向量化和语义检索功能。\"}],\"topn\":2,\"similarity\":0.3}'", "curl -X GET \"http://localhost:8103/health\""]}