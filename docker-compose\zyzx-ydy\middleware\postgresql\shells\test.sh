
echo "查看容器版本"
docker exec -it postgres-server psql -V
echo "查看容器配置"
docker exec -it postgres-server cat /etc/postgresql/postgresql.conf
# postgresql发送测试数据
echo "postgresql发送测试数据"
echo "docker exec -it postgres-server psql -U admin -d postgres -c \"INSERT INTO test (name, age) VALUES ('test', 18)\""
docker exec -it postgres-server psql -U admin -d postgres -c "INSERT INTO test (name, age) VALUES ('test', 18)"
echo "查看postgresql的索引"
echo "docker exec -it postgres-server psql -U admin -d postgres -c \"SELECT * FROM test\""
docker exec -it postgres-server psql -U admin -d postgres -c "SELECT * FROM test"
echo "删除postgresql的索引"
echo "docker exec -it postgres-server psql -U admin -d postgres -c \"DELETE FROM test\""
docker exec -it postgres-server psql -U admin -d postgres -c "DELETE FROM test"