#!/bin/bash

set -e

# 脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

log() {
  echo -e "\033[34m[MAIN]\033[0m $1"
}

# 创建数据目录，用于存放最终打包和清单
DATA_DIR="$(pwd)/data"
mkdir -p "$DATA_DIR"
log "数据输出目录: $DATA_DIR"

# 准备阶段：处理配置参数替换
log "开始第一阶段：准备处理配置参数替换..."
"$SCRIPT_DIR/prepare.sh"

# 确保local_apps.json存在
if [ ! -f local_apps.json ]; then
  log "错误: local_apps.json文件不存在，请确保prepare.sh执行正确"
  exit 1
fi

log "开始第二阶段：标准化应用目录..."
# 标准化处理每个应用
jq -c '.[]' local_apps.json | while read -r app; do
  app_name=$(echo "$app" | jq -r '.app_name')
  log "标准化处理应用: $app_name"
  "$SCRIPT_DIR/standardize.sh" "$app_name"
done

log "开始第三阶段：移动应用目录到数据目录..."
# 读取local_apps.json中的应用名列表，将每个应用目录复制到data目录下
jq -c '.[]' local_apps.json | while read -r app; do
  app_name=$(echo "$app" | jq -r '.app_name')
  if [ -d "$app_name" ]; then
    log "复制应用目录 $app_name 到 $DATA_DIR/"
    cp -r "$app_name" "$DATA_DIR/"
  else
    log "警告: 未找到应用目录 $app_name，跳过复制"
  fi
done

log "开始第四阶段：打包应用..."
# 打包阶段：调用packager.sh进行打包
"$SCRIPT_DIR/packager.sh" local_apps.json "$DATA_DIR" "$DATA_DIR"

log "处理完成。结果已输出到 $DATA_DIR"
ls -l "$DATA_DIR"

# 如果data目录中有tar.gz文件，则展示其大小
find "$DATA_DIR" -name "*.tar.gz" -exec du -h {} \; 