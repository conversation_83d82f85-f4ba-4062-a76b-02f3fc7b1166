FROM m.daocloud.io/docker.io/python:3.10-slim

WORKDIR /home
ENV UV_HTTP_TIMEOUT=120
# 安装网络工具和编译依赖
RUN apt-get update && \
    apt-get install -y curl telnet vim tree build-essential liblzma-dev \
    cmake libboost-all-dev zlib1g-dev libbz2-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 配置pip使用国内镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com
#RUN echo "安装tensorflow-cpu.................."
#RUN pip install tensorflow-cpu==2.10.0 --dry-run | cat
#RUN echo "安装h5py.................."
#RUN pip install h5py==3.7.0 --dry-run | cat
#RUN pip uninstall -y numpy
#RUN pip install numpy==1.25.0
# 复制依赖文件并安装依赖
COPY requirements.txt .
RUN pip install uv 
RUN uv pip install --index-url https://mirrors.aliyun.com/pypi/simple/ --prerelease=allow --system -r requirements.txt

# 复制项目文件
COPY rag_qa_online_v4 /home/<USER>

# 复制并修正shell脚本权限
COPY start.sh /home/<USER>/start.sh
RUN chmod +x /home/<USER>/start.sh

# 设置环境变量
ENV PYTHONPATH=/home
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8080

# 设置启动命令，直接调用Python运行views.py，而不是通过shell脚本
CMD ["/bin/bash", "/home/<USER>/start.sh"] 