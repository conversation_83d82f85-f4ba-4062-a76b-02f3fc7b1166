#! /bin/bash
echo "启动mindie服务..."
MINDIE_PATH="/usr/local/Ascend/mindie/latest/mindie-service" # mindie服务路径

# 设置config.json权限为640
#echo "设置config.json权限为640..."
#echo "chmod 640 ${MINDIE_PATH}/conf/config.json"
#chmod 640 ${MINDIE_PATH}/conf/config.json

# 修改驱动权限
#echo "chmod 440 /usr/local/Ascend/mindie/2.0.T3/mindie-service/lib/libmindieservice_llm_engine.so"
#chmod 440 /usr/local/Ascend/mindie/2.0.T3/mindie-service/lib/libmindieservice_llm_engine.so

# 修改 connector 权限为 400（只读）
#echo "chmod 400 /usr/local/Ascend/mindie/2.0.T3/mindie-llm/bin/mindie_llm_backend_connector"
#chmod 400 /usr/local/Ascend/mindie/2.0.T3/mindie-llm/bin/mindie_llm_backend_connector

# 确保目录权限正确
#echo "chmod 755 /usr/local/Ascend/mindie/2.0.T3/mindie-llm/bin"
#chmod 755 /usr/local/Ascend/mindie/2.0.T3/mindie-llm/bin

# 启动mindie服务
cd ${MINDIE_PATH}/bin
./mindieservice_daemon

echo "启动mindie服务完成"


echo "测试VLLM接口："
echo "curl 127.0.0.1:1025/generate -d '{\"prompt\": \"What is deep learning?\", \"max_tokens\": 32, \"stream\": false, \"do_sample\":true, \"repetition_penalty\": 1.00, \"temperature\": 0.01, \"top_p\": 0.001, \"top_k\": 1, \"model\": \"llama\"}'"
curl 127.0.0.1:1025/generate -d '{"prompt": "What is deep learning?", "max_tokens": 32, "stream": false, "do_sample":true, "repetition_penalty": 1.00, "temperature": 0.01, "top_p": 0.001, "top_k": 1, "model": "llama"}'
echo "测试完成"
