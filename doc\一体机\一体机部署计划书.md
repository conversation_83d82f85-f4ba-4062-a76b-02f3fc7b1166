# 中移巡视系统一体机部署计划书

## 1. 项目背景

### 1.1 项目概述

在云能的一体机中部署中移巡视系统，一体机中包含多个服务，需要将这些服务部署到一体机中，并保证服务的正常运行。

### 1.2 项目意义

- 实现中移巡视系统在离线环境下的完整部署
- 提供稳定可靠的服务运行环境
- 建立完整的运维管理体系
- 为客户提供一体化的解决方案

### 1.3 技术架构

- **前端服务**：用户界面和交互
- **后端服务**：业务逻辑处理
- **中间件服务**：数据库、缓存、消息队列等
- **AI模型服务**：大模型、向量模型、排序模型
- **运维监控**：日志管理、监控告警、运维控制台

## 2. 项目目标

### 2.1 总体目标

在8月10日前完成中移巡视系统在一体机环境中的完整部署，建立稳定、可维护、可监控的服务运行体系，并提供运维控制台的访问地址。

### 2.2 具体目标

1. **服务部署目标**

   - 完成所有服务的ARM版本镜像制作和部署
   - 确保服务间调用关系正常
   - 实现服务的自动化启动和停止
2. **运维管理目标**

   - 建立完整的监控告警体系
   - 开发运维控制台，提供可视化运维管理
   - 制定标准化的故障处理流程
3. **文档交付目标**

   - 完成服务台账、部署方案等技术文档
   - 提供运维手册和故障处理指南
   - 建立文档版本管理体系
4. **时间目标**

   - 8月10日前完成所有部署工作
   - 提供运维控制台的访问地址
   - 完成客户培训和交付

## 3. 项目计划

### 3.1 项目阶段划分

#### 第一阶段：部署前准备（预计3个工作日）

**时间安排**：7月29日-7月31日

**主要任务**：

1. 梳理中移巡视系统的服务清单

   - 服务名称、类型、版本信息
   - 容器内日志路径、数据路径
   - 启动脚本、配置文件路径
   - 环境变量、下游服务依赖
2. 梳理AI模型服务

   - 磐智模型API报文格式
   - 向量模型、排序模型接口规范
   - 模型调用链路分析
3. 服务架构分析

   - 绘制服务调用关系图
   - 分析服务依赖关系
   - 识别关键路径和瓶颈点
4. 部署方案设计

   - 服务部署架构设计
   - 数据存储方案设计
   - 网络配置方案设计
   - 安全配置方案设计
5. 镜像制作和推送

   - 制作前端、后端、中间件的ARM版本镜像
   - 推送到移动云镜像仓库
   - 镜像版本管理和标签规范：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/<服务名称>:CPU架构(arm/x86)-<版本号>
6. 告警体系设计

   - 梳理告警指标
   - 设计告警规则
   - 配置告警通知机制
7. 服务测试用例设计

   - 梳理每个服务的测试curl命令样例
   - 提供测试结果和验证标准
   - 设计自动化测试脚本

**交付物**：

- 服务清单文档
- 服务调用关系图
- 部署架构设计文档
- ARM版本镜像（已推送）
- 告警配置方案
- 服务测试用例文档

#### 第二阶段：环境准备和基础部署（预计2个工作日）

**时间安排**：8月1日-8月2日

**主要任务**：

1. 一体机环境准备

   - 申请一体机访问账号
   - 测试互联网访问权限
   - 验证yum源可用性
   - 测试移动云镜像仓库访问
2. 基础软件安装

   - 安装Docker和Docker Compose
   - 配置Docker镜像加速
   - 测试容器运行环境
3. AI模型服务验证

   - 测试大模型服务可用性
   - 验证向量模型功能
   - 确认排序模型接口
4. 部署脚本开发

   - 编写docker-compose.yml文件
   - 开发shell脚本菜单
   - 制作配置文件模板
   - 编写启动、停止、测试脚本

**交付物**：

- 环境准备报告
- 基础软件安装文档
- AI模型服务测试报告
- 部署脚本和配置文件

#### 第三阶段：服务部署和测试（预计4个工作日）

**时间安排**：8月3日-8月6日

**主要任务**：

1. 中间件服务部署

   - 数据库服务部署
   - 缓存服务部署
   - 消息队列服务部署
   - 服务间连接测试
2. Python服务部署

   - AI相关Python服务部署
   - 数据处理服务部署
   - 服务功能测试
3. Java服务部署

   - 后端业务服务部署
   - 微服务架构部署
   - 服务接口测试
4. 前端服务部署

   - Web前端服务部署
   - 静态资源部署
   - 用户界面测试
5. 运维服务部署

   - 日志管理服务部署
   - 监控告警服务部署
   - 运维控制台部署
6. 集成测试

   - 服务间调用测试
   - 端到端功能测试
   - 性能压力测试

**交付物**：

- 各阶段部署报告
- 服务测试报告
- 集成测试报告
- 性能测试报告

#### 第四阶段：运维体系建设和文档交付（预计2个工作日）

**时间安排**：8月7日-8月8日

**主要任务**：

1. 运维控制台开发

   - 服务部署情况展示
   - 服务调用关系图展示
   - 部署日志查看功能
   - 测试报告展示功能
   - 日志分析功能
2. 故障处理流程制定

   - 制定故障处理标准流程
   - 编写故障处理文档
   - 建立故障处理记录机制
3. 文档整理和交付

   - 服务台账整理
   - 部署方案文档完善
   - 运维手册编写
   - 用户使用指南编写
4. 客户培训和交付

   - 运维控制台使用培训
   - 故障处理流程培训
   - 系统运维培训

**交付物**：

- 运维控制台系统
- 故障处理文档
- 完整的技术文档
- 培训材料和记录

### 3.2 关键里程碑

| 里程碑             | 时间节点 | 交付物                       |
| ------------------ | -------- | ---------------------------- |
| M1: 部署前准备完成 | 7月31日  | 服务清单、架构设计、镜像准备 |
| M2: 环境准备完成   | 8月2日   | 基础环境、部署脚本           |
| M3: 服务部署完成   | 8月6日   | 所有服务部署完成并测试通过   |
| M4: 项目交付完成   | 8月8日   | 运维体系、文档、培训完成     |

## 4. 项目分工

### 4.1 项目组织架构

```
项目负责人
├── 服务清单梳理人员（1人）
├── 镜像制作人员（1人）
└── 服务部署人员（1人）
└── 测试方（1人或团队）
```

### 4.2 角色分工

#### 项目负责人（1人）

**职责**：

- 项目整体规划和进度控制
- 与云能方协调沟通
- 资源协调和风险管控
- 项目质量把控

**技能要求**：

- 具备项目管理经验
- 熟悉DevOps和容器化部署
- 良好的沟通协调能力

#### 服务清单梳理人员（1人）

**职责**：

- 梳理中移巡视系统的服务清单
- 分析服务架构和调用关系
- 绘制服务调用关系图
- 设计部署方案
- 梳理告警指标和配置

**技能要求**：

- 熟悉系统架构设计
- 具备服务依赖分析能力
- 熟悉微服务架构
- 具备技术文档编写能力

#### 镜像制作人员（1人）

**职责**：

- 制作前端、后端、中间件的ARM版本镜像
- 推送到移动云镜像仓库
- 镜像版本管理和标签规范
- 镜像质量把控和测试

**技能要求**：

- 精通Docker镜像制作
- 熟悉ARM架构
- 具备多平台镜像构建经验
- 熟悉容器化技术

#### 服务部署人员（1人）

**职责**：

- 编写部署脚本和配置文件
- 按照部署顺序进行服务部署
- 服务间调用测试和验证
- 运维控制台开发和部署
- 故障处理流程制定

**技能要求**：

- 精通Docker和Docker Compose
- 熟悉Shell脚本编程
- 具备服务部署和运维经验
- 熟悉监控告警系统

#### 测试方（1人或团队）

**职责**：

- 依据服务清单和测试用例，对所有服务进行功能测试
- 输出测试报告，反馈问题

**技能要求**：

- 熟悉接口测试、自动化测试工具
- 具备问题分析和定位能力

**协作说明**：

测试方与服务清单梳理人员、镜像制作人员、服务部署人员密切协作，及时获取服务部署进展和测试需求，确保测试覆盖和问题闭环。

### 4.3 人员配置

**总人数**：3人

**人员配置明细**：

- 项目负责人：1人
- 服务清单梳理人员：1人
- 镜像制作人员：1人
- 服务部署人员：1人

**注**：项目负责人可兼任其中一个技术角色，实际投入3人

### 4.4 技能要求

**核心技术栈**：

- 容器化技术：Docker、Docker Compose
- 编程语言：Python、Java、Shell、JavaScript
- 数据库：PostgreSQL、Redis
- 消息队列：RabbitMQ
- 监控工具：Prometheus、Grafana
- 日志工具：Opensearch
- AI模型：大语言模型、向量模型、排序模型
- ARM架构：ARM64镜像构建和部署

**软技能要求**：

- 团队协作能力
- 问题解决能力
- 沟通表达能力
- 学习创新能力

### 4.5 合作方分工

#### 云能方职责

- 提供一体机硬件设备
- 安装910B操作系统
- 开通互联网访问权限
- 提供一体机访问账号
- 部署大模型、向量模型、排序模型
- 提供模型访问地址

#### 我方职责

- 梳理服务清单和架构
- 制作ARM版本镜像
- 推送到移动云镜像仓库
- 部署所有服务
- 开发运维控制台
- 提供运维控制台访问地址

## 5. 风险管控

### 5.1 技术风险

- **ARM架构兼容性风险**：提前进行兼容性测试
- **AI模型部署风险**：准备多种部署方案
- **服务依赖风险**：详细分析依赖关系，准备降级方案

### 5.2 进度风险

- **环境准备延迟**：提前申请资源，准备备选方案
- **镜像制作问题**：并行制作，预留调试时间
- **集成测试问题**：分阶段测试，及时发现问题

### 5.3 质量风险

- **功能缺陷**：建立完善的测试体系
- **性能问题**：进行充分的性能测试
- **安全漏洞**：进行安全扫描和加固

## 6. 成功标准

### 6.1 功能标准

- 所有服务正常启动和运行
- 服务间调用关系正常
- 用户功能完整可用

### 6.2 性能标准

- 系统响应时间满足要求
- 并发处理能力达标
- 资源利用率合理

### 6.3 运维标准

- 监控告警体系完善
- 故障处理流程清晰
- 运维控制台功能完整

### 6.4 文档标准

- 技术文档完整准确
- 运维手册实用易懂
- 培训材料详实有效

## 7. 项目交付

### 7.1 交付物清单

1. **技术文档**

   - 服务清单和架构文档
   - 部署方案和配置文档
   - API接口文档
2. **运维工具**

   - 运维控制台系统
   - 监控告警配置
   - 自动化部署脚本
3. **培训材料**

   - 运维培训手册
   - 故障处理指南
   - 用户使用手册
4. **源代码和镜像**

   - ARM版本Docker镜像
   - 部署脚本源码
   - 配置文件模板

### 7.2 验收标准

- 功能验收：所有功能测试通过
- 性能验收：性能指标满足要求
- 文档验收：文档完整且准确
- 培训验收：客户掌握运维技能

---

**文档版本**：v1.1
**编制日期**：2024年7月
**编制人员**：项目组
**审核人员**：项目负责人
