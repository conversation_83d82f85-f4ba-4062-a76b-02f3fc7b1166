#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色
APP_VERSION="v1"

APP_NAME="ai-hegao-python"
MODULE_NAME="hegao"
APP_DIR="/data/$MODULE_NAME/$APP_NAME"
DEPLOY_DIR="/data/build/$APP_NAME"
PORT="8097"
CONTAINER_NAME="$APP_NAME"
APP_CONFIG_PATH="$APP_DIR/config/application.properties"
APP_START_SCRIPT="$APP_DIR/shells/start.sh"
APP_RESTART_SCRIPT="$APP_DIR/restart.sh"
APP_TEST_CURL_SCRIPT="$APP_DIR/test_curl.sh"
APP_LOGS_PATH="$APP_DIR/logs"
APP_SHELLS_PATH="$APP_DIR/shells"


# 容器内信息
# 容器启动端口
CONTAINER_START_PORT="8080"
# 容器启动脚本
CONTAINER_START_SCRIPT="start.sh"
# 容器配置文件路径
CONTAINER_CONFIG_PATH="/app/config/application.properties"
# 容器日志路径
CONTAINER_LOG_PATH="/app/web_source_code/log"
# 容器数据路径
CONTAINER_DATA_PATH="/app/web_source_code/local_data"
# 容器shell脚本路径
CONTAINER_SHELLS_PATH="/app/shells"

echo  "=================容器内信息==================="
echo "容器内启动端口: $CONTAINER_START_PORT"
echo "容器内启动脚本: $CONTAINER_START_SCRIPT"
echo "容器内配置文件路径: $CONTAINER_CONFIG_PATH"
echo "容器内日志路径: $CONTAINER_LOG_PATH"
echo "容器内数据路径: $CONTAINER_DATA_PATH"
echo "容器内shell脚本路径: $CONTAINER_SHELLS_PATH"
echo "=================容器内信息==================="

echo  "=================容器宿主机信息==================="
echo "容器名称: $APP_NAME"
echo "容器宿主机端口: $PORT"
echo "容器宿主机目录: $APP_DIR"
echo "容器宿主机配置文件路径: $APP_CONFIG_PATH"
echo "容器宿主机启动脚本: $APP_START_SCRIPT"
echo "容器宿主机重启脚本: $APP_RESTART_SCRIPT"
echo "容器宿主机测试curl脚本: $APP_TEST_CURL_SCRIPT"
echo "容器宿主机日志路径: $APP_LOGS_PATH"
echo "容器宿主机shell脚本路径: $APP_SHELLS_PATH"
echo "=================容器宿主机信息==================="

echo  "=================容器宿主机部署信息==================="
echo "容器宿主机部署目录: $DEPLOY_DIR"
echo "容器宿主机Dockerfile路径: $DOCKERFILE_PATH"
tree $DEPLOY_DIR
echo "=================容器宿主机部署信息==================="


echo -e "${BLUE}=== $APP_NAME 重启脚本 ===${NC}"

# 反向复制配置和脚本文件（从APP_DIR到DEPLOY_DIR）
echo -e "${YELLOW}反向复制配置和脚本文件到构建目录...${NC}"
# 确保DEPLOY_DIR存在
mkdir -p $DEPLOY_DIR

# 复制配置文件 - 修正路径
if [ -f "$APP_DIR/config/application.properties" ]; then
    echo -e "${GREEN}复制 $APP_DIR/config/application.properties -> $DEPLOY_DIR/application.properties${NC}"
    cp -f $APP_DIR/config/application.properties $DEPLOY_DIR/application.properties
else
    echo -e "${RED}配置文件不存在: $APP_DIR/config/application.properties${NC}"
    # 检查可能的备选位置
    if [ -f "$APP_DIR/config/application.properties" ]; then
        echo -e "${YELLOW}在备选位置找到配置文件，复制 $APP_DIR/config/application.properties -> $DEPLOY_DIR/application.properties${NC}"
        cp -f $APP_DIR/config/application.properties $DEPLOY_DIR/application.properties
    fi
fi

# 复制脚本文件
if [ -f "$APP_DIR/shells/start.sh" ]; then
    echo -e "${GREEN}复制 $APP_DIR/shells/start.sh -> $DEPLOY_DIR/start.sh${NC}"
    cp -f $APP_DIR/shells/start.sh $DEPLOY_DIR/start.sh
else
    echo -e "${RED}脚本文件不存在: $APP_DIR/shells/start.sh${NC}"
fi

#复制restart.sh文件到DEPLOY_DIR
if [ -f "$APP_DIR/restart.sh" ]; then
    echo -e "${GREEN}复制 $APP_DIR/restart.sh -> $DEPLOY_DIR/restart.sh${NC}"
    cp -f $APP_DIR/restart.sh $DEPLOY_DIR/restart.sh
else
    echo -e "${RED}重启脚本不存在: $APP_DIR/restart.sh${NC}"
fi

#复制test_curl.sh文件到DEPLOY_DIR
if [ -f "$APP_DIR/test_curl.sh" ]; then
    echo -e "${GREEN}复制 $APP_DIR/test_curl.sh -> $DEPLOY_DIR/test_curl.sh${NC}"
    cp -f $APP_DIR/test_curl.sh $DEPLOY_DIR/test_curl.sh
else
    echo -e "${RED}测试脚本不存在: $APP_DIR/test_curl.sh${NC}"
fi
# 显示目录结构
echo -e "${YELLOW}显示目录结构...${NC}"
echo "APP_DIR结构:"
tree $APP_DIR
echo "DEPLOY_DIR结构:"
tree $DEPLOY_DIR

# 检查是否存在同名容器并停止
CONTAINER_NAME="$APP_NAME"
echo -e "${YELLOW}检查是否存在同名容器...${NC}"
if docker ps -a --format '{{.Names}}' | grep -q "^$CONTAINER_NAME$"; then
    echo -e "${YELLOW}发现已存在的$CONTAINER_NAME容器，正在停止并删除...${NC}"
    docker stop $CONTAINER_NAME
    docker rm -f $CONTAINER_NAME
fi

# 检查镜像是否存在
if ! docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^$MODULE_NAME/$APP_NAME:$APP_VERSION$"; then
    echo -e "${RED}错误: 镜像 $MODULE_NAME/$APP_NAME:$APP_VERSION 不存在${NC}"
    echo -e "${YELLOW}请先运行 deploy_article-auto-compose-python.sh 脚本构建镜像${NC}"
    exit 1
fi

# 运行容器 - 修正挂载路径
echo -e "${YELLOW}启动容器...${NC}"
echo "docker run -d --name $CONTAINER_NAME \
    --restart always \
    --network jiutianwensi \
    -p $PORT:$CONTAINER_START_PORT \
    -v $APP_CONFIG_PATH:$CONTAINER_CONFIG_PATH \
    -v $APP_LOGS_PATH:$CONTAINER_LOG_PATH \
    -v $APP_SHELLS_PATH:$CONTAINER_SHELLS_PATH \
    -e TZ=Asia/Shanghai \
    $MODULE_NAME/$APP_NAME:$APP_VERSION"
    
docker run -d --name $CONTAINER_NAME \
    --restart always \
    --network jiutianwensi \
    -p $PORT:$CONTAINER_START_PORT \
    -v $APP_CONFIG_PATH:$CONTAINER_CONFIG_PATH \
    -v $APP_LOGS_PATH:$CONTAINER_LOG_PATH \
    -v $APP_SHELLS_PATH:$CONTAINER_SHELLS_PATH \
    -e TZ=Asia/Shanghai \
    $MODULE_NAME/$APP_NAME:$APP_VERSION

if [ $? -ne 0 ]; then
    echo -e "${RED}容器启动失败，请检查错误信息${NC}"
    echo -e "${YELLOW}尝试查看文件挂载情况:${NC}"
    echo "application.properties 文件实际位置:"
    find $APP_DIR -name "application.properties" -type f
    exit 1
fi

echo -e "${GREEN}容器重启成功!${NC}"

# 显示容器状态
docker ps -f name=$CONTAINER_NAME

echo -e "${BLUE}=== 重启完成 ===${NC}"
echo "可以通过以下命令查看容器日志:"
echo "docker logs $CONTAINER_NAME"
echo
echo "应用访问地址: http://localhost:$PORT"
echo

# 确保日志和数据目录权限正确
echo "设置日志和数据目录权限"
echo "chmod 777 $APP_DIR/logs"
chmod 777 $APP_DIR/logs
echo "chmod 777 $APP_DIR/data"
chmod 777 $APP_DIR/data

#增加API测试
echo -e "${YELLOW}增加API测试...${NC}"
# ====================== API测试命令 ======================
echo -e "${BLUE}=== API测试命令 ===${NC}"
echo "以下是基于test_api.py生成的CURL测试命令:"

sleep 10

./test_curl.sh