pipeline {
    agent any
    
    parameters {
        string(name: 'APP_NAME', defaultValue: 'spring-boot-app', description: '应用名称')
        string(name: 'MODUL_NAME', defaultValue: 'backend', description: '模块名称')
        string(name: 'HOST_PORT', defaultValue: '8080', description: '宿主机映射端口')
        string(name: 'BUILD_NUMBER', defaultValue: '1.0.0', description: '构建版本号')
        string(name: 'REMOTE_SERVER', defaultValue: 'production-server', description: '远程服务器地址')
        string(name: 'REMOTE_USER', defaultValue: 'jenkins', description: '远程服务器用户名')
        string(name: 'CREDENTIALS_ID', defaultValue: 'server-credentials', description: '凭据ID(用户名密码凭据)')
        choice(name: 'ENV', choices: ['dev', 'test', 'prod'], description: '部署环境')
        string(name: 'DEPLOY_SCRIPT_ID', defaultValue: 'deploy-app-script', description: '部署脚本ID(Managed files)')
        string(name: 'APP_PROPERTIES_ID', defaultValue: '', description: '应用配置文件ID(留空则使用[应用名称]-[环境]-properties)')
        string(name: 'DOCKERFILE_ID', defaultValue: 'spring-boot-dockerfile', description: 'Dockerfile ID(Managed files)')
    }
    
    environment {
        // 远程服务器信息
        REMOTE_DIR = "/home/<USER>"
        // 动态设置配置文件ID
        EFFECTIVE_PROPERTIES_ID = "${params.APP_PROPERTIES_ID ?: params.APP_NAME + '-' + params.ENV + '-properties'}"
    }
    
    stages {
        stage('准备构建环境') {
            steps {
                // 清理工作空间
                cleanWs()
                
                // 创建构建目录
                sh "mkdir -p build/config"
            }
        }
        
        stage('复制应用程序JAR包') {
            steps {
                // 这里假设JAR包已经在Jenkins工作区中
                // 也可以从构件库(如Nexus或Artifactory)下载
                copyArtifacts filter: '**/*.jar', 
                              projectName: '${JOB_NAME}', 
                              selector: specific('${BUILD_NUMBER}')
                
                // 重命名JAR包
                sh "cp *.jar build/app.jar"
            }
        }
        
        stage('准备配置文件') {
            steps {
                // 使用Jenkins Managed Files功能获取配置文件
                configFileProvider([
                    configFile(fileId: "${EFFECTIVE_PROPERTIES_ID}", targetLocation: 'build/application.properties')
                ]) {
                    echo "应用配置文件已准备就绪"
                }
                
                // 获取Dockerfile
                configFileProvider([
                    configFile(fileId: "${params.DOCKERFILE_ID}", targetLocation: 'build/Dockerfile')
                ]) {
                    echo "Dockerfile已准备就绪"
                }
                
                // 获取部署脚本
                configFileProvider([
                    configFile(fileId: "${params.DEPLOY_SCRIPT_ID}", targetLocation: 'deploy_app.sh')
                ]) {
                    sh "chmod +x deploy_app.sh"
                    echo "部署脚本已准备就绪"
                }
            }
        }
        
        stage('打包应用') {
            steps {
                // 创建ZIP包供远程部署使用
                dir('build') {
                    sh "zip -r ../app.zip *"
                }
            }
        }
        
        stage('部署到远程服务器') {
            steps {
                withCredentials([usernamePassword(credentialsId: "${params.CREDENTIALS_ID}", usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                    // 确保sshpass已安装
                    sh 'which sshpass || (echo "sshpass未安装" && exit 1)'
                    
                    // 上传ZIP包到远程服务器
                    sh '''
                        export SSHPASS=${PASSWORD}
                        sshpass -e scp -o StrictHostKeyChecking=no app.zip ${params.REMOTE_USER}@${params.REMOTE_SERVER}:${REMOTE_DIR}/app.zip
                    '''
                    
                    // 上传部署脚本
                    sh '''
                        export SSHPASS=${PASSWORD}
                        sshpass -e scp -o StrictHostKeyChecking=no deploy_app.sh ${params.REMOTE_USER}@${params.REMOTE_SERVER}:${REMOTE_DIR}/deploy_app.sh
                    '''
                    
                    // 执行远程部署（使用命令行参数）
                    sh '''
                        export SSHPASS=${PASSWORD}
                        sshpass -e ssh -o StrictHostKeyChecking=no ${params.REMOTE_USER}@${params.REMOTE_SERVER} "
                        chmod +x ${REMOTE_DIR}/deploy_app.sh && 
                        ${REMOTE_DIR}/deploy_app.sh -a ${params.APP_NAME} -m ${params.MODUL_NAME} -p ${params.HOST_PORT} -b ${params.BUILD_NUMBER} -d ${REMOTE_DIR}/Dockerfile
                        "
                    '''
                }
            }
        }
    }
    
    post {
        success {
            echo "Spring Boot应用部署成功！"
        }
        failure {
            echo "Spring Boot应用部署失败，请检查日志"
        }
        always {
            // 清理工作空间
            cleanWs()
        }
    }
} 