#!/bin/bash

# 简单修复脚本 - 修复当前目录下所有文件的行尾问题
# 将Windows格式的CRLF（\r\n）转换为Unix格式的LF（\n）

echo "开始修复文件格式问题..."

# 使用sed修复所有脚本和配置文件
echo "修复.sh文件..."
find . -name "*.sh" -type f -exec sed -i 's/\r$//' {} \;

echo "修复.py文件..."
find . -name "*.py" -type f -exec sed -i 's/\r$//' {} \;

echo "修复.json文件..."
find . -name "*.json" -type f -exec sed -i 's/\r$//' {} \;

echo "修复.yaml和.yml文件..."
find . -name "*.yaml" -type f -exec sed -i 's/\r$//' {} \;
find . -name "*.yml" -type f -exec sed -i 's/\r$//' {} \;

# 设置可执行权限
echo "设置脚本文件可执行权限..."
find . -name "*.sh" -type f -exec chmod +x {} \;

echo "文件修复完成!" 