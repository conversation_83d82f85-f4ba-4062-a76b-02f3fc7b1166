version: '3'

services:
  ai-redactor:
    image: redactor/ai-redactor:v1
    container_name: ai-redactor
    ports:
      - "8097:8080"
    volumes:
      - ${BASE_DIR:-/data/redactor}/ai-redactor/config/application.properties:/app/config/application.properties
      - ${BASE_DIR:-/data/redactor}/ai-redactor/logs:/app/web_source_code/log
      - ${BASE_DIR:-/data/redactor}/ai-redactor/shells:/app/shells
    environment:
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always
  
networks:
  jiutianwensi-network:
    external: true 