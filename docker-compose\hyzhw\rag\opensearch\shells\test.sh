# 测试opensearch是否正常运行
PORT=9201
echo "测试opensearch是否正常运行"
echo "curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:${PORT}/ | grep -q '"status":"success"' && echo "OpenSearch 运行正常" || echo "OpenSearch 运行异常""
curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:${PORT}/ | grep -q '"status":"success"' && echo "OpenSearch 运行正常" || echo "OpenSearch 运行异常"

# 用curl测试opensearch创建测试索引test
echo "用curl测试opensearch创建测试索引test"
echo "curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:${PORT}/test | grep -q '"status":"success"' && echo "OpenSearch 创建索引成功" || echo "OpenSearch 创建索引失败""
curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:${PORT}/test | grep -q '"status":"success"' && echo "OpenSearch 创建索引成功" || echo "OpenSearch 创建索引失败"

# opensearch发送测试数据
echo "opensearch发送测试数据"
echo "curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:${PORT}/test/_doc/1 -X POST -H 'Content-Type: application/json' -d '{\"name\":\"test\",\"age\":18}'"
curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:${PORT}/test/_doc/1 -X POST -H 'Content-Type: application/json' -d '{\"name\":\"test\",\"age\":18}'

# 查看opensearch的索引的数据  
echo "查看opensearch的索引的数据"
echo "curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:${PORT}/test/_search?q=*&size=1"
curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:${PORT}/test/_search?q=*&size=1

# 删除opensearch的索引及数据
echo "删除opensearch的索引及数据"
echo "curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:${PORT}/test/_doc/1 -X DELETE"
curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:${PORT}/test/_doc/1 -X DELETE