{"app_name": "rag-doc-parser", "module_name": "znwd", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "v1", "image_name": "znwd/rag-doc-parser:v1", "container_port": "8080", "host_port": "8098", "app_data_dir": "/app/web_source_code/local_data", "app_logs_dir": "/app/web_source_code/log", "app_config_dir": "/app/web_source_code/backend/config", "host_data_dir": "{{base_dir}}/znwd/rag-doc-parser/data", "host_logs_dir": "{{base_dir}}/znwd/logs", "host_config_dir": "{{base_dir}}/znwd/rag-doc-parser/config", "restart_script": "{{base_dir}}/znwd/rag-doc-parser/restart.sh", "test_script": "{{base_dir}}/znwd/rag-doc-parser/test_curl.sh", "runtime": "python", "env_vars": [{"name": "PYTHONPATH", "value": "/app/web_source_code"}, {"name": "TZ", "value": "Asia/Shanghai"}], "external_dependencies": [{"type": "service", "name": "embedding-service-bge-m3", "url": "http://embedding-service-bge-m3:8080"}, {"type": "middleware", "name": "opensearch", "url": "opensearch:9200"}], "test_commands": ["curl -s http://localhost:8098/health | grep OK"]}