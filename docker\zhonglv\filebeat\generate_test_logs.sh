#!/bin/bash
echo "生成测试日志内容以验证Filebeat配置"

# 确保日志目录存在
for logdir in "/data/znwd/rbac/logs" "/data/znwd/qa/logs" "/data/znwd/aiknowledge-controller/logs"; do
  if [ ! -d "$logdir" ]; then
    echo "创建目录 $logdir"
    mkdir -p $logdir
    chmod -R 755 $logdir
  fi
done

# 生成测试日志内容
generate_log() {
  local log_file=$1
  local log_type=$2
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  
  echo "$timestamp [INFO] 这是一条测试日志 - $log_type - $(date +%s)" >> $log_file
  echo "已向 $log_file 添加测试日志"
}

# 为每个应用生成测试日志
generate_log "/data/znwd/rbac/logs/rbac.log" "RBAC服务"
generate_log "/data/znwd/rbac/logs/startup.log" "RBAC启动日志"
generate_log "/data/znwd/qa/logs/spring.log" "QA服务"
generate_log "/data/znwd/aiknowledge-controller/logs/application.log" "AI知识控制器"

# 确保文件权限正确
chmod 644 /data/znwd/rbac/logs/*.log
chmod 644 /data/znwd/qa/logs/*.log
chmod 644 /data/znwd/aiknowledge-controller/logs/*.log

echo "测试日志生成完成，等待Filebeat处理..."
sleep 10

# 检查索引数据
bash check_indices.sh 