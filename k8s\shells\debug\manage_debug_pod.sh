#!/bin/bash
# manage_debug_pod.sh - 用于管理、列出和删除调试状态的Pod
# 提供给k8s-menu.sh脚本使用的功能模块

set -eo pipefail

# 默认命名空间
NAMESPACE=${1:-"oa-llm"}

# 颜色定义
GREEN="\033[0;32m"
BLUE="\033[0;34m"
RED="\033[0;31m"
YELLOW="\033[1;33m"
NC="\033[0m" # 恢复默认颜色

# 依赖检查
check_dependencies() {
  command -v kubectl >/dev/null 2>&1 || { echo >&2 "需要kubectl但未安装"; exit 1; }
  command -v jq >/dev/null 2>&1 || { echo >&2 "需要jq但未安装"; exit 1; }
}

# 检查Pod是否处于调试状态
is_pod_in_debug_mode() {
  local pod_name=$1
  local namespace=$2
  
  echo -e "${YELLOW}检查Pod是否处于调试状态...${NC}" >&2
  
  # 检查名称是否包含-debug-
  if [[ "$pod_name" == *"-debug-"* ]]; then
    echo -e "${GREEN}通过名称确认: Pod是调试状态${NC}" >&2
    return 0  # 是调试状态
  fi
  
  # 获取Pod的容器命令
  local pod_command=$(kubectl get pod $pod_name -n $namespace -o jsonpath='{.spec.containers[0].command}' 2>/dev/null)
  local pod_args=$(kubectl get pod $pod_name -n $namespace -o jsonpath='{.spec.containers[0].args}' 2>/dev/null)
  
  # 检查是否包含典型的调试命令模式
  if [[ "$pod_command" == *"/bin/sh"* ]] && [[ "$pod_args" == *"while true; do sleep"* ]]; then
    echo -e "${GREEN}通过命令确认: Pod处于调试状态${NC}" >&2
    return 0  # 是调试状态
  else
    echo -e "${YELLOW}该Pod不是处于调试状态${NC}" >&2
    return 1  # 不是调试状态
  fi
}

# 检查是否存在调试控制器
check_debug_controllers() {
  local namespace=$1
  local debug_controllers=()
  
  echo -e "${BLUE}检查命名空间 ${namespace} 中的调试控制器...${NC}"
  
  # 检查Deployments
  local deployments=($(kubectl get deployment -n $namespace -o jsonpath='{.items[*].metadata.name}' 2>/dev/null | tr ' ' '\n' | grep -i "\-debug$\|\-debug-"))
  
  # 检查StatefulSets
  local statefulsets=($(kubectl get statefulset -n $namespace -o jsonpath='{.items[*].metadata.name}' 2>/dev/null | tr ' ' '\n' | grep -i "\-debug$\|\-debug-"))
  
  # 合并结果
  debug_controllers=( "${deployments[@]}" "${statefulsets[@]}" )
  
  # 打印结果
  if [ ${#debug_controllers[@]} -eq 0 ]; then
    echo -e "${YELLOW}未找到任何调试控制器${NC}"
  else
    echo -e "${GREEN}找到 ${#debug_controllers[@]} 个调试控制器:${NC}"
    for ctrl in "${debug_controllers[@]}"; do
      # 检查控制器类型
      if [[ " ${deployments[@]} " =~ " ${ctrl} " ]]; then
        local type="Deployment"
      else
        local type="StatefulSet"
      fi
      echo -e "  • ${ctrl} (${type})"
    done
  fi
  
  # 返回结果
  DEBUG_CONTROLLERS=("${debug_controllers[@]}")
  DEBUG_DEPLOYMENTS=("${deployments[@]}")
  DEBUG_STATEFULSETS=("${statefulsets[@]}")
}

# 删除调试控制器
delete_debug_controller() {
  local controller_name=$1
  local namespace=$2
  
  # 首先确定控制器类型
  local ctrl_type=""
  kubectl get deployment $controller_name -n $namespace &>/dev/null && ctrl_type="deployment"
  if [ -z "$ctrl_type" ]; then
    kubectl get statefulset $controller_name -n $namespace &>/dev/null && ctrl_type="statefulset"
  fi
  
  if [ -z "$ctrl_type" ]; then
    echo -e "${RED}错误: 找不到控制器 ${controller_name}${NC}"
    return 1
  fi
  
  echo -e "${YELLOW}正在删除${ctrl_type} ${controller_name}...${NC}"
  kubectl delete $ctrl_type $controller_name -n $namespace
  
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}成功删除${ctrl_type} ${controller_name}${NC}"
    return 0
  else
    echo -e "${RED}删除${ctrl_type} ${controller_name}失败${NC}"
    return 1
  fi
}

# 列出所有调试状态的Pod
list_debug_pods() {
  local namespace=$1
  local debug_pods=()
  
  echo -e "${BLUE}正在查找调试状态的Pod...${NC}"
  
  # 方法1: 更严格地匹配调试状态特征 - 寻找完全匹配调试模式的命令
  echo "方法1: 查找调试状态的Pod..."
  local found_debug_pods_1=($(kubectl get pods -n $namespace -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.spec.containers[0].command}{"\t"}{.spec.containers[0].args}{"\n"}{end}' | grep "/bin/sh" | grep "while true; do sleep" | awk '{print $1}'))
  
  # 方法2: 查找名称中包含debug的Pod (精确匹配)
  echo "方法2: 查找名称包含debug的Pod..."
  local found_debug_pods_2=($(kubectl get pods -n $namespace -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n' | grep -i "\-debug\-\|\-debug$\|debug\-"))
  
  # 合并结果并去重
  for pod in "${found_debug_pods_1[@]}" "${found_debug_pods_2[@]}"; do
    if [[ -n "$pod" && ! " ${debug_pods[@]} " =~ " ${pod} " ]]; then
      debug_pods+=("$pod")
    fi
  done
  
  # 打印结果
  if [ ${#debug_pods[@]} -eq 0 ]; then
    echo -e "${YELLOW}未发现任何处于调试状态的Pod${NC}"
    echo "{\"count\": 0, \"pods\": []}" # 返回JSON格式的空结果
  else
    echo -e "${GREEN}找到 ${#debug_pods[@]} 个调试状态的Pod:${NC}"
    
    # 构建JSON数组
    local json_result='{"count": '"${#debug_pods[@]}"', "pods": ['
    local first=true
    
    for pod in "${debug_pods[@]}"; do
      if [ "$first" = true ]; then
        first=false
      else
        json_result+=','
      fi
      
      local pod_status=$(kubectl get pod $pod -n $namespace -o jsonpath='{.status.phase}')
      
      # 获取控制器信息
      local controller_type=$(kubectl get pod $pod -n $namespace -o jsonpath='{.metadata.ownerReferences[0].kind}' 2>/dev/null)
      local controller_name=$(kubectl get pod $pod -n $namespace -o jsonpath='{.metadata.ownerReferences[0].name}' 2>/dev/null)
      
      # 如果是ReplicaSet，尝试找到Deployment
      if [[ "$controller_type" == "ReplicaSet" ]]; then
        local deployment=$(kubectl get rs $controller_name -n $namespace -o jsonpath='{.metadata.ownerReferences[0].name}' 2>/dev/null)
        if [[ -n "$deployment" ]]; then
          controller_type="Deployment"
          controller_name="$deployment"
        fi
      fi
      
      local controller_info=""
      if [[ -n "$controller_type" && -n "$controller_name" ]]; then
        controller_info=" (${controller_type}: ${controller_name})"
      fi
      
      echo -e "  • ${pod} - 状态: ${pod_status}${controller_info}"
      
      # 添加到JSON
      json_result+='{"name": "'"$pod"'", "status": "'"$pod_status"'", "controller_type": "'"$controller_type"'", "controller_name": "'"$controller_name"'"}'
    done
    
    json_result+=']}'
    echo $json_result
  fi
  
  # 返回调试Pod数组
  DEBUG_PODS=("${debug_pods[@]}")
}

# 删除单个调试Pod
delete_debug_pod() {
  local pod_name=$1
  local namespace=$2
  
  echo -e "${YELLOW}检查Pod ${pod_name} 是否为调试Pod...${NC}"
  
  # 首先验证这是否为调试Pod
  is_pod_in_debug_mode "$pod_name" "$namespace"
  local is_debug=$?
  
  if [ $is_debug -eq 0 ]; then
    echo -e "${GREEN}确认为调试Pod，正在删除...${NC}"
    kubectl delete pod "$pod_name" -n "$namespace" --force --grace-period=0
    echo -e "${GREEN}Pod ${pod_name} 已删除${NC}"
    return 0
  else
    # 检查名称是否包含debug关键词
    if [[ "$pod_name" == *debug* ]]; then
      echo -e "${YELLOW}Pod名称包含debug，但未检测到调试命令。仍然删除? (yes/no)${NC}"
      read -p "" confirm
      
      if [ "$confirm" = "yes" ]; then
        kubectl delete pod "$pod_name" -n "$namespace" --force --grace-period=0
        echo -e "${GREEN}Pod ${pod_name} 已删除${NC}"
        return 0
      else
        echo -e "${YELLOW}取消删除${NC}"
        return 1
      fi
    else
      echo -e "${RED}警告: ${pod_name} 不是调试Pod，拒绝删除${NC}"
      return 1
    fi
  fi
}

# 判断控制器是否为调试控制器
is_controller_debug_controller() {
  local controller_name=$1
  
  if [[ "$controller_name" == *"-debug"* ]]; then
    return 0  # 是调试控制器
  else
    return 1  # 不是调试控制器
  fi
}

# 恢复调试Pod的控制器配置
restore_debug_pod_controller() {
  local pod_name=$1
  local namespace=$2
  
  # 获取控制器信息
  echo -e "${YELLOW}获取Pod ${pod_name} 的控制器信息...${NC}"
  
  # 先检查Pod是否存在
  kubectl get pod "$pod_name" -n "$namespace" &>/dev/null
  if [ $? -ne 0 ]; then
    echo -e "${RED}Pod ${pod_name} 不存在${NC}"
    return 1
  fi
  
  # 获取控制器类型和名称
  local controller_type=$(kubectl get pod "$pod_name" -n "$namespace" -o jsonpath='{.metadata.ownerReferences[0].kind}' 2>/dev/null)
  local controller_name=$(kubectl get pod "$pod_name" -n "$namespace" -o jsonpath='{.metadata.ownerReferences[0].name}' 2>/dev/null)
  
  # 检查是否获取到控制器信息
  if [[ -z "$controller_type" || -z "$controller_name" ]]; then
    echo -e "${RED}无法获取Pod的控制器信息${NC}"
    return 1
  fi
  
  echo -e "${GREEN}找到控制器: ${controller_type} ${controller_name}${NC}"
  
  # 如果控制器是ReplicaSet，尝试获取Deployment
  if [ "$controller_type" = "ReplicaSet" ]; then
    local deployment_name=$(kubectl get rs "$controller_name" -n "$namespace" -o jsonpath='{.metadata.ownerReferences[0].name}' 2>/dev/null)
    if [ -n "$deployment_name" ]; then
      controller_type="Deployment"
      controller_name="$deployment_name"
      echo -e "${GREEN}找到父控制器: ${controller_type} ${controller_name}${NC}"
    fi
  fi
  
  # 检查是否为调试控制器
  is_controller_debug_controller "$controller_name"
  local is_debug_controller=$?
  
  if [ $is_debug_controller -eq 0 ]; then
    # 这是一个调试控制器，提示删除整个控制器
    echo -e "${YELLOW}检测到这是一个调试控制器 (${controller_name})${NC}"
    echo -e "${YELLOW}推荐删除整个调试控制器，而不是修改其配置${NC}"
    echo -e "${YELLOW}是否删除此调试控制器? (yes/no)${NC}"
    read -p "" confirm
    
    if [ "$confirm" = "yes" ]; then
      delete_debug_controller "$controller_name" "$namespace"
      return $?
    else
      echo -e "${YELLOW}操作已取消${NC}"
      return 1
    fi
  fi
  
  # 获取容器名称
  local container_name=$(kubectl get pod "$pod_name" -n "$namespace" -o jsonpath='{.spec.containers[0].name}')
  
  echo -e "${YELLOW}使用JSON补丁方式移除调试命令...${NC}"
  
  # 生成适当的补丁命令，采用JSON补丁操作
  kubectl patch "${controller_type,,}" "${controller_name}" -n "${namespace}" --type json -p "[
    {\"op\":\"remove\",\"path\":\"/spec/template/spec/containers/0/command\"},
    {\"op\":\"remove\",\"path\":\"/spec/template/spec/containers/0/args\"}
  ]"
  
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}成功恢复控制器配置${NC}"
    echo -e "${YELLOW}提示: 如果Pod没有自动重启，可以手动删除:${NC}"
    echo -e "kubectl delete pod ${pod_name} -n ${namespace} --force --grace-period=0"
    return 0
  else
    echo -e "${RED}恢复控制器配置失败${NC}"
    return 1
  fi
}

# 删除所有调试Pod
delete_all_debug_pods() {
  local namespace=$1
  local force=$2  # 是否强制删除
  
  # 获取所有调试Pod
  list_debug_pods "$namespace" >/dev/null
  
  if [ ${#DEBUG_PODS[@]} -eq 0 ]; then
    echo -e "${YELLOW}没有找到任何调试Pod${NC}"
    return 0
  fi
  
  if [ "$force" != "force" ]; then
    echo -e "${RED}警告: 此操作将删除${#DEBUG_PODS[@]}个调试Pod${NC}"
    read -p "输入 'DELETE-ALL-DEBUG-PODS' 确认: " confirm
    
    if [ "$confirm" != "DELETE-ALL-DEBUG-PODS" ]; then
      echo -e "${YELLOW}操作已取消${NC}"
      return 1
    fi
  fi
  
  echo -e "${YELLOW}开始删除所有调试Pod...${NC}"
  local success_count=0
  
  for pod in "${DEBUG_PODS[@]}"; do
    echo -e "${BLUE}处理: ${pod}${NC}"
    delete_debug_pod "$pod" "$namespace"
    if [ $? -eq 0 ]; then
      ((success_count++))
    fi
  done
  
  echo -e "${GREEN}已成功删除 ${success_count}/${#DEBUG_PODS[@]} 个调试Pod${NC}"
  return 0
}

# 删除所有调试控制器
delete_all_debug_controllers() {
  local namespace=$1
  local force=$2  # 是否强制删除
  
  # 获取所有调试控制器
  check_debug_controllers "$namespace"
  
  if [ ${#DEBUG_CONTROLLERS[@]} -eq 0 ]; then
    echo -e "${YELLOW}没有找到任何调试控制器${NC}"
    return 0
  fi
  
  if [ "$force" != "force" ]; then
    echo -e "${RED}警告: 此操作将删除${#DEBUG_CONTROLLERS[@]}个调试控制器及其所有关联的Pod${NC}"
    read -p "输入 'DELETE-ALL-DEBUG-CONTROLLERS' 确认: " confirm
    
    if [ "$confirm" != "DELETE-ALL-DEBUG-CONTROLLERS" ]; then
      echo -e "${YELLOW}操作已取消${NC}"
      return 1
    fi
  fi
  
  echo -e "${YELLOW}开始删除所有调试控制器...${NC}"
  local success_count=0
  
  for ctrl in "${DEBUG_CONTROLLERS[@]}"; do
    echo -e "${BLUE}处理控制器: ${ctrl}${NC}"
    delete_debug_controller "$ctrl" "$namespace"
    if [ $? -eq 0 ]; then
      ((success_count++))
    fi
  done
  
  echo -e "${GREEN}已成功删除 ${success_count}/${#DEBUG_CONTROLLERS[@]} 个调试控制器${NC}"
  return 0
}

# 交互式管理调试Pod和控制器
manage_debug_pods_interactive() {
  local namespace=${1:-"oa-llm"}
  
  echo -e "${BLUE}===== 调试资源管理工具 =====${NC}"
  echo -e "${YELLOW}命名空间: ${namespace}${NC}"
  
  # 检查调试控制器
  check_debug_controllers "$namespace"
  local has_controllers=false
  if [ ${#DEBUG_CONTROLLERS[@]} -gt 0 ]; then
    has_controllers=true
    echo -e "${BLUE}找到 ${#DEBUG_CONTROLLERS[@]} 个调试控制器:${NC}"
    for i in "${!DEBUG_CONTROLLERS[@]}"; do
      # 确定控制器类型
      local ctrl_type="Unknown"
      if [[ " ${DEBUG_DEPLOYMENTS[@]} " =~ " ${DEBUG_CONTROLLERS[$i]} " ]]; then
        ctrl_type="Deployment"
      elif [[ " ${DEBUG_STATEFULSETS[@]} " =~ " ${DEBUG_CONTROLLERS[$i]} " ]]; then
        ctrl_type="StatefulSet"
      fi
      echo -e "${GREEN}C$((i+1)). ${DEBUG_CONTROLLERS[$i]}${NC} - 类型: ${ctrl_type}"
    done
    echo
  fi
  
  # 列出所有调试Pod
  list_debug_pods "$namespace" >/dev/null
  
  if [ ${#DEBUG_PODS[@]} -eq 0 ]; then
    if [ "$has_controllers" = false ]; then
      echo -e "${YELLOW}没有找到任何调试Pod或控制器${NC}"
      return 0
    fi
  else
    # 显示所有找到的调试Pod
    echo -e "${BLUE}找到 ${#DEBUG_PODS[@]} 个调试Pod:${NC}"
    for i in "${!DEBUG_PODS[@]}"; do
      local pod_status=$(kubectl get pod ${DEBUG_PODS[$i]} -n $namespace -o jsonpath='{.status.phase}')
      echo -e "${GREEN}P$((i+1)). ${DEBUG_PODS[$i]}${NC} - 状态: ${pod_status}"
    done
    echo
  fi
  
  # 显示选项
  echo -e "${BLUE}可用操作:${NC}"
  echo -e "${GREEN}1. 删除单个调试Pod${NC}"
  echo -e "${GREEN}2. 恢复控制器配置 (移除调试命令)${NC}"
  if [ "$has_controllers" = true ]; then
    echo -e "${GREEN}3. 删除单个调试控制器${NC}"
  fi
  echo -e "${RED}9. 删除所有调试Pod${NC}"
  if [ "$has_controllers" = true ]; then
    echo -e "${RED}0. 删除所有调试控制器 (包含关联的Pod)${NC}"
  fi
  echo -e "${YELLOW}q. 退出${NC}"
  echo
  
  read -p "请选择操作: " action
  
  case $action in
    1)
      # 删除单个调试Pod
      if [ ${#DEBUG_PODS[@]} -eq 0 ]; then
        echo -e "${YELLOW}没有找到调试Pod可删除${NC}"
        return 0
      fi
      
      read -p "输入要删除的Pod编号 (P1-P${#DEBUG_PODS[@]}): " pod_number
      
      # 移除前缀P
      pod_number=${pod_number#P}
      
      if [[ "$pod_number" =~ ^[0-9]+$ ]] && [ "$pod_number" -ge 1 ] && [ "$pod_number" -le ${#DEBUG_PODS[@]} ]; then
        local selected_pod=${DEBUG_PODS[$((pod_number-1))]}
        
        echo -e "${YELLOW}您选择了: ${selected_pod}${NC}"
        read -p "确认删除此Pod? (yes/no): " confirm
        
        if [ "$confirm" = "yes" ]; then
          delete_debug_pod "$selected_pod" "$namespace"
        else
          echo -e "${YELLOW}操作已取消${NC}"
        fi
      else
        echo -e "${RED}无效的选择${NC}"
      fi
      ;;
      
    2)
      # 恢复单个Pod的控制器配置
      if [ ${#DEBUG_PODS[@]} -eq 0 ]; then
        echo -e "${YELLOW}没有找到调试Pod可恢复${NC}"
        return 0
      fi
      
      read -p "输入要恢复的Pod编号 (P1-P${#DEBUG_PODS[@]}): " pod_number
      
      # 移除前缀P
      pod_number=${pod_number#P}
      
      if [[ "$pod_number" =~ ^[0-9]+$ ]] && [ "$pod_number" -ge 1 ] && [ "$pod_number" -le ${#DEBUG_PODS[@]} ]; then
        local selected_pod=${DEBUG_PODS[$((pod_number-1))]}
        
        echo -e "${YELLOW}您选择了: ${selected_pod}${NC}"
        read -p "确认恢复此Pod的控制器配置? (yes/no): " confirm
        
        if [ "$confirm" = "yes" ]; then
          restore_debug_pod_controller "$selected_pod" "$namespace"
        else
          echo -e "${YELLOW}操作已取消${NC}"
        fi
      else
        echo -e "${RED}无效的选择${NC}"
      fi
      ;;
    
    3)
      # 删除单个调试控制器
      if [ "$has_controllers" = false ]; then
        echo -e "${YELLOW}没有找到调试控制器可删除${NC}"
        return 0
      fi
      
      read -p "输入要删除的控制器编号 (C1-C${#DEBUG_CONTROLLERS[@]}): " ctrl_number
      
      # 移除前缀C
      ctrl_number=${ctrl_number#C}
      
      if [[ "$ctrl_number" =~ ^[0-9]+$ ]] && [ "$ctrl_number" -ge 1 ] && [ "$ctrl_number" -le ${#DEBUG_CONTROLLERS[@]} ]; then
        local selected_ctrl=${DEBUG_CONTROLLERS[$((ctrl_number-1))]}
        
        echo -e "${YELLOW}您选择了: ${selected_ctrl}${NC}"
        echo -e "${RED}警告: 删除控制器将删除所有关联的Pod${NC}"
        read -p "确认删除此控制器? (yes/no): " confirm
        
        if [ "$confirm" = "yes" ]; then
          delete_debug_controller "$selected_ctrl" "$namespace"
        else
          echo -e "${YELLOW}操作已取消${NC}"
        fi
      else
        echo -e "${RED}无效的选择${NC}"
      fi
      ;;
      
    9)
      # 删除所有调试Pod
      delete_all_debug_pods "$namespace"
      ;;
      
    0)
      # 删除所有调试控制器
      if [ "$has_controllers" = false ]; then
        echo -e "${YELLOW}没有找到调试控制器可删除${NC}"
        return 0
      fi
      delete_all_debug_controllers "$namespace"
      ;;
      
    q|Q|*)
      echo -e "${YELLOW}退出操作${NC}"
      ;;
  esac
  
  return 0
}

# 命令行接口
if [[ "$0" == "${BASH_SOURCE[0]}" ]]; then
  # 脚本被直接执行而不是被导入
  check_dependencies
  
  # 解析命令行参数
  COMMAND=${1:-"list"}
  NAMESPACE=${2:-"oa-llm"}
  POD_NAME=$3
  
  case "$COMMAND" in
    list)
      # 列出所有调试Pod
      list_debug_pods "$NAMESPACE"
      ;;
    list-controllers)
      # 列出所有调试控制器
      check_debug_controllers "$NAMESPACE"
      ;;
    delete)
      # 删除指定的调试Pod
      if [ -z "$POD_NAME" ]; then
        echo -e "${RED}错误: 需要指定Pod名称${NC}"
        echo "用法: $0 delete <namespace> <pod_name>"
        exit 1
      fi
      delete_debug_pod "$POD_NAME" "$NAMESPACE"
      ;;
    delete-controller)
      # 删除指定的调试控制器
      if [ -z "$POD_NAME" ]; then
        echo -e "${RED}错误: 需要指定控制器名称${NC}"
        echo "用法: $0 delete-controller <namespace> <controller_name>"
        exit 1
      fi
      delete_debug_controller "$POD_NAME" "$NAMESPACE"
      ;;
    restore)
      # 恢复调试Pod的控制器配置
      if [ -z "$POD_NAME" ]; then
        echo -e "${RED}错误: 需要指定Pod名称${NC}"
        echo "用法: $0 restore <namespace> <pod_name>"
        exit 1
      fi
      restore_debug_pod_controller "$POD_NAME" "$NAMESPACE"
      ;;
    delete-all)
      # 删除所有调试Pod
      delete_all_debug_pods "$NAMESPACE" "$POD_NAME" # 第三个参数是force标志
      ;;
    delete-all-controllers)
      # 删除所有调试控制器
      delete_all_debug_controllers "$NAMESPACE" "$POD_NAME" # 第三个参数是force标志
      ;;
    interactive|manage)
      # 交互式管理调试Pod
      manage_debug_pods_interactive "$NAMESPACE"
      ;;
    *)
      echo -e "${RED}未知命令: $COMMAND${NC}"
      echo "支持的命令: list, list-controllers, delete, delete-controller, restore, delete-all, delete-all-controllers, interactive/manage"
      echo "用法: $0 <command> [namespace] [pod_name|controller_name]"
      exit 1
      ;;
  esac
fi 