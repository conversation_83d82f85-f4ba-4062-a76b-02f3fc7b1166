{"model_name": "embedding-service-bge-m3", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "embedding/embedding-service-bge-m3:x86.python3.10.v1", "host_port": "8101", "container_port": "8080", "api_endpoint": "/Rag/BgeM3Embedding", "base_model": "BAAI/bge-m3", "host_logs_dir": "{{base_dir}}/embedding/embedding-service-bge-m3/logs", "host_shells_dir": "{{base_dir}}/embedding/embedding-service-bge-m3/shells", "container_logs_dir": "/app/logs", "container_shells_dir": "/app/shells", "restart_script": "{{base_dir}}/embedding/embedding-service-bge-m3/restart.sh", "test_script": "{{base_dir}}/embedding/embedding-service-bge-m3/test_curl.sh", "env_vars": [{"name": "TZ", "value": "Asia/Shanghai"}], "test_commands": ["curl -X POST \"http://localhost:8101/Rag/BgeM3Embedding\" -H 'Content-Type: application/json' -d '{\"text\":\"这是一段测试文本，用来验证BGE-M3 embedding服务是否正常工作\"}'", "curl -X GET \"http://localhost:8101/health\""]}