version: '3'

services:
  nginx:
    image: m.daocloud.io/docker.io/nginx:1.27.4
    container_name: nginx
    ports:
      - "${NGINX_PORT:-30912}:80"
    volumes:
      - ${BASE_DIR:-/data/nginx}/conf/nginx.conf:/etc/nginx/nginx.conf
      - ${BASE_DIR:-/data/nginx}/html:/usr/share/nginx/html
      - ${BASE_DIR:-/data/nginx}/log:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    restart: always
    networks:
      - jiutianwensi-network

networks:
  jiutianwensi-network:
    external: true 