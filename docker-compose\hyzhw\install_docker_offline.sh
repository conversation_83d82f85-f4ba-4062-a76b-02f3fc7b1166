#!/bin/bash

# 离线安装 Docker 脚本
# 假设 docker-24.0.9.tgz 已经在 /usr/local 目录下

DOCKER_VERSION="24.0.9"
DOCKER_TGZ="docker-${DOCKER_VERSION}.tgz"
INSTALL_DIR="/usr/local"
BIN_DIR="/usr/bin"

set -e

echo "开始离线安装 Docker..."

# 1. 解压二进制包
cd ${INSTALL_DIR}
tar -xzvf ${DOCKER_TGZ}

# 2. 拷贝二进制文件到 /usr/bin
cp -f docker/* ${BIN_DIR}/

# 3. 配置 systemd 服务
cat > /etc/systemd/system/docker.service <<EOF
[Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network-online.target firewalld.service
Wants=network-online.target

[Service]
Type=notify
ExecStart=/usr/bin/dockerd 
ExecReload=/bin/kill -s HUP \$MAINPID
TimeoutSec=0
RestartSec=2
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 4. 创建 Docker 配置
mkdir -p /etc/docker
cat > /etc/docker/daemon.json <<EOF
{
    "data-root": "/data/docker",
    "icc": true
}
EOF

# 5. 启动 Docker 服务
systemctl daemon-reload
systemctl start docker
systemctl enable docker

# 6. 验证
docker --version
docker info

echo "Docker 离线安装完成！"
