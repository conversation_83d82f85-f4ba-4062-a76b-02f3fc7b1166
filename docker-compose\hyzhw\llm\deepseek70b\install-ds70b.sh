#!/bin/bash
# DeepSeek-70B 镜像部署一键脚本（适用于麒麟/ARM环境）

# ========== 变量定义 ==========
IMAGE_NAME="swr.cn-south-1.myhuaweicloud.com/ascendhub/mindie:2.0.T3.1-800I-A2-py311-openeuler24.03-lts"                             # 镜像名（可用 docker images 查看）
CONTAINER_NAME="deepseek70b-server"                                 # 容器名称
WEIGHTS_PATH="/home/<USER>/DeepSeek-R1-Distill-Llama-70B"           # 权重文件主机路径
MODEL_WEIGHT_PATH="/data/DeepSeek-R1-Distill-Llama-70B"      # 容器内权重路径
DRIVER_PATH="/usr/local/Ascend/driver"                       # Ascend驱动路径
MINDIE_PATH="/usr/local/Ascend/mindie/latest/mindie-service" # mindie服务路径
# 主机配置文件路径
START_PATH="/data/deepseek70b/shells/start.sh"
# 主机配置文件路径
CONFIG_PATH="/data/deepseek70b/config/config.json"
# 日志路径
LOG_PATH="/data/deepseek70b/logs"
mkdir -p ${LOG_PATH}
chmod 750 ${LOG_PATH}
INIT_LOG_PATH="/data/deepseek70b/init-logs"
mkdir -p ${INIT_LOG_PATH}
chmod 640 ${INIT_LOG_PATH}

# 容器内配置文件路径
CONTAINER_CONFIG_PATH="/usr/local/Ascend/mindie/latest/mindie-service/conf/config.json"
# 容器内启动脚本路径
CONTAINER_START_PATH="/usr/local/Ascend/mindie/latest/mindie-service/bin/start.sh"
# 容器内日志路径
CONTAINER_LOG_PATH="/usr/local/Ascend/mindie/latest/mindie-service/logs"
# 容器内初始化日志路径
CONTAINER_INIT_LOG_PATH="/root/mindie/log/debug/"

echo "容器配置文件路径：${CONTAINER_CONFIG_PATH}"
echo "主机配置文件路径：${CONFIG_PATH}"
echo "启动脚本路径：${START_PATH}"
echo "容器启动脚本路径：${CONTAINER_START_PATH}"

# 如果容器存在，则删除
if docker ps -a | grep -q ${CONTAINER_NAME}; then
  echo "容器存在，删除容器..."
  echo "docker rm -f ${CONTAINER_NAME}"
  docker rm -f ${CONTAINER_NAME}
fi

# ========== 2. 启动容器 ==========
echo "启动容器..."
echo "docker run -itd --privileged=true \
  --net=jiutianwensi-network --shm-size=10g \
  -p 1025:1025 \
  --name ${CONTAINER_NAME} \
  --device=/dev/davinci_manager \
  --device=/dev/hisi_hdc \
  --device=/dev/devmm_svm \
  --device=/dev/davinci0 \
  --device=/dev/davinci1 \
  --device=/dev/davinci2 \
  --device=/dev/davinci3 \
  --device=/dev/davinci4 \
  --device=/dev/davinci5 \
  --device=/dev/davinci6 \
  --device=/dev/davinci7 \
  -v ${DRIVER_PATH}:${DRIVER_PATH}:ro \
  -v /usr/local/sbin:/usr/local/sbin:ro \
  -v ${WEIGHTS_PATH}:${MODEL_WEIGHT_PATH}:ro \
  -v ${START_PATH}:${CONTAINER_START_PATH} \
  -v ${CONFIG_PATH}:${CONTAINER_CONFIG_PATH}:ro \
  -v ${LOG_PATH}:${CONTAINER_LOG_PATH} \
  -v ${INIT_LOG_PATH}:${CONTAINER_INIT_LOG_PATH} \
  ${IMAGE_NAME} /bin/bash ${CONTAINER_START_PATH}"
docker run -itd  \
  --net=jiutianwensi-network --shm-size=10g --privileged=true \
  --name ${CONTAINER_NAME} \
  -p 1025:1025 \
  --device=/dev/davinci_manager \
  --device=/dev/hisi_hdc \
  --device=/dev/devmm_svm \
  --device=/dev/davinci0 \
  --device=/dev/davinci1 \
  --device=/dev/davinci2 \
  --device=/dev/davinci3 \
  --device=/dev/davinci4 \
  --device=/dev/davinci5 \
  --device=/dev/davinci6 \
  --device=/dev/davinci7 \
  -v ${DRIVER_PATH}:${DRIVER_PATH}:ro \
  -v /usr/local/sbin:/usr/local/sbin:ro \
  -v ${WEIGHTS_PATH}:${MODEL_WEIGHT_PATH} \
  -v ${START_PATH}:${CONTAINER_START_PATH} \
  -v ${CONFIG_PATH}:${CONTAINER_CONFIG_PATH} \
  -v ${LOG_PATH}:${CONTAINER_LOG_PATH} \
  -v ${INIT_LOG_PATH}:${CONTAINER_INIT_LOG_PATH} \
  ${IMAGE_NAME} /bin/bash ${CONTAINER_START_PATH}



echo "查看日志："
echo "docker logs -f  ${CONTAINER_NAME}"
docker logs -f  ${CONTAINER_NAME}

#查看最新的启动日志，日志文件格式： mindie-server_549_202506101146.log，549是进程id，202506101146是时间戳
# 获取最新的日志文件名，日志在/data/deepseek70b/init-logs/目录下
echo "查看最新的启动日志："
LATEST_LOG=$(ls -t ${INIT_LOG_PATH}/mindie-server_* 2>/dev/null | head -n 1)
if [ -n "$LATEST_LOG" ]; then
    echo "最新日志文件: $LATEST_LOG"
    echo "日志内容:"
    tail -n 200 "$LATEST_LOG"
else
    echo "未找到日志文件，请检查日志目录: ${INIT_LOG_PATH}"
fi

echo "全部流程已完成，请根据提示完成后续操作。"

echo "curl 127.0.0.1:1025/generate -d '{\"prompt\": \"你是什么模型\", \"max_tokens\": 32, \"stream\": false, \"do_sample\":true, \"repetition_penalty\": 1.00, \"temperature\": 0.01, \"top_p\": 0.001, \"top_k\": 1, \"model\": \"deepseek-70b\"}'"

curl 127.0.0.1:1025/generate -d '{"prompt": "你是什么模型", "max_tokens": 32, "stream": false, "do_sample":true, "repetition_penalty": 1.00, "temperature": 0.01, "top_p": 0.001, "top_k": 1, "model": "deepseek-70b"}'
