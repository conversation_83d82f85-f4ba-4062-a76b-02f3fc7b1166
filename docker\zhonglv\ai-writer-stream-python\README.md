# AI Writer Stream Python

基于流式输出的自动文章生成系统的Docker部署方案

## 项目结构

```
ai-writer-stream-python/
├── web_source_code/           # 项目源代码
│   ├── source_app/            # 应用源码
│   │   ├── views.py           # 视图文件
│   │   └── config.py          # 配置文件
│   ├── local_data/            # 本地数据
│   │   └── writing_template/  # 写作模板目录
│   ├── log/                   # 日志目录
│   ├── manage.py              # Django管理脚本
│   └── ...
├── Dockerfile                 # Docker镜像定义
├── config.py                  # 配置文件
├── deploy.sh                  # 部署脚本
├── restart.sh                 # 重启脚本
├── start.sh                   # 容器内启动脚本
├── test_curl.sh               # API测试脚本
└── requirements.txt           # 项目依赖
```

## 快速部署

### 方法1：使用部署脚本

```bash
# 添加执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

部署脚本会执行以下操作：
- 创建项目目录结构
- 复制配置文件和启动脚本到相应位置
- 构建Docker镜像
- 启动容器
- 设置日志和数据目录权限
- 执行API测试脚本

### 方法2：使用重启脚本（容器已存在）

```bash
# 添加执行权限
chmod +x restart.sh

# 运行重启脚本
./restart.sh
```

重启脚本会执行以下操作：
- 反向复制配置和脚本文件到构建目录
- 停止并删除现有容器
- 使用已有的镜像重新启动容器
- 设置日志和数据目录权限
- 执行API测试脚本

## 配置说明

配置文件位于 `config.py`，包含以下主要配置项：
- 模型配置（APPID, APPKEY, CNAME）
- 模型服务URL
- API密钥
- 本地数据路径
- FAISS向量库数据路径

配置文件通过卷挂载方式映射到容器内部，可以直接在宿主机上修改配置文件，无需重新构建镜像。

## 目录挂载

- 配置文件：`/data/ai-writer/ai-writer-stream-python/config/config.py:/app/web_source_code/source_app/config.py`
- 日志目录：`/data/ai-writer/ai-writer-stream-python/logs:/app/web_source_code/log`
- Shell脚本目录：`/data/ai-writer/ai-writer-stream-python/shells:/app/shells`

## 端口说明

应用默认在以下端口提供服务：
- 容器内部：8080
- 宿主机映射：8095（deploy.sh）/ 8095（restart.sh）

可以通过修改 deploy.sh 或 restart.sh 中的 PORT 变量来更改宿主机映射端口。

## API测试

项目包含一个测试脚本 `test_curl.sh`，用于验证API功能。该脚本会：
- 测试健康检查接口
- 测试流式文章生成接口（SSE格式）
- 提供流式数据处理示例

测试命令示例：
```bash
./test_curl.sh
```

## 网络配置

容器默认使用 `jiutianwensi` 网络，可以根据实际环境需要在部署脚本中修改。 