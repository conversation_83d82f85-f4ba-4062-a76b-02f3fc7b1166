version: '3.5'

services:
  minio:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/minio:arm64-RELEASE.2023-04-28T18-11-17Z
    container_name: minio
    environment:
      - MINIO_ROOT_USER=admin
      - MINIO_ROOT_PASSWORD=MiniO@2025
    volumes:
      - ./minio/data:/data
      - ./minio/logs:/var/log/minio
    ports:
      - 9000:9000
      - 9001:9001
    command: server /data --console-address ":9001"
    networks:
      - jiutianwensi-network
    restart: unless-stopped
  opensearch:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch:arm64-2.9.0
    container_name: opensearch
    ports:
      - "9201:9200"
      - "9600:9600"
    environment:
      - TZ=Asia/Shanghai
      - OPENSEARCH_JVM_HEAP=2g
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=QmffyH1zxSWE5Nke
    volumes:
      - ./opensearch/data:/usr/share/opensearch/data
      - ./opensearch/logs:/usr/share/opensearch/logs
    ulimits:
      memlock:
        soft: -1
        hard: -1
    networks:
      - jiutianwensi-network
    restart: unless-stopped
  opensearch-dashboards:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch-dashboards:arm64-2.9.0
    container_name: opensearch-dashboards
    ports:
      - "5602:5601"
    environment:
      - TZ=Asia/Shanghai
      - OPENSEARCH_HOSTS=https://opensearch:9200
    networks:
      - jiutianwensi-network
    restart: unless-stopped
    depends_on:
      - opensearch
  postgres:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/postgres:arm64-16.2
    container_name: postgres-server
    ports:
      - "5433:5432"
    environment:
      - TZ=Asia/Shanghai
      - POSTGRES_PASSWORD=PgSQL@2025
    volumes:
      - ./postgresql/data:/var/lib/postgresql/data
      - ./postgresql/logs:/var/log/postgresql
      - ./postgresql/config:/etc/postgresql
    command: -c log_destination=stderr -c logging_collector=on
    networks:
      - jiutianwensi-network
    restart: unless-stopped
  redis:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/redis:arm64-7.4.1
    container_name: redis-server
    ports:
      - "6379:6379"
    volumes:
      - ./redis/config/redis.conf:/etc/redis/redis.conf
      - ./redis/data:/data
    command: redis-server /etc/redis/redis.conf
    networks:
      - jiutianwensi-network
    restart: unless-stopped
  rabbitmq:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/rabbitmq:arm64-3.12-management
    container_name: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - ./rabbitmq/data:/var/lib/rabbitmq
      - ./rabbitmq/logs:/var/log/rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
      - RABBITMQ_DEFAULT_VHOST=jtws
    networks:
      - jiutianwensi-network
    restart: unless-stopped
    command: >
      bash -c "rabbitmq-plugins enable --offline rabbitmq_management && rabbitmq-server"

networks:
  jiutianwensi-network:
    name: jiutianwensi-network