{"middleware_name": "kibana", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "docker.elastic.co/kibana/kibana:7.17.14", "host_port": "5601", "container_port": "5601", "username": "elastic", "password": "Elastic20250417@#", "host_logs_dir": "{{base_dir}}/kibana/logs", "host_config_dir": "{{base_dir}}/kibana/config", "host_data_dir": "{{base_dir}}/kibana/data", "container_config_dir": "/usr/share/kibana/config", "container_data_dir": "/usr/share/kibana/data", "restart_script": "{{base_dir}}/kibana/restart.sh", "test_script": "{{base_dir}}/kibana/test_curl.sh", "env_vars": [{"name": "ELASTICSEARCH_USERNAME", "value": "elastic"}, {"name": "ELASTICSEARCH_PASSWORD", "value": "Elastic20250417@#"}], "config": {"server.name": "kibana", "server.host": "0.0.0.0", "elasticsearch.hosts": ["http://elasticsearch:9200"], "elasticsearch.username": "elastic", "elasticsearch.password": "Elastic20250417@#", "i18n.locale": "zh-CN"}, "test_commands": ["curl -s http://localhost:5601/api/status | grep -q '\"overall\":{\"level\":\"available\"'"]}