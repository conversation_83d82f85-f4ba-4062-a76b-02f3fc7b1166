{"model_name": "embedding-service-bge-large-zh", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "embedding/embedding-service-bge-large-zh:x86.python3.10.v1", "host_port": "8104", "container_port": "8080", "api_endpoint": "/Rag/BgeEmbeddingService", "base_model": "BAAI/bge-large-zh-v1.5", "host_logs_dir": "{{base_dir}}/embedding/embedding-service-bge-large-zh/logs", "host_shells_dir": "{{base_dir}}/embedding/embedding-service-bge-large-zh/shells", "container_logs_dir": "/app/logs", "container_shells_dir": "/app/shells", "restart_script": "{{base_dir}}/embedding/embedding-service-bge-large-zh/restart.sh", "test_script": "{{base_dir}}/embedding/embedding-service-bge-large-zh/test_curl.sh", "env_vars": [{"name": "TZ", "value": "Asia/Shanghai"}], "test_commands": ["curl -X POST \"http://localhost:8104/Rag/BgeEmbeddingService\" -H 'Content-Type: application/json' -d '{\"text\":\"这是一段测试文本，用来验证BGE-Large-ZH embedding服务是否正常工作\"}'", "curl -X GET \"http://localhost:8104/health\""]}