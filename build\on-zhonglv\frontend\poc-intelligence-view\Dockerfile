# 使用DaoCloud的nginx镜像
FROM m.daocloud.io/docker.io/nginx:1.27.4

# 设置时区
ENV TZ=Asia/Shanghai

# 创建目录
RUN mkdir -p /usr/share/nginx/html/poc-intelligence-view

# 复制编译好的前端文件
COPY poc-intelligence-view/ /usr/share/nginx/html/poc-intelligence-view/

RUN chmod -R 755 /usr/share/nginx/html

# 设置nginx配置文件
RUN ls -l /usr/share/nginx/html/poc-intelligence-view


# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"] 