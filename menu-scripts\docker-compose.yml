version: '3'

services:
  ai-doc-poc:
    image: hegao/ai-doc-poc:0.0.1-SNAPSHOT
    container_name: ai-doc-poc
    ports:
      - "8093:8080"
    volumes:
      - ${BASE_DIR:-/data/hegao/ai-doc-poc}/data:/app/files
      - ${BASE_DIR:-/data/hegao/ai-doc-poc}/logs:/app/logs
      - ${BASE_DIR:-/data/hegao/ai-doc-poc}/config:/app/config
      - ${BASE_DIR:-/data/hegao/ai-doc-poc}/shells:/app/shells
    environment:
      - JAVA_OPTS=-Xms512m -Xmx1g
    networks:
      - jiutianwenshi-network
    restart: always
  
networks:
  jiutianwenshi-network:
    driver: bridge 