{"Version": "1.0.0", "LogConfig": {"logLevel": "Info", "logFileSize": 20, "logFileNum": 20, "logPath": "/usr/local/Ascend/mindie/latest/mindie-service/logs"}, "ServerConfig": {"ipAddress": "0.0.0.0", "managementIpAddress": "*********", "port": 1025, "managementPort": 1026, "metricsPort": 1027, "allowAllZeroIpListening": true, "maxLinkNum": 1000, "httpsEnabled": false, "fullTextEnabled": false, "tlsCaPath": "security/ca/", "tlsCaFile": ["ca.pem"], "tlsCert": "security/certs/server.pem", "tlsPk": "security/keys/server.key.pem", "tlsPkPwd": "security/pass/key_pwd.txt", "tlsCrlPath": "security/certs/", "tlsCrlFiles": ["server_crl.pem"], "managementTlsCaFile": ["management_ca.pem"], "managementTlsCert": "security/certs/management/server.pem", "managementTlsPk": "security/keys/management/server.key.pem", "managementTlsPkPwd": "security/pass/management/key_pwd.txt", "managementTlsCrlPath": "security/management/certs/", "managementTlsCrlFiles": ["server_crl.pem"], "kmcKsfMaster": "tools/pmt/master/ksfa", "kmcKsfStandby": "tools/pmt/standby/ksfb", "inferMode": "standard", "interCommTLSEnabled": true, "interCommPort": 1121, "interCommTlsCaPath": "security/grpc/ca/", "interCommTlsCaFiles": ["ca.pem"], "interCommTlsCert": "security/grpc/certs/server.pem", "interCommPk": "security/grpc/keys/server.key.pem", "interCommPkPwd": "security/grpc/pass/key_pwd.txt", "interCommTlsCrlPath": "security/grpc/certs/", "interCommTlsCrlFiles": ["server_crl.pem"], "openAiSupport": "vllm"}, "BackendConfig": {"backendName": "mindieservice_llm_engine", "modelInstanceNumber": 1, "npuDeviceIds": [[0, 1, 2, 3, 4, 5, 6, 7]], "tokenizerProcessNumber": 8, "multiNodesInferEnabled": false, "multiNodesInferPort": 1120, "interNodeTLSEnabled": true, "interNodeTlsCaPath": "security/grpc/ca/", "interNodeTlsCaFiles": ["ca.pem"], "interNodeTlsCert": "security/grpc/certs/server.pem", "interNodeTlsPk": "security/grpc/keys/server.key.pem", "interNodeTlsPkPwd": "security/grpc/pass/mindie_server_key_pwd.txt", "interNodeTlsCrlPath": "security/grpc/certs/", "interNodeTlsCrlFiles": ["server_crl.pem"], "interNodeKmcKsfMaster": "tools/pmt/master/ksfa", "interNodeKmcKsfStandby": "tools/pmt/standby/ksfb", "ModelDeployConfig": {"maxSeqLen": 32768, "maxInputTokenLen": 16384, "truncation": false, "tokenTimeout": 10000, "ModelConfig": [{"modelInstanceType": "Standard", "modelName": "DeepSeek-70B", "modelWeightPath": "/data/DeepSeek-R1-Distill-Llama-70B", "worldSize": 8, "cpuMemSize": 5, "npuMemSize": -1, "backendType": "atb", "trustRemoteCode": false}]}, "ScheduleConfig": {"templateType": "Standard", "templateName": "Standard_LLM", "cacheBlockSize": 128, "maxPrefillBatchSize": 50, "maxPrefillTokens": 32768, "prefillTimeMsPerReq": 150, "prefillPolicyType": 0, "decodeTimeMsPerReq": 50, "decodePolicyType": 0, "maxBatchSize": 200, "maxIterTimes": 16384, "maxPreemptCount": 0, "supportSelectBatch": false, "maxQueueDelayMicroseconds": 5000}}}