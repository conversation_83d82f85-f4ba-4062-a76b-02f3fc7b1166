version: '3'

services:
  postgres-server:
    image: m.daocloud.io/docker.io/postgres:12.18
    container_name: postgres-server
    hostname: postgres
    ports:
      - "5432:5432"
    volumes:
      - ${BASE_DIR:-/data/postgresql}/data:/var/lib/postgresql/data
      - ${BASE_DIR:-/data/postgresql}/logs:/var/log/postgresql
      - ${BASE_DIR:-/data/postgresql}/config:/etc/postgresql
      - ${BASE_DIR:-/data/postgresql}/schema/postgresql.sql:/docker-entrypoint-initdb.d/postgresql.sql
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-admin}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-PgSQL@2025}
      - PGDATA=/var/lib/postgresql/data/pgdata
      - TZ=Asia/Shanghai
    command: -c "log_destination=stderr" -c "logging_collector=on"
    restart: always
    networks:
      - jiutianwensi-network

networks:
  jiutianwensi-network:
    external: true 