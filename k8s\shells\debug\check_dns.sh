#! /bin/bash

# 检查容器内部的dns是否正常
# 获取当前oa-llm中所有pod的名称
POD_NAMES=$(kubectl get pod -n oa-llm -o jsonpath='{.items[*].metadata.name}')

# 遍历所有pod，相互之间的ping是否正常，
for POD_NAME in $POD_NAMES; do
    # 获取当前pod的ip
    POD_IP=$(kubectl get pod $POD_NAME -n oa-llm -o jsonpath='{.status.podIP}')
    for OTHER_POD_NAME in $POD_NAMES; do
        # 如果pod名称相同，则跳过
        if [ "$POD_NAME" == "$OTHER_POD_NAME" ]; then
            continue
        fi
        # 获取其他pod的ip
        OTHER_POD_IP=$(kubectl get pod $OTHER_POD_NAME -n oa-llm -o jsonpath='{.status.podIP}')
        echo "检查pod $POD_NAME 和 $OTHER_POD_NAME 的ping是否正常"
        kubectl exec -it $POD_NAME -n oa-llm -- sh -c "ping -c 4 $OTHER_POD_IP"
        # 如果ping不通，则打印出错信息
        if [ $? -ne 0 ]; then
            echo "pod $POD_NAME 和 $OTHER_POD_NAME 的ping不通"
        else
            echo "pod $POD_NAME 和 $OTHER_POD_NAME 的ping正常"
        fi
    done
done