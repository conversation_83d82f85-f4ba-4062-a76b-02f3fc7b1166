#!/usr/bin/env python
# coding=utf-8

import json
import time

import requests


def myclient(text, history):
    url = "http://localhost:7890/Rag/QAOnlineSSE"


    data = json.dumps(parameter)
    headers = {"Content-Type": "application/json"}
    response = requests.post(url, headers=headers, data=data, stream=True)
    response.status_code
    return requests.post(url, headers=headers, data=data, stream=True)


if __name__ == "__main__":

    texts = [
        "研发项目立项有哪些环节",
        "具体规定是什么？",
        "现在有多少项目",
        "立项完成的呢？",
        "今年的流程有什么变化",
        "流程如何规定的呢",
        "怎么做呢？",
        "有没有具体的流程要求",
        "流程有多少个呢？",
        "研发项目有多少个？",
        "明年的研发项目有多少个？",
    ]
    texts = ['planing是什么',
             '有哪些场景',
             '意图识别有哪些场景',
             'ai agent是什么',
             '有哪些场景',
             'prompt chain',
             '意图识别的应用',
             '什么是意图识别',
             '员工退休后办理哪些手续',
             '退休人员的补贴',
             '薪酬管理办法制定的原则',
             '什么是人工智能安全治理框架',
             '治理原则是什么',
             '安全风险有哪些',
             '治理的措施',
             '综合治理措施',
             '广州、深圳的住宿标准',
             '那重庆呢',
             '移动党校的位置在哪',
             '去的方式',
             '有几座教学楼']
    history = []
    for text in texts:
        print("query:", text)
        answer = ""
        for d in myclient(text=text, history=history).iter_lines():
            val = d.decode("utf-8").split("data:")[-1]
            if not val:
                continue
            answer = json.loads(val)["result"]["text"]
        print(answer)
        history.append(
            {
                "question": text,
                "answer": answer,
                "intents": [{"intent": "largeLanguageModel", "slots": []}],
            }
        )
        history = history[-5:]
        print("-" * 30)