# 接口测试报告

## 测试环境

- 测试环境：http://*************:1025/v1/chat/completions
- 测试模型：Qwen/Qwen2.5-72B-Instruct

## 测试结果

### 核稿测试

#### ai-redactor到大模型

- 测试脚本：ai-redactor/shells/test.sh
- 测试结果：
``` shell
[root@aiserver04 jtws-host]# docker exec -it ai-redactor bash
(base) root@aiserver04:/app# cd shells/
(base) root@aiserver04:/app/shells# sh test.sh
-e === 大模型接口测试 ===
model_url: http://localhost:1025/v1/chat/completions
model_name: DeepSeek-R1-Distill-Llama-70B
api_key:
-e 执行命令:
curl -v -X POST "http://localhost:1025/v1/chat/completions"       -H "Content-Type: application/json"       -H "Authorization: Bearer "       -d '{
      "model": "DeepSeek-R1-Distill-Llama-70B",
      "messages": [
        {
          "role": "system",
          "content": "你是一个专业的文本纠错助手。请对输入的文本进行纠错，确保输出符合以下要求：

任务说明：
你需要对输入的文本进行纠错，主要处理以下几类错误：

1. 词语错别字
    - 修正词语中的错别字
    - 示例：[我觉得很星福。] → [我觉得很幸福。]

2. 拼写错误
    - 将拼音转换为正确的汉字
    - 示例：[你hao！] → [你好！]
    - 示例：[我们需要yuan助。] → [我们需要援助。]

3. 多余字
    - 删除文本中多余的字
    - 示例：[今天真开的心。] → [今天真开心。]

4. 缺失字
    - 补充文本中缺失的字
    - 示例：[请各单组织学习青年大学习] → [请各单位组织学习青年大学习]

5. 标点符号错误
    - 修正错误的标点符号
    - 示例：[今天天气真好.] → [今天天气真好。]

6. 语法错误
    - 搭配不当：修正动词宾语、主语宾语、修饰语、主语谓语搭配错误
    - 语序问题：修正动作逻辑顺序、时间状语位置、修饰语位置、关联词位置错误
    - 语义矛盾：修正语义不一致的内容
    - 副词冗余：删除多余的副词
    - 成分缺失：补充缺失的句子成分

输出要求：
1. 如果文本存在错误，请进行纠错并以JSON格式输出：
    {\"output\": \"纠错后的文本\"}

2. 如果文本没有错误，请直接以JSON格式输出原文：
    {\"output\": \"原文\"}

3. 只输出JSON格式的结果，不要添加任何其他信息。"
        },
        {
          "role": "user",
          "content": "标题：设立中蒙二连浩特扎门乌德经济合作区的批复
根据\"三个一百年\"奋斗目标的宏伟蓝图和\"四个代表\"重要思想的指导精神，我们对中蒙两国在经贸领域深化合作、共同推动区域发展的努力表示肯定和支持。你们《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号）收悉。现批复如下：
一、同意设立中蒙二连浩特—扎门乌德经济合作区（以下简称合作区）。合作区中方区域面积9.03平方公里，位于内蒙古自治区二连浩特市中蒙边境公路口岸西北恻，四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至边防哨所600米，北至边防禁区南边界。合作区中方区域分东、西两区，按功能定位实行分区监管，并在区域间设立隔离设施。其中，东区面积4.31平方公里，四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至西经1路，北至边防禁区南边界；西区面积4.7二平方公里，四至范围为：东至西经1路，南至边防部队网围栏，西至边防哨所6百米，北至边防禁区南边界。合作区中方区域范围具体以界址点坐标控制，界址点坐标由商务部、自然资源部负责发布。
二、合作区中方区域建设要全面贯彻落实党的二十大精神，按照党中央决策部署，立足本地特色优势，重点发展国际贸易、、国际物流、进出口加工、跨境旅游及相关配套服务，深入推进国际经茂合作，打造沿边地区的高水平开放平台 、一带一辂中蒙俄经济走廊的重要节点、中蒙友好合作的典范，服务构建新发展格局、推动高质量发展.
三、合作区中方区域通过物理围网和信息化监管实行封闭管理。按中蒙政府间协议约定，与蒙方合作设立双方区域间的跨境设施，实施边防、海关检查，以及相关查验、检验检疫、安检等方面监管，有序推进与蒙方区域的人员、货物便利通行。
三、同意对合作区中方区域实施相关支持政策。支持建设国际医疗先行区，鼓励医疗新技术、新药品研发应用。支持研究规划建设合作区跨境铁路专用线。允许开展国际文化艺术品展示、拍卖、交易业务。地方通过外经贸发展专项资金等现有资金渠道，统筹支持合作区发展。支持配套金融服务，鼓励中资和外资银行机构入驻，知持民间资本进入区内金融业，支持依法合规开展跨境投融资业务和个人本外币兑换业务，扩大人民币跨境使用。允许引入境外优质教育资源，开展高水平国际教育交流合作。支持确有需求时按程序设立综合保税区。
五、内蒙古自治区人民政府要加强组织领导，切实落实主体责任，创新行政管理体制，科学设置合作区职能机构，提高行政效率和服务水平。严格尊循国土空间规划，按规定程序履行具体用地报批手续;严格执行项目建设用地控制指标和招标拍卖挂牌出让制度，节约集约利用土地资源。落实生态文明建设要求，依法依规开展规划环评工作，严格建设项目审批，加强自然生态环境和生物多样性保护，促进经济建设和资源环境胁调发展。
六、商务部要会同内蒙古自治区人民政府有序推进合作区双边协调机制建设，协调推动有关部门加强诣导和服务，促进合作区持续健康发展。
七、内蒙古自治区人民政府和商务部等有关部门要认真梳理和研究合作区建设中出现的新情况、新问题，重大事项及时请示报告。
联系人：XXX
联系电话：29111111111
原文：
国务院关于设立中蒙二连浩特—扎门乌德经济合作区的批复
https://www.gov.cn/zhengce/content/202403/content_6940964.htm"
        }
      ]
    }'
Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying 127.0.0.1:1025...
* Connected to localhost (127.0.0.1) port 1025 (#0)
> POST /v1/chat/completions HTTP/1.1
> Host: localhost:1025
> User-Agent: curl/7.88.1
> Accept: */*
> Content-Type: application/json
> Authorization: Bearer
> Content-Length: 5763
>
< HTTP/1.1 200 OK
< Connection: close
< Content-Length: 12411
< Content-Type: application/json
< Keep-Alive: timeout=180, max=2147483647
<
{"id":"endpoint_common_26","object":"chat.completion","created":1749176618,"model":"DeepSeek-R1-Distill-Llama-70B","choices":[{"index":0,"message":{"role":"assistant","content":"嗯，我现在需要处理这个用户的查询。用户提供了一段文本，要求我作为专业的文本纠错助手，对其进行纠错，并按照特定的JSON格式输出结果。首先，我得仔细阅读用户的任务说明，确保我理解所有的纠错类别，包括词语错别字、拼写错误、多余字、缺失字、标点符号错误和语法错误。\n\n接下来，我看看用户提供的文本。这是一份关于设立中蒙二连浩特扎门乌德经济合作区的批复，看起来是一个正式的政府文件。文本内容较多，我需要逐段检查，找出其中的错误。\n\n首先，我会通读全文，了解整体结构和内容，然后逐句检查是否有错别字或拼写错误。例如，文本中有“西北恻”，这可能应该是“西北侧”。还有“4.7二平方公里”中的“二”应该是“平方”，所以应该是“4.72平方公里”。这些都是词语错别字，需要修正。\n\n接下来，检查拼写错误。比如“yuan助”应该是“援助”，但在这个文本中没有类似的拼音错误，所以这一部分可能没有问题。然后，检查是否有多余字或缺失字。例如，文本中是否有重复的字或者缺少必要的字，比如“四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至边防哨所600米，北至边防禁区南边界。” 这部分看起来没有多余或缺失字。\n\n标点符号方面，检查是否有错误的使用。例如，句末是否使用了正确的句号或句末标点。看起来大部分标点都是正确的，但有些地方可能需要调整，比如在“打造沿边地区的高水平开放平台 、一带一辂中蒙俄经济走廊的重要节点”这里，逗号后面可能应该加上顿号，或者检查是否有多余的空格。\n\n语法错误方面，需要检查句子的结构是否正确。例如，“打造沿边地区的高水平开放平台 、一带一辂中蒙俄经济走廊的重要节点”中的“一带一辂”应该是“一带一路”，这是一个明显的错别字。此外，检查是否有搭配不当或语序问题，比如“服务构建新发展格局、推动高质量发展”是否正确，或者是否有更合适的表达方式。\n\n在检查过程中，我还注意到一些重复的内容，比如第三点出现了两次，可能需要删除重复的部分。此外，最后一段联系人和联系电话的格式是否正确，是否需要调整。\n\n完成初步检查后，我会逐一修正这些错误，确保输出符合用户的要求。修正后的文本需要以JSON格式输出，如果没有错误，则直接返回原文。如果有错误，则返回纠正后的版本。\n\n最后，我会再次通读修正后的文本，确保所有错误都已处理，格式正确，没有遗漏。这样，用户就能得到一个准确无误的纠错结果了。\n</think>\n\n{\n    \"output\": \"国务院关于设立中蒙二连浩特—扎门乌德经济合作区的批复\n\n根据“三个一百年”奋斗目标的宏伟蓝图和“四个代表”重要思想的指导精神，我们对中蒙两国在经贸领域深化合作、共同推动区域发展的努力表示肯定和支持。你们《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号）收悉。现批复如下：\n\n一、同意设立中蒙二连浩特—扎门乌德经济合作区（以下简称合作区）。合作区中方区域面积9.03平方公里，位于内蒙古自治区二连浩特市中蒙边境公路口岸西北侧，四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至边防哨所600米，北至边防禁区南边界。合作区中方区域分东、西两区，按功能定位实行分区监管，并在区域间设立隔离设施。其中，东区面积4.31平方公里，四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至西经1路，北至边防禁区南边界；西区面积4.72平方公里，四至范围为：东至西经1路，南至边防部队网围栏，西至边防哨所600米，北至边防禁区南边界。合作区中方区域范围具体以界址点坐标控制，界址点坐标由商务部、自然资源部负责发布。\n\n二、合作区中方区域建设要全面贯彻落实党的二十大精神，按照党中央决策部署，立足本地特色优势，重点发展国际贸易、国际物流、进出口加工、跨境旅游及相关配套服务，深入推进国际经贸合作，打造沿边地区的高水平开放平台、一带一路中蒙俄经济走廊的重要节点、中蒙友好合作的典范，服务构建新发展格局、推动高质量发展。\n\n三、合作区中方区域通过物理围网和信息化监管实行封闭管理。按中蒙政府间协议约定，与蒙方合作设立双方区域间的跨境设施，实施边防、海关检查，以及相关查验、检验检疫、安检等方面监管，有序推进与蒙方区域的人员、货物便利通行。\n\n四、同意对合作区中方区域实施相关支持政策。支持建设国际医疗先行区，鼓励医疗新技术、新药品研发应用。支持研究规划建设合作区跨境铁路专用线。允许开展国际文化艺术品展示、拍卖、交易业务。地方通过外经贸发展专项资金等现有资金渠道，统筹支持合作区发展。支持配套金融服务，鼓励中资和外资银行机构入驻，允持民间资本进入区内金融业，支持依法合规开展跨境投融资业务和个人本外币兑换业务，扩大人民币跨境使用。允许引入境外优质教育资源，开展高水平国际教育交流合作。支持确有需求时按程序设立综合保税区。\n\n五、内蒙古自治区人民政府要加强组织领导，切实落实主体责任，创新行政管理体制，科学设置合作区职能机构，提高行政效率和服务水平。严格遵循国土空间规划，按规定程序履行具体用地报批手续；严格执行项目建设用地控制指标和招标拍卖挂牌出让制度，节约集约利用土地资源。落实生态文明建设要求，依法依规开展规划环评工作，严格建设项目审批，加强自然生态环境和生物多样性保护，促进经济建设和资源环境协调发展。\n\n六、商务部要会同内蒙古自治区人民政府有序推进合作区双边协调机制建设，协调推动有关部门加强指导和服务，促进合作区持续健康发展。\n\n七、内蒙古自治区人民政府和商务部等有关部门要认真梳理和研究合作区建设中出现的新情况、新问题，重大事项及时请示报告。\n\n联系人：XXX\n联系电话：29111111111\"\n}","tool_calls":null},"finish_reason":"stop"}],"usage":{"prompt_tokens":1540,"completion_tokens":1757,"total_tokens":3297},"prefill_time":260,"decode_time_arr":[49,37,37,36,37,37,36,36,36,37,36,37,36,36,36,36,36,37,36,37,37,37,36,37,37,37,43,36,37,205,37,37,37,37,36,36,37,36,37,37,36,36,37,37,37,36,36,36,37,37,36,36,36,36,36,37,37,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,37,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,37,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,37,36,37,36,36,36,36,37,36,36,36,37,36,36,36,36,36,37,39,36,36,36,36,36,36,37,36,36,36,36,36,37,36,36,37,37,36,36,36,36,37,36,36,37,36,37,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,38,37,37,37,37,36,36,36,36,37,36,36,36,36,54,38,37,46,37,36,37,37,37,37,36,36,36,36,36,36,37,36,36,37,36,37,36,36,38,36,38,36,37,36,37,38,37,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,37,36,36,36,36,36,36,36,38,36,37,36,36,36,36,36,36,36,36,36,36,36,36,37,37,37,36,37,37,36,36,37,37,36,37,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,37,37,36,36,36,36,36,36,37,37,37,37,36,36,36,36,37,37,37,37,36,36,36,36,36,36,37,37,37,37,37,37,36,37,36,36,36,37,37,36,37,37,37,37,36,36,37,37,37,36,36,36,36,37,37,36,36,36,36,37,36,37,38,36,38,41,36,36,36,37,37,37,37,37,37,37,37,37,37,36,37,37,36,36,37,37,37,36,36,36,37,36,36,36,37,36,36,36,36,36,36,37,38,36,37,37,197,36,37,36,36,37,37,37,37,37,37,36,36,36,36,39,37,37,36,36,36,36,36,37,37,36,37,36,38,37,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,37,37,36,36,36,36,36,36,36,37,37,37,36,37,38,37,36,37,36,37,37,37,37,36,36,36,37,36,36,36,36,37,37,37,36,36,36,36,36,36,36,36,36,36,36,37,36,37,36,37,37,37,36,36,36,36,37,37,37,37,37,37,37,37,37,37,37,36,36,37,36,37,37,37,37,37,37,37,37,37,37,37,37,36,36,36,37,36,37,37,37,37,37,37,37,37,36,36,90,39,77,39,48,37,37,37,37,36,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,36,37,37,36,36,37,37,37,37,37,37,36,37,37,37,36,37,38,37,37,37,37,37,37,37,37,37,37,38,37,38,37,36,37,37,37,36,37,37,37,36,37,37,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,38,37,37,37,37,36,37,37,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,36,37,38,37,37,37,37,37,36,37,37,36,37,36,36,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,36,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,37,37,37,37,37,40,36,36,36,195,37,37,37,37,37,37,37,37,37,37,37,36,37,37,37,37,37,36,36,36,37,36,37,37,37,37,36,36,36,37,37,37,37,37,38,37,37,37,37,37,37,37,37,37,37,38,37,37,37,37,37,37,37,37,36,37,37,36,37,37,37,37,37,37,37,37,37,36,36,38,37,37,37,37,36,36,36,36,38,37,37,36,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,38,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,37,37,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,38,37,39,38,37,39,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,39,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,39,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,37,37,37,37,37,37,71,38,37,37,49,37,37,37,37,37,37,37,37,38,37,37,37,37,37,37,38,37,37,37,38,37,37,37,37,37,43,37,37,37,37,37,37,37,37,37,37,37,38,37,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,37,37,37,37,37,38,38,199,37,38,37,37,37,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,38,37,37,37,44,37,37,38,37,37,37,38,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,39,39,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,38,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,38,37,37,37,38,37,38,37,38,37,37,37,37,37,37,37,37,37,38,37,37,37,38,37,37,37,37,37,37,38,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,38,38,37,37,37,37,37,38,38,37,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,44,37,37,39,37,38,37,37,37,38,37,37,37,37,37,37,37,37,38,37,37,38,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,39,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,37,37,37,37,37,37,37,37,37,37,38,38,37,37,37,3* Closing connection 0
7,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,38,37,37,38,37,37,37,37,37,37,37,39,37,37,37,37,37,37,37,37,38]}
-e 大模型请求耗时: 67 秒
-e === 本服务接口测试 ===
-e 执行命令:
curl -v -X POST "http://localhost:8000/spellcheck/oa"       -H "Content-Type: application/json"       -d '{
      "content": "标题：设立中蒙二连浩特扎门乌德经济合作区的批复
根据\"三个一百年\"奋斗目标的宏伟蓝图和\"四个代表\"重要思想的指导精神，我们对中蒙两国在经贸领域深化合作、共同推动区域发展的努力表示肯定和支持。你们《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号）收悉。现批复如下：
一、同意设立中蒙二连浩特—扎门乌德经济合作区（以下简称合作区）。合作区中方区域面积9.03平方公里，位于内蒙古自治区二连浩特市中蒙边境公路口岸西北恻，四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至边防哨所600米，北至边防禁区南边界。合作区中方区域分东、西两区，按功能定位实行分区监管，并在区域间设立隔离设施。其中，东区面积4.31平方公里，四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至西经1路，北至边防禁区南边界；西区面积4.7二平方公里，四至范围为：东至西经1路，南至边防部队网围栏，西至边防哨所6百米，北至边防禁区南边界。合作区中方区域范围具体以界址点坐标控制，界址点坐标由商务部、自然资源部负责发布。
二、合作区中方区域建设要全面贯彻落实党的二十大精神，按照党中央决策部署，立足本地特色优势，重点发展国际贸易、、国际物流、进出口加工、跨境旅游及相关配套服务，深入推进国际经茂合作，打造沿边地区的高水平开放平台 、一带一辂中蒙俄经济走廊的重要节点、中蒙友好合作的典范，服务构建新发展格局、推动高质量发展.
三、合作区中方区域通过物理围网和信息化监管实行封闭管理。按中蒙政府间协议约定，与蒙方合作设立双方区域间的跨境设施，实施边防、海关检查，以及相关查验、检验检疫、安检等方面监管，有序推进与蒙方区域的人员、货物便利通行。
三、同意对合作区中方区域实施相关支持政策。支持建设国际医疗先行区，鼓励医疗新技术、新药品研发应用。支持研究规划建设合作区跨境铁路专用线。允许开展国际文化艺术品展示、拍卖、交易业务。地方通过外经贸发展专项资金等现有资金渠道，统筹支持合作区发展。支持配套金融服务，鼓励中资和外资银行机构入驻，知持民间资本进入区内金融业，支持依法合规开展跨境投融资业务和个人本外币兑换业务，扩大人民币跨境使用。允许引入境外优质教育资源，开展高水平国际教育交流合作。支持确有需求时按程序设立综合保税区。
五、内蒙古自治区人民政府要加强组织领导，切实落实主体责任，创新行政管理体制，科学设置合作区职能机构，提高行政效率和服务水平。严格尊循国土空间规划，按规定程序履行具体用地报批手续;严格执行项目建设用地控制指标和招标拍卖挂牌出让制度，节约集约利用土地资源。落实生态文明建设要求，依法依规开展规划环评工作，严格建设项目审批，加强自然生态环境和生物多样性保护，促进经济建设和资源环境胁调发展。
六、商务部要会同内蒙古自治区人民政府有序推进合作区双边协调机制建设，协调推动有关部门加强诣导和服务，促进合作区持续健康发展。
七、内蒙古自治区人民政府和商务部等有关部门要认真梳理和研究合作区建设中出现的新情况、新问题，重大事项及时请示报告。
联系人：XXX
联系电话：29111111111
原文：
国务院关于设立中蒙二连浩特—扎门乌德经济合作区的批复
https://www.gov.cn/zhengce/content/202403/content_6940964.htm",
      "type_list": [],
      "user_id": "123"
    }'
Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying 127.0.0.1:8000...
* Connected to localhost (127.0.0.1) port 8000 (#0)
> POST /spellcheck/oa HTTP/1.1
> Host: localhost:8000
> User-Agent: curl/7.88.1
> Accept: */*
> Content-Type: application/json
> Content-Length: 4036
>
< HTTP/1.1 200 OK
< Date: Fri, 06 Jun 2025 02:31:21 GMT
< Server: WSGIServer/0.2 CPython/3.10.16
< Content-Type: application/json
< Vary: Accept, Cookie
< Allow: POST, OPTIONS
< X-Frame-Options: DENY
< Content-Length: 2168
< X-Content-Type-Options: nosniff
< Referrer-Policy: same-origin
< Cross-Origin-Opener-Policy: same-origin
<
{"code":"200","message":"SUCCESS","result":[{"category":"疑似错误","comment":"引用格式错误","level":1,"length":34,"offset":97,"proposal":"引用文件的规范为：《文件标题》（XXX〔20XX〕X号）","type":"检查引用格式错误","word":"《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号）"},{"category":"疑似错误","comment":"数字单位类错误","level":1,"length":2,"offset":374,"proposal":72,"type":"检查数字单位类错误","word":"7二"},{"category":"疑似错误","comment":"格式错误","level":1,"length":11,"offset":1291,"proposal":"请核对电话号码“29111111111”是否正确","type":"检查格式错误","word":"29111111111"},{"category":"疑似错误","comment":"结构层次标注错误","level":1,"length":2,"offset":1390,"proposal":"5.","type":"检查结构层次标注错误","word":"4."},{"category":"疑似错误","comment":"标点符号错误","level":1,"length":1,"offset":519,"proposal":"","type":"检查标点符号错误","word":"、"},{"category":"疑似错误","comment":"结构层次标注错误","level":1,"length":2,"offset":724,"proposal":"四、","type":"检查结构层次标注错误","word":"三、"},{"category":"疑似错误","comment":"数字单位类错误","level":1,"length":2,"offset":410,"proposal":600,"type":"检查数字单位类错误","word":"6百"},{"category":"疑似错误","comment":"标点符号错误","level":1,"length":1,"offset":32,"proposal":"”","type":"检查标点符号错误","word":"\""},{"category":"疑似错误","comment":"结构层次标注错误","level":1,"length":2,"offset":312,"proposal":"10.","type":"检查结构层次标注错误","word":"4."},{"category":"疑似错误","comment":"标点符号错误","level":1,"length":1,"offset":48,"proposal":"”","type":"检查标点符号错误","word":"\""},{"category":"疑似错误","comment":"标点符号错误","level":1,"length":1,"offset":26,"proposal":"“","type":"检查标点符号错误","word":"\""},{"category":"疑似错误","comment"* Connection #0 to host localhost left intact
:"标点符号错误","level":1,"length":1,"offset":43,"proposal":"“","type":"检查标点符号错误","word":"\""}]}
-e 本服务请求耗时: 463 秒

```

日志如下
```
[2025-06-06 02:23:38,616][Thread-2 (process_request_thread):281473416425888][task_id:check_ref_errors][check_ref_errors.py:46][INFO][ref_errors 匹配的文本: 《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号） 起始下标: 97 结束下标: 131 匹配的长度: 34
]
[2025-06-06 02:23:38,616][Thread-2 (process_request_thread):281473416425888][task_id:check_struct_errors][check_struct_errors.py:81][INFO][detect_struct_num 匹配的文本: 4. 起始下标: 312 结束下标: 314 匹配的长度: 2
]
[2025-06-06 02:23:38,617][Thread-2 (process_request_thread):281473416425888][task_id:check_struct_errors][check_struct_errors.py:81][INFO][detect_struct_num 匹配的文本: 4. 起始下标: 1390 结束下标: 1392 匹配的长度: 2
]
[2025-06-06 02:23:38,617][Thread-2 (process_request_thread):281473416425888][task_id:check_struct_errors][check_struct_errors.py:46][INFO][detect_struct_chinese 匹配的文本: 三、 起始下标: 724 结束下标: 726 匹配的长度: 2
]
[2025-06-06 02:24:50,177][Thread-1 (process_request_thread):281473425928608][task_id:ai_hegao_plus_app.logger][views.py:54][INFO][get result == [{'category': '疑似错误', 'comment': '引用格式错误', 'level': 1, 'length': 34, 'offset': 97, 'proposal': '引用文件的规范为：《文件标题》（XXX〔20XX〕X号）', 'type': '检查引用格式错误', 'word': '《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号）'}, {'category': '疑似错误', 'comment': '数字单位类错误', 'level': 1, 'length': 2, 'offset': 374, 'proposal': 72, 'type': '检查数字单位类错误', 'word': '7二'}, {'category': '疑似错误', 'comment': '格式错误', 'level': 1, 'length': 11, 'offset': 1291, 'proposal': '请核对电话号码“29111111111”是否正确', 'type': '检查格式错误', 'word': '29111111111'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 1390, 'proposal': '5.', 'type': '检查结构层次标注错误', 'word': '4.'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 519, 'proposal': '', 'type': '检查标点符号错误', 'word': '、'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 724, 'proposal': '四、', 'type': '检查结构层次标注错误', 'word': '三、'}, {'category': '疑似错误', 'comment': '数字单位类错误', 'level': 1, 'length': 2, 'offset': 410, 'proposal': 600, 'type': '检查数字单位类错误', 'word': '6百'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 32, 'proposal': '”', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 312, 'proposal': '10.', 'type': '检查结构层次标注错误', 'word': '4.'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 48, 'proposal': '”', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 26, 'proposal': '“', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 43, 'proposal': '“', 'type': '检查标点符号错误', 'word': '"'}]]
[2025-06-06 02:24:50,177][Thread-1 (process_request_thread):281473425928608][task_id:ai_hegao_plus_app.logger][views.py:36][INFO][get normal response == [{'category': '疑似错误', 'comment': '引用格式错误', 'level': 1, 'length': 34, 'offset': 97, 'proposal': '引用文件的规范为：《文件标题》（XXX〔20XX〕X号）', 'type': '检查引用格式错误', 'word': '《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号）'}, {'category': '疑似错误', 'comment': '数字单位类错误', 'level': 1, 'length': 2, 'offset': 374, 'proposal': 72, 'type': '检查数字单位类错误', 'word': '7二'}, {'category': '疑似错误', 'comment': '格式错误', 'level': 1, 'length': 11, 'offset': 1291, 'proposal': '请核对电话号码“29111111111”是否正确', 'type': '检查格式错误', 'word': '29111111111'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 1390, 'proposal': '5.', 'type': '检查结构层次标注错误', 'word': '4.'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 519, 'proposal': '', 'type': '检查标点符号错误', 'word': '、'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 724, 'proposal': '四、', 'type': '检查结构层次标注错误', 'word': '三、'}, {'category': '疑似错误', 'comment': '数字单位类错误', 'level': 1, 'length': 2, 'offset': 410, 'proposal': 600, 'type': '检查数字单位类错误', 'word': '6百'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 32, 'proposal': '”', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 312, 'proposal': '10.', 'type': '检查结构层次标注错误', 'word': '4.'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 48, 'proposal': '”', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 26, 'proposal': '“', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 43, 'proposal': '“', 'type': '检查标点符号错误', 'word': '"'}]]
[2025-06-06 02:31:21,203][Thread-2 (process_request_thread):281473416425888][task_id:ai_hegao_plus_app.logger][views.py:54][INFO][get result == [{'category': '疑似错误', 'comment': '引用格式错误', 'level': 1, 'length': 34, 'offset': 97, 'proposal': '引用文件的规范为：《文件标题》（XXX〔20XX〕X号）', 'type': '检查引用格式错误', 'word': '《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号）'}, {'category': '疑似错误', 'comment': '数字单位类错误', 'level': 1, 'length': 2, 'offset': 374, 'proposal': 72, 'type': '检查数字单位类错误', 'word': '7二'}, {'category': '疑似错误', 'comment': '格式错误', 'level': 1, 'length': 11, 'offset': 1291, 'proposal': '请核对电话号码“29111111111”是否正确', 'type': '检查格式错误', 'word': '29111111111'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 1390, 'proposal': '5.', 'type': '检查结构层次标注错误', 'word': '4.'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 519, 'proposal': '', 'type': '检查标点符号错误', 'word': '、'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 724, 'proposal': '四、', 'type': '检查结构层次标注错误', 'word': '三、'}, {'category': '疑似错误', 'comment': '数字单位类错误', 'level': 1, 'length': 2, 'offset': 410, 'proposal': 600, 'type': '检查数字单位类错误', 'word': '6百'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 32, 'proposal': '”', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 312, 'proposal': '10.', 'type': '检查结构层次标注错误', 'word': '4.'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 48, 'proposal': '”', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 26, 'proposal': '“', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 43, 'proposal': '“', 'type': '检查标点符号错误', 'word': '"'}]]
[2025-06-06 02:31:21,204][Thread-2 (process_request_thread):281473416425888][task_id:ai_hegao_plus_app.logger][views.py:36][INFO][get normal response == [{'category': '疑似错误', 'comment': '引用格式错误', 'level': 1, 'length': 34, 'offset': 97, 'proposal': '引用文件的规范为：《文件标题》（XXX〔20XX〕X号）', 'type': '检查引用格式错误', 'word': '《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号）'}, {'category': '疑似错误', 'comment': '数字单位类错误', 'level': 1, 'length': 2, 'offset': 374, 'proposal': 72, 'type': '检查数字单位类错误', 'word': '7二'}, {'category': '疑似错误', 'comment': '格式错误', 'level': 1, 'length': 11, 'offset': 1291, 'proposal': '请核对电话号码“29111111111”是否正确', 'type': '检查格式错误', 'word': '29111111111'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 1390, 'proposal': '5.', 'type': '检查结构层次标注错误', 'word': '4.'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 519, 'proposal': '', 'type': '检查标点符号错误', 'word': '、'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 724, 'proposal': '四、', 'type': '检查结构层次标注错误', 'word': '三、'}, {'category': '疑似错误', 'comment': '数字单位类错误', 'level': 1, 'length': 2, 'offset': 410, 'proposal': 600, 'type': '检查数字单位类错误', 'word': '6百'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 32, 'proposal': '”', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '结构层次标注错误', 'level': 1, 'length': 2, 'offset': 312, 'proposal': '10.', 'type': '检查结构层次标注错误', 'word': '4.'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 48, 'proposal': '”', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 26, 'proposal': '“', 'type': '检查标点符号错误', 'word': '"'}, {'category': '疑似错误', 'comment': '标点符号错误', 'level': 1, 'length': 1, 'offset': 43, 'proposal': '“', 'type': '检查标点符号错误', 'word': '"'}]]

```

#### ai-doc-poc 到 ai-redactor

- 测试脚本：ai-doc-poc/shells/test.sh
- 测试结果：
```
[root@aiserver04 jtws-host]# docker exec -it ai-doc-poc bash
bash-5.1$ cd shells/
bash-5.1$ ls
start.sh  test.sh  test.txt
bash-5.1$ sh test.sh
接口验证: curl -X POST http://*************:8000

<!doctype html>
<html lang="en">
<head>
  <title>Not Found</title>
</head>
<body>
  <h1>Not Found</h1><p>The requested resource was not found on this server.</p>
</body>
</html>
=== 文档检查API测试 ===
正在测试文档检查接口...
curl -X POST \
  -F "file=@test.txt;type=text/plain" \
  -F "accessMode=STANDALONE" \
  -F "checkUnitId=BALANCEABILITY" \
  -F "fileType=HTML" \
  -F "extension={\"title\":\"请在此输入标题\",\"mainSubmit\":[],\"copySubmit\":[]}" \
  http://localhost:$PORT/ai-doc-poc/api/v1/check

{"code":"FH_PUB_OK","message":"成功","data":{"checkUnitId":"pncheck","checkInfoList":[],"unCheckInfoList":[],"file":"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"}}执行时间: 0 秒

=== 文档检查API测试结束 ===

```
#### poc-intelligence-view-write 到 ai-doc-poc

- 测试脚本：poc-intelligence-view-write/shells/test.sh
- 测试结果：
```
root@aiserver04 jtws-host]# docker restart poc-intelligence-view
poc-intelligence-view
[root@aiserver04 jtws-host]# docker exec -it poc-intelligence-view bash
root@aiserver04:/# sh test.sh
-e === 文档检查API测试 ===
-e 正在测试文档检查接口...
-e 正在测试网络是否连通...
-e 正在测试接口是否能访问...
curl -v http://*************:20005
*   Trying *************:20005...
* Connected to ************* (*************) port 20005 (#0)
> GET / HTTP/1.1
> Host: *************:20005
> User-Agent: curl/7.88.1
> Accept: */*
>
< HTTP/1.1 404
< Content-Type: text/html;charset=utf-8
< Content-Language: en
< Content-Length: 431
< Date: Fri, 06 Jun 2025 03:09:49 GMT
<
* Connection #0 to host ************* left intact
<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>-e 正在测试直连...
Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying *************:20005...
* Connected to ************* (*************) port 20005 (#0)
> POST /ai-doc-poc/api/v1/check HTTP/1.1
> Host: *************:20005
> User-Agent: curl/7.88.1
> Accept: */*
> Content-Length: 4619
> Content-Type: multipart/form-data; boundary=------------------------cb1760a6d3fa5592
>
* We are completely uploaded and fine
< HTTP/1.1 200
< Content-Type: application/json
< Transfer-Encoding: chunked
< Date: Fri, 06 Jun 2025 03:09:49 GMT
<
{"code":"FH_PUB_OK","message":"成功","data":{"checkUnitId":"pncheck","checkInfoList":[],"unCheckInfoList":[],"file":"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* Connection #0 to host ************* left intact
iDogZTns7vkurrvvJpYWFgg6IGU57O755S16K+d77yaMjkxMTExMTExMTEg5Y6f5paH77yaIOWbveWKoemZouWFs+S6juiuvueri+S4reiSmeS6jOi/nua1qeeJueKAlOaJjumXqOS5jOW+t+e7j+a1juWQiOS9nOWMuueahOaJueWkjSBodHRwczovL3d3dy5nb3YuY24vemhlbmdjZS9jb250ZW50LzIwMjQwMy9jb250ZW50XzY5NDA5NjQuaHRt"}}执行时间: 0 秒

-e 正在测试文档检查代理接口...
Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying *************:20003...
* Connected to ************* (*************) port 20003 (#0)
> POST /ai-doc-poc/api/v1/check HTTP/1.1
> Host: *************:20003
> User-Agent: curl/7.88.1
> Accept: */*
> Content-Length: 4619
> Content-Type: multipart/form-data; boundary=------------------------93590933795bc250
>
* We are completely uploaded and fine
< HTTP/1.1 200
< Server: nginx/1.27.4
< Date: Fri, 06 Jun 2025 03:09:49 GMT
< Content-Type: application/json
< Transfer-Encoding: chunked
< Connection: keep-alive
< Vary: Accept-Encoding
<
{"code":"FH_PUB_OK","message":"成功","data":{"checkUnitId":"pncheck","checkInfoList":[],"unCheckInfoList":[],"file":"5qCH6aKY77ya6K6+56uL5Lit6JKZ5LqM6L+e5rWp54m55omO6Zeo5LmM5b6357uP5rWO5ZCI5L2c5Yy655qE5om55aSNIOagueaNriLkuInkuKrkuIDnmb7lubQi5aWL5paX55uu5qCH55qE5a6P5Lyf6JOd5Zu+5ZKMIuWbm+S4quS7o+ihqCLph43opoHmgJ3mg7PnmoTmjIflr7znsr7npZ7vvIzmiJHku6zlr7nkuK3okpnkuKTlm73lnKjnu4/otLjpoobln5/mt7HljJblkIjkvZzjgIHlhbHlkIzmjqjliqjljLrln5/lj5HlsZXnmoTliqrlipvooajnpLrogq/lrprlkozmlK/mjIHjgILkvaDku6zjgIrlhbPkuo7orr7nq4vkuK3okpnkuozov57mtannibnigJTmiY7pl6jkuYzlvrfnu4/mtY7lkIjkvZzljLrnmoTor7fnpLrjgIvvvIjjgJQyMDI044CV5Y+377yJ5pS25oKJ44CC546w5om55aSN5aaC5LiL77yaIOS4gOOAgeWQjOaEj+iuvueri+S4reiSmeS6jOi/nua1qeeJueKAlOaJjumXqOS5jOW+t+e7j+a1juWQiOS9nOWMuu+8iOS7peS4i+eugOensOWQiOS9nOWMuu+8ieOAguWQiOS9nOWMuuS4reaWueWMuuWfn+mdouenrzkuMDPlubPmlrnlhazph4zvvIzkvY3kuo7lhoXokpnlj6Toh6rmsrvljLrkuozov57mtannibnluILkuK3okpnovrnlooPlhazot6/lj6Plsrjopb/ljJfmgbvvvIzlm5voh7PojIPlm7TkuLrvvJrkuJzoh7Plhazot6/lj6PlsrjotKfov5DpgJrpgZM3MOexs++8jOWNl+iHs+i+uemYsumDqOmYn+e9keWbtOagj++8jOilv+iHs+i+uemYsuWTqOaJgDYwMOexs++8jOWMl+iHs+i+uemYsuemgeWMuuWNl+i+ueeVjOOAguWQiOS9nOWMuuS4reaWueWMuuWfn+WIhuS4nOOAgeilv+S4pOWMuu+8jOaMieWKn+iDveWumuS9jeWunuihjOWIhuWMuuebkeeuoe+8jOW5tuWcqOWMuuWfn+mXtOiuvueri+malOemu+iuvuaWveOAguWFtuS4re+8jOS4nOWMuumdouenrzQuMzHlubPmlrnlhazph4zvvIzlm5voh7PojIPlm7TkuLrvvJrkuJzoh7Plhazot6/lj6PlsrjotKfov5DpgJrpgZM3MOexs++8jOWNl+iHs+i+uemYsumDqOmYn+e9keWbtOagj++8jOilv+iHs+ilv+e7jzHot6/vvIzljJfoh7PovrnpmLLnpoHljLrljZfovrnnlYzvvJvopb/ljLrpnaLnp680LjfkuozlubPmlrnlhazph4zvvIzlm5voh7PojIPlm7TkuLrvvJrkuJzoh7Popb/nu48x6Lev77yM5Y2X6Iez6L656Ziy6YOo6Zif572R5Zu05qCP77yM6KW/6Iez6L656Ziy5ZOo5omANueZvuexs++8jOWMl+iHs+i+uemYsuemgeWMuuWNl+i+ueeVjOOAguWQiOS9nOWMuuS4reaWueWMuuWfn+iMg+WbtOWFt+S9k+S7peeVjOWdgOeCueWdkOagh+aOp+WItu+8jOeVjOWdgOeCueWdkOagh+eUseWVhuWKoemDqOOAgeiHqueEtui1hOa6kOmDqOi0n+i0o+WPkeW4g+OAgiDkuozjgIHlkIjkvZzljLrkuK3mlrnljLrln5/lu7rorr7opoHlhajpnaLotK/lvbvokL3lrp7lhZrnmoTkuozljYHlpKfnsr7npZ7vvIzmjInnhaflhZrkuK3lpK7lhrPnrZbpg6jnvbLvvIznq4votrPmnKzlnLDnibnoibLkvJjlir/vvIzph43ngrnlj5HlsZXlm73pmYXotLjmmJPjgIHjgIHlm73pmYXnianmtYHjgIHov5vlh7rlj6PliqDlt6XjgIHot6jlooPml4XmuLjlj4rnm7jlhbPphY3lpZfmnI3liqHvvIzmt7HlhaXmjqjov5vlm73pmYXnu4/ojILlkIjkvZzvvIzmiZPpgKDmsr/ovrnlnLDljLrnmoTpq5jmsLTlubPlvIDmlL7lubPlj7Ag44CB5LiA5bim5LiA6L6C5Lit6JKZ5L+E57uP5rWO6LWw5buK55qE6YeN6KaB6IqC54K544CB5Lit6JKZ5Y+L5aW95ZCI5L2c55qE5YW46IyD77yM5pyN5Yqh5p6E5bu65paw5Y+R5bGV5qC85bGA44CB5o6o5Yqo6auY6LSo6YeP5Y+R5bGVLiDkuInjgIHlkIjkvZzljLrkuK3mlrnljLrln5/pgJrov4fniannkIblm7TnvZHlkozkv6Hmga/ljJbnm5HnrqHlrp7ooYzlsIHpl63nrqHnkIbjgILmjInkuK3okpnmlL/lupzpl7TljY/orq7nuqblrprvvIzkuI7okpnmlrnlkIjkvZzorr7nq4vlj4zmlrnljLrln5/pl7TnmoTot6jlooPorr7mlr3vvIzlrp7mlr3ovrnpmLLjgIHmtbflhbPmo4Dmn6XvvIzku6Xlj4rnm7jlhbPmn6XpqozjgIHmo4Dpqozmo4DnlqvjgIHlronmo4DnrYnmlrnpnaLnm5HnrqHvvIzmnInluo/mjqjov5vkuI7okpnmlrnljLrln5/nmoTkurrlkZjjgIHotKfniankvr/liKnpgJrooYzjgIIg5LiJ44CB5ZCM5oSP5a+55ZCI5L2c5Yy65Lit5pa55Yy65Z+f5a6e5pa955u45YWz5pSv5oyB5pS/562W44CC5pSv5oyB5bu66K6+5Zu96ZmF5Yy755aX5YWI6KGM5Yy677yM6byT5Yqx5Yy755aX5paw5oqA5pyv44CB5paw6I2v5ZOB56CU5Y+R5bqU55So44CC5pSv5oyB56CU56m26KeE5YiS5bu66K6+5ZCI5L2c5Yy66Leo5aKD6ZOB6Lev5LiT55So57q/44CC5YWB6K645byA5bGV5Zu96ZmF5paH5YyW6Im65pyv5ZOB5bGV56S644CB5ouN5Y2W44CB5Lqk5piT5Lia5Yqh44CC5Zyw5pa56YCa6L+H5aSW57uP6LS45Y+R5bGV5LiT6aG56LWE6YeR562J546w5pyJ6LWE6YeR5rig6YGT77yM57uf56255pSv5oyB5ZCI5L2c5Yy65Y+R5bGV44CC5pSv5oyB6YWN5aWX6YeR6J6N5pyN5Yqh77yM6byT5Yqx5Lit6LWE5ZKM5aSW6LWE6ZO26KGM5py65p6E5YWl6am777yM55+l5oyB5rCR6Ze06LWE5pys6L+b5YWl5Yy65YaF6YeR6J6N5Lia77yM5pSv5oyB5L6d5rOV5ZCI6KeE5byA5bGV6Leo5aKD5oqV6J6N6LWE5Lia5Yqh5ZKM5Liq5Lq65pys5aSW5biB5YWR5o2i5Lia5Yqh77yM5omp5aSn5Lq65rCR5biB6Leo5aKD5L2/55So44CC5YWB6K645byV5YWl5aKD5aSW5LyY6LSo5pWZ6IKy6LWE5rqQ77yM5byA5bGV6auY5rC05bmz5Zu96ZmF5pWZ6IKy5Lqk5rWB5ZCI5L2c44CC5pSv5oyB56Gu5pyJ6ZyA5rGC5pe25oyJ56iL5bqP6K6+56uL57u85ZCI5L+d56iO5Yy644CCIOS6lOOAgeWGheiSmeWPpOiHquayu+WMuuS6uuawkeaUv+W6nOimgeWKoOW8uue7hOe7h+mihuWvvO+8jOWIh+WunuiQveWunuS4u+S9k+i0o+S7u++8jOWIm+aWsOihjOaUv+euoeeQhuS9k+WItu+8jOenkeWtpuiuvue9ruWQiOS9nOWMuuiBjOiDveacuuaehO+8jOaPkOmrmOihjOaUv+aViOeOh+WSjOacjeWKoeawtOW5s+OAguS4peagvOWwiuW+quWbveWcn+epuumXtOinhOWIku+8jOaMieinhOWumueoi+W6j+WxpeihjOWFt+S9k+eUqOWcsOaKpeaJueaJi+e7rTvkuKXmoLzmiafooYzpobnnm67lu7rorr7nlKjlnLDmjqfliLbmjIfmoIflkozmi5vmoIfmi43ljZbmjILniYzlh7rorqnliLbluqbvvIzoioLnuqbpm4bnuqbliKnnlKjlnJ/lnLDotYTmupDjgILokL3lrp7nlJ/mgIHmlofmmI7lu7rorr7opoHmsYLvvIzkvp3ms5Xkvp3op4TlvIDlsZXop4TliJLnjq/or4Tlt6XkvZzvvIzkuKXmoLzlu7rorr7pobnnm67lrqHmibnvvIzliqDlvLroh6rnhLbnlJ/mgIHnjq/looPlkoznlJ/nianlpJrmoLfmgKfkv53miqTvvIzkv4Pov5vnu4/mtY7lu7rorr7lkozotYTmupDnjq/looPog4HosIPlj5HlsZXjgIIg5YWt44CB5ZWG5Yqh6YOo6KaB5Lya5ZCM5YaF6JKZ5Y+k6Ieq5rK75Yy65Lq65rCR5pS/5bqc5pyJ5bqP5o6o6L+b5ZCI5L2c5Yy65Y+M6L655Y2P6LCD5py65Yi25bu66K6+77yM5Y2P6LCD5o6o5Yqo5pyJ5YWz6YOo6Zeo5Yqg5by66K+j5a+85ZKM5pyN5Yqh77yM5L+D6L+b5ZCI5L2c5Yy65oyB57ut5YGl5bq35Y+R5bGV44CCIOS4g+OAgeWGheiSmeWPpOiHquayu+WMuuS6uuawkeaUv+W6nOWSjOWVhuWKoemDqOetieacieWFs+mDqOmXqOimgeiupOecn+ais+eQhuWSjOeglOeptuWQiOS9nOWMuuW7uuiuvuS4reWHuueOsOeahOaWsOaDheWGteOAgeaWsOmXrumimO+8jOmHjeWkp+S6i+mhueWPiuaXtuivt+ekuuaKpeWRiuOAg* Connection #0 to host ************* left intact
iDogZTns7vkurrvvJpYWFgg6IGU57O755S16K+d77yaMjkxMTExMTExMTEg5Y6f5paH77yaIOWbveWKoemZouWFs+S6juiuvueri+S4reiSmeS6jOi/nua1qeeJueKAlOaJjumXqOS5jOW+t+e7j+a1juWQiOS9nOWMuueahOaJueWkjSBodHRwczovL3d3dy5nb3YuY24vemhlbmdjZS9jb250ZW50LzIwMjQwMy9jb250ZW50XzY5NDA5NjQuaHRt"}}执行时间: 0 秒

-e === 文档检查API测试结束 ===
root@aiserver04:/#

```



### 拟文测试

#### ai-writer-nostream到大模型

- 测试脚本：ai-writer-nostream/shells/test.sh
- 测试结果：
```
[root@aiserver04 ~]# cd /data/jtws-host/
[root@aiserver04 jtws-host]# vi ai-writer-nostream/config/application.properties
[root@aiserver04 jtws-host]# docker restart ai-writer-nostream
ai-writer-nostream
[root@aiserver04 jtws-host]# docker exec -it ai-writer-nostream
"docker exec" requires at least 2 arguments.
See 'docker exec --help'.

Usage:  docker exec [OPTIONS] CONTAINER COMMAND [ARG...]

Run a command in a running container
[root@aiserver04 jtws-host]# docker exec -it ai-writer-nostream bash
root@aiserver04:/app# cd shells/
root@aiserver04:/app/shells# ls
start.sh  test.sh
root@aiserver04:/app/shells# sh test.sh
-e === 大模型接口测试 ===
model_url: http://localhost:1025/v1/chat/completions
model_name: DeepSeek-R1-Distill-Llama-70B
api_key:
-e 执行命令:
curl -v -X POST "http://localhost:1025/v1/chat/completions" -H "Content-Type: application/json" -H "Authorization: Bearer " -d '{"model": "DeepSeek-R1-Distill-Llama-70B", "messages": [{"role": "system", "content": "你是一个公文写作专家，公文内不要出现\"我们\"、\"我\"、\"你们\"等口语化词汇，也不需要带入主送单位"}, {"role": "user", "content": "请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求1800字以上。"}]}'
Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying 127.0.0.1:1025...
* Connected to localhost (127.0.0.1) port 1025 (#0)
> POST /v1/chat/completions HTTP/1.1
> Host: localhost:1025
> User-Agent: curl/7.88.1
> Accept: */*
> Content-Type: application/json
> Authorization: Bearer
> Content-Length: 1699
>
< HTTP/1.1 200 OK
< Connection: close
< Content-Length: 7430
< Content-Type: application/json
< Keep-Alive: timeout=180, max=2147483647
<
{"id":"endpoint_common_54","object":"chat.completion","created":1749176943,"model":"DeepSeek-R1-Distill-Llama-70B","choices":[{"index":0,"message":{"role":"assistant","content":"嗯，用户希望生成一份关于召开2024年度信息化建设推进会议的通知。首先，我需要明确这个通知的结构和内容。通常，公文通知会包括标题、发文单位、发文时间、正文、附件、联系方式、结束语和单位盖章部分。\n\n用户特别指出不要出现“我们”、“我”、“你们”这些口语化词汇，所以我得用正式的语言来写。同时，主送单位需要用户自行输入，所以我在模板里留了空白。\n\n接下来，会议的目的、时间、地点、参会人员、议程和参会要求都已经明确给出，我需要把这些信息整合进去。会议目的主要是推进信息化建设，提升管理效率，这一点要在正文中强调。\n\n会议时间设为2024年9月6日上午9:15，会场在总部三楼会议室和各分公司的视频会议室。参会人员分为主会场和分会场，分别列出了集团公司领导、信息化办公室负责人、各部门专员等，确保通知的全面性。\n\n会议议程有四个部分：通报进展情况、部署项目、交流经验、答疑讨论，这些内容需要清晰列出，让参会人员提前了解流程。\n\n参会要求部分，用户给了五点，我需要逐条列出，确保每项要求都明确，比如着正装、遵守纪律、不随意走动等，这些都是维持会议秩序的重要细节。\n\n联系方式部分，提供了王伟的电话和邮箱，这样遇到问题时可以及时联系。\n\n最后，字数要求1800字以上，我需要确保内容足够详细，每个部分都有足够的描述，同时保持语言的正式和简洁。\n\n整体来看，这个通知需要结构清晰，内容全面，同时符合公文写作的规范，避免口语化表达。现在，我可以根据这些要点，开始撰写正式的通知内容了。\n</think>\n\n关于召开2024年度信息化建设推进会议的通知\n\n为进一步推进公司信息化建设工作，提升管理效率，现将有关召开2024年度信息化建设推进会议的有关事项通知如下：\n\n一、会议目的\n通过本次会议，全面总结2024年上半年信息化建设工作成效，部署下半年重点信息化项目，交流信息化建设经验，答疑与讨论，进一步推动公司信息化建设工作的深入开展。\n\n二、会议时间\n2024年9月6日（周五）上午9:15\n\n三、会场信息\n主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。\n\n四、参会人员\n（一）主会场参会人员：\n1. 集团公司领导\n2. 信息化办公室负责人\n3. 各部门信息化专员及相关人员\n\n（二）分会场参会人员：\n1. 各分公司负责人\n2. 各分公司信息化专员及相关人员\n\n五、会议议程\n（一）通报2024年上半年信息化建设进展情况\n（二）部署下半年重点信息化项目\n（三）交流信息化建设经验\n（四）答疑与讨论\n\n六、参会要求\n（一）请各单位高度重视，安排相关人员准时参会。\n（二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。\n（三）会议期间请勿随意走动，不得录音录像。\n（四）请着正装出席会议。\n（五）如有特殊情况不能参会，请提前报备。\n\n七、联系方式\n联系人：王伟\n电话：010-12345678\n邮箱：<EMAIL>\n\n请各单位按照通知要求做好参会准备工作。\n\n特此通知。\n\n（此处为发文单位）\n2024年8月30日\n\n（此处为附件）\n\n1. 会议议程表\n2. 参会人员名单\n3. 会场布置图\n\n（此处为联系方式）\n联系人：王伟\n电话：010-12345678\n邮箱：<EMAIL>\n\n（此处为结束语）\n请各单位认真对照本通知要求，做好相关工作。\n\n（此处为单位盖章）\n\n以上内容约1800字，符合公文写作规范，不含口语化词汇，结构清晰，内容全面。","tool_calls":null},"finish_reason":"stop"}],"usage":{"prompt_tokens":457,"completion_tokens":1041,"total_tokens":1498},"prefill_time":127,"decode_time_arr":[40,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,37,36,54,36,36,36,37,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,37,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,40,192,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,37,37,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,38,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,38,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,37,36,38,36,36,36,36,37,36,37,36,36,36,36,36,38,191,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,56,36,36,36,36,36,37,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,38,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,37,36,36,36,36,36,36,36,39,36,36,36,36,36,36,36,36,36,36,37,37,37,37,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,37,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,37,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,37,38,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,37,37,37,36,37,37,36,36,36,36,37,36,36,36,36,36,37,37,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,37,36,36,37,37,37,36,36,36,37,37,36,36,37,36,36,36,36,37,36,37,36,36,37,36,36,37,37,37,37,37,37,37,37,37,37,37,37,37,39,201,37,36,37,36,36,38,39,37,43,38,36,36,36,36,36,36,36,36,36,36,36,36,37,37,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,54,40,36,58,38,36,47,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,37,36,36,36,39,36,36,37,37,36,36,37,37,36,36,36,37,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,38,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,* Closing connection 0
36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,38,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,36,37,36]}
-e 大模型请求耗时: 39 秒
-e === 简单请求测试 ===
-e 执行命令:
curl -v -X POST "http://127.0.0.1:20008/mubanwriter/v1/service" -H "Content-Type: application/json" -d '{"text": "请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求1800字以上。"}'
Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying 127.0.0.1:20008...
* Connected to 127.0.0.1 (127.0.0.1) port 20008 (#0)
> POST /mubanwriter/v1/service HTTP/1.1
> Host: 127.0.0.1:20008
> User-Agent: curl/7.88.1
> Accept: */*
> Content-Type: application/json
> Content-Length: 1449
>
< HTTP/1.1 200 OK
< Date: Fri, 06 Jun 2025 02:29:38 GMT
< Server: WSGIServer/0.2 CPython/3.10.17
< Content-Type: application/json
< Vary: Accept, Cookie
< Allow: POST, OPTIONS
< X-Frame-Options: DENY
< Content-Length: 3171
< X-Content-Type-Options: nosniff
< Referrer-Policy: same-origin
< Cross-Origin-Opener-Policy: same-origin
<
{"success":"true","code":"FH_PUB_OK","message":"执行成功","data":{"sourceText":"请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求1800字以上。","answerText":"关于召开2024年度信息化建设推进会议的通知\n\n为进一步推进公司信息化建设工作，提升管理效率，开创信息化建设新局面，打造高效、智能、安全的信息化管理平台，集团公司决定召开2024年度信息化建设推进会议。现将有关事项通知如下：\n\n一、会议时间\n2024年9月6日（周五）上午9:15\n\n二、会议地点\n主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。\n\n三、参会人员\n（一）主会场参会人员：\n1. 集团公司领导；\n2. 信息化办公室负责人；\n3. 各部门信息化专员及相关人员。\n\n（二）分会场参会人员：\n1. 各分公司负责人；\n2. 各分公司信息化专员及相关人员。\n\n四、会议议程\n（一）通报2024年上半年信息化建设进展情况；\n（二）部署下半年重点信息化项目；\n（三）交流信息化建设经验；\n（四）答疑与讨论。\n\n五、参会要求\n（一）请各单位高度重视，安排相关人员准时参会；\n（二）参会人员须遵守会议纪律，保持会场安静，手机调至静音；\n（三）会议期间请勿随意走动，不得录音录像；\n（四）请着正装出席会议；\n（五）如有特殊情况不能参会，请提前报备。\n\n六、联系方式\n联系人：王伟\n联系电话：010-12345678\n邮箱：<EMAIL>\n\n请各相关单位按照会议要求做好参会准备工作，确保会议顺利进行。\n\n特此通▒* Connection #0 to host 127.0.0.1 left intact
▒▒。\n\n附件：会议议程、参会回执表\n\nXXX公司信息化办公室\n2024年9月5日"}}
-e 简单请求耗时: 35 秒
-e === 带extension的请求测试 ===
-e 执行命令:
curl -v -X POST "http://127.0.0.1:20008/mubanwriter/v1/service" -H "Content-Type: application/json" -d '{"text": "请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求1800字以上。", "extension": {"docInfo": {"sourceText": "关于召开项目进度评审会议的通知", "universalType": "通知", "subTypeName": "会议通知", "mainDeliveryUnit": "各部门负责人"}, "fileContent": {"modelEssay": ["为确保项目按计划推进，解决项目过程中的问题，特召开本次项目进度评审会议。"]}}}'
Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying 127.0.0.1:20008...
* Connected to 127.0.0.1 (127.0.0.1) port 20008 (#0)
> POST /mubanwriter/v1/service HTTP/1.1
> Host: 127.0.0.1:20008
> User-Agent: curl/7.88.1
> Accept: */*
> Content-Type: application/json
> Content-Length: 1785
>
< HTTP/1.1 200 OK
< Date: Fri, 06 Jun 2025 02:30:10 GMT
< Server: WSGIServer/0.2 CPython/3.10.17
< Content-Type: application/json
< Vary: Accept, Cookie
< Allow: POST, OPTIONS
< X-Frame-Options: DENY
< Content-Length: 2967
< X-Content-Type-Options: nosniff
< Referrer-Policy: same-origin
< Cross-Origin-Opener-Policy: same-origin
<
{"success":"true","code":"FH_PUB_OK","message":"执行成功","data":{"sourceText":"请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求1800字以上。","answerText":"关于召开2024年度信息化建设推进会议的通知\n\n为进一步推进公司信息化建设工作，提升管理效率，经研究决定，定于2024年9月6日（周五）上午9:15召开2024年度信息化建设推进会议。现将有关事项通知如下：\n\n一、会议时间\n2024年9月6日（周五）上午9:15\n\n二、会议地点\n主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。\n\n三、参会人员\n（一）主会场参会人员：\n1. 集* Connection #0 to host 127.0.0.1 left intact
团公司领导\n2. 信息化办公室负责人\n3. 各部门信息化专员及相关人员\n\n（二）分会场参会人员：\n1. 各分公司负责人\n2. 各分公司信息化专员及相关人员\n\n四、会议议程\n（一）通报2024年上半年信息化建设进展情况\n（二）部署下半年重点信息化项目\n（三）交流信息化建设经验\n（四）答疑与讨论\n\n五、参会要求\n（一）请各单位高度重视，安排相关人员准时参会。\n（二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。\n（三）会议期间请勿随意走动，不得录音录像。\n（四）请着正装出席会议。\n（五）如有特殊情况不能参会，请提前报备。\n\n六、联系方式\n联系人：王伟\n电话：010-12345678\n邮箱：<EMAIL>\n\n请各单位按照通知要求做好参会准备工作。\n\n特此通知。"}}-e 带extension的请求耗时: 32 秒

-e === 所有测试完成 ===

```
#### article-auto-compose-server 到 ai-writer-nostream

- 测试脚本：article-auto-compose-server/shells/test.sh
- 测试结果：
```
[root@aiserver04 jtws-host]# docker exec -it article-auto-compose-server bash
bash-5.1$ ls
app.jar  config  files  logs  shells
bash-5.1$ cd shells/
bash-5.1$ ls
start.sh  test.sh
bash-5.1$ sh test.sh
接口验证: curl -X POST http://localhost:20007/ai-compose-poc/api/v1/compose
{"timestamp":"2025-06-06 11:16:08.819","status":400,"error":"Bad Request","path":"/ai-compose-poc/api/v1/compose"}Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying 127.0.0.1:20007...
* Connected to 127.0.0.1 (127.0.0.1) port 20007 (#0)
> POST /ai-compose-poc/api/v1/compose HTTP/1.1
> Host: 127.0.0.1:20007
> User-Agent: curl/7.79.1
> Accept: */*
> Content-Type: application/json
> Content-Length: 193
>
* Mark bundle as not supporting multiuse
< HTTP/1.1 200
< Content-Type: application/json
< Transfer-Encoding: chunked
< Date: Fri, 06 Jun 2025 03:16:38 GMT
<
{"code":"FH_PUB_OK","message":"成功","data":{"composeUnitId":"glmwriter","sourceText":"请以《中国移动公司关于开展成本管理专项活动通知》为主题生成一篇通知类型的公文。字数要求100字以上","answerText":["关于开展成本管理专项活动的通知","为进一步提升公司管理水平，优化资源配置，提高运营效率，开创成本管理新局面，打造高效运营体系，集团公司决定在全公司范围内开展成本管理专项活动。现将有关事项通知如下：","一、各经营单元要高度重视成本管理工作，成立专项工作小组，明确责任分工，定期分析成本数据，制定降本增效方案。","二、各经营单元要加强成本管理能力建设，组织开展成本管理培训，提升全员成本意识和管理能力。","三、各经营单元要加强成本管理制度建设，完善成本核算、预算管理、考核机制等相关制度，确保成本管理工作有序推进。","四、各经营单元* Connection #0 to host 127.0.0.1 left intact
要加强成本管理执行力，强化采购、投资等关键环节的成本控制，确保资源有效利用。","其他事项：","请各经营单元认真贯彻落实上述要求，于本月30日前将专项活动实施方案上报。联系人：张XX，联系电话：XXX-XXXXXX，邮箱：<EMAIL>。","特此通知。","附件：1.成本管理专项活动实施方案","      2.成本管理工作表格模板"],"msgid":"0e9028e6-516a-4b64-801c-54ef4ab496ad"}}bash-5.1$
bash-5.1$
```
#### poc-intelligence-view-write 到 article-auto-compose-server

- 测试脚本：poc-intelligence-view-write/shells/test.sh
- 测试结果：
```
[root@aiserver04 jtws-host]# vi poc-intelligence-view-write/shells/test.sh
[root@aiserver04 jtws-host]# docker restart poc-intelligence-view-write
poc-intelligence-view-write
[root@aiserver04 jtws-host]# docker exec -it poc-intelligence-view-write bash
root@aiserver04:/# sh test.sh
-e === 文档检查API测试 ===
-e 正在测试文档检查接口...
-e 正在测试网络是否连通...
-e 正在测试直连...
Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying *************:20007...
* Connected to ************* (*************) port 20007 (#0)
> POST /ai-compose-poc/api/v1/compose HTTP/1.1
> Host: *************:20007
> User-Agent: curl/7.88.1
> Accept: */*
> Content-Type: application/json
> Content-Length: 193
>
< HTTP/1.1 200
< Content-Type: application/json
< Transfer-Encoding: chunked
< Date: Fri, 06 Jun 2025 03:34:18 GMT
<
{"code":"FH_PUB_OK","message":"成功","data":{"composeUnitId":"glmwriter","sourceText":"请以《中国移动公司关于开展成本管理专项活动通知》为主题生成一篇通知类型的公文。字数要求100字以上","answerText":["关于开展成本管理专项活动的通知","为进一步加强成本管理，提升管理水平，确保公司高质量发展，优化资源配置，提高运营效率，集团公司决定在全公司范围内开展成本管理专项活动。现将有关事项通知如下：","一、各经营单元要高度重视成本管理工作，严格执行成本管理制度，建立健全成本管理长效机制。建议从细节入手，强化预算管理，严格控制费用开支，优化采购流程，提高资金使用效率。","二、各经营单元要加强成本核算与分析工作。相关技术人员要建立完善的成本核算体系，及时准确反映成本数据。要定期分析成本变化趋势，发现问题并提出改进建议。同时，要建立成* Connection #0 to host ************* left intact
本可视化系统，直观展示成本数据，支持管理决策。","三、各经营单元要强化成本管理责任制。各企业要落实成本管理责任，主要负责人要把成本管理作为重要工作来抓，确保各项措施落实到位。要建立成本管理考核机制，将成本控制效果与绩效考核挂钩，确保各项措施有效执行。","四、其他事项","各经营单元请认真组织学习本通知及相关文件，梳理管理漏洞，制定整改措施，并于12月31日17:00前反馈进展情况。联系人：张三，联系电话：123-4567890，邮箱：<EMAIL>。","特此通知。","附件：1.成本管理专项活动实施方案","      2.成本管理自查表"],"msgid":"834f3367-2104-48eb-a84f-8dbafbb28318"}}
-e 正在测试文档检查代理接口...
Note: Unnecessary use of -X or --request, POST is already inferred.
*   Trying 127.0.0.1:20002...
* Connected to 127.0.0.1 (127.0.0.1) port 20002 (#0)
> POST /ai-compose-poc/api/v1/compose HTTP/1.1
> Host: 127.0.0.1:20002
> User-Agent: curl/7.88.1
> Accept: */*
> Content-Type: application/json
> Content-Length: 193
>
< HTTP/1.1 200
< Server: nginx/1.27.4
< Date: Fri, 06 Jun 2025 03:34:48 GMT
< Content-Type: application/json
< Transfer-Encoding: chunked
< Connection: keep-alive
< Vary: Accept-Encoding
<
{"code":"FH_PUB_OK","message":"成功","data":{"composeUnitId":"glmwriter","sourceText":"请以《中国移动公司关于开展成本管理专项活动通知》为主题生成一篇通知类型的公文。字数要求100字以上","answerText":["关于开展成本管理专项活动的通知","相关经营单元：","为进一步提升公司经济效益，优化资源配置，提高管理水平，开创成本管理工作新局面，打造精细化管理新模式，集团公司决定开展成本管理专项活动，统筹部署成本管理工作。为强化成本管理，做好成本核算、成本分析等工作，根据集团公司安排，现将有关要求通知如下：","一、各经营单元成本管理制度建设","为实现科学化、精细化管理，各经营单元应进一步完善成本管理制度，明确成本管理责任，强化内部监督。建议各单位结合实际情况，制定切实可行的成本管理方案，确保制度执行到位。","二、各经营单元成本核算与分析* Connection #0 to host 127.0.0.1 left intact
","相关技术人员应建立健全成本核算体系，定期对成本数据进行分析，及时发现问题并提出改进建议。要求各单位加强成本数据的准确性和完整性，确保分析结果的科学性和可操作性。","三、各经营单元成本控制措施","各企业应加强成本预算管理，严格执行成本控制标准，确保各项成本费用合理合规。主要负责人应高度重视，亲自抓好成本管理工作，确保成本控制措施落实到位。","四、其他事项","各经营单元请认真组织学习相关文件精神，结合实际情况制定具体实施方案，于12月31日17:00前反馈成本管理工作进展情况。联系人：张明，联系电话：123-4567890，邮箱：<EMAIL>。","特此通知。","附件：1.成本管理工作实施方案","2.成本核算表格模板"],"msgid":"eca52720-54aa-4e92-9bdd-fcfa1fe324e0"}}
-e === 文档检查API测试结束 ===
root@aiserver04:/#

```










