# 下载核稿拟稿镜像
echo "下载核稿拟稿镜像"
TAR_DIR=/mnt/e/jtws/draftflow

mkdir -p $TAR_DIR

cd $TAR_DIR

echo "登录镜像仓库"
echo "docker login officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn --username=zglyjt --password=Znwd0415@"
docker login officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn --username=zglyjt --password=Znwd0415@

echo "下载核稿拟稿镜像"
echo "docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0"
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0
echo "docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0"
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
echo "docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT"
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT
echo "docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18"
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18
echo "docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT"
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT
echo "docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python:v528"
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python:v528

echo "保存核稿拟稿镜像"
echo "docker save -o poc-intelligence-view-write.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0"
docker save -o poc-intelligence-view-write.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0
echo "docker save -o poc-intelligence-view.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0"
docker save -o poc-intelligence-view.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
echo "docker save -o ai-doc-poc.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT"
docker save -o ai-doc-poc.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT
echo "docker save -o ai-redactor.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18"
docker save -o ai-redactor.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18
echo "docker save -o article-auto-compose-server.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT"
docker save -o article-auto-compose-server.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT
echo "docker save -o ai-writer-nostream-python.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python:v528"
docker save -o ai-writer-nostream-python.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python:v528

