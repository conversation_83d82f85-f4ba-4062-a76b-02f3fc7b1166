apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-config
  namespace: oa-llm
data:
  elasticsearch.yml: |
    cluster.name: es-docker-cluster
    node.name: node-1
    network.host: 0.0.0.0
    discovery.type: single-node
    xpack.security.enabled: true
    http.cors.enabled: true
    http.cors.allow-origin: "*"
    http.port: 9200
    transport.port: 9300
    xpack.security.authc:
      anonymous:
        username: anonymous_user
        roles: superuser
        authz_exception: true
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: elasticsearch
  namespace: oa-llm
  labels:
    k8s-app: elasticsearch
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s-app: elasticsearch
  template:
    metadata:
      labels:
        k8s-app: elasticsearch
    spec:
      containers:
      - image: {{elasticsearch.image-url}}
        name: elasticsearch
        resources:
          limits:
            cpu: 2
            memory: 2Gi
          requests:
            cpu: 2
            memory: 2Gi
        env:
          - name: ES_JAVA_OPTS
            value: "-Xms512m -Xmx2g"
          - name: "bootstrap.memory_lock"
            value: "true"
          - name: ELASTIC_PASSWORD
            value: "elastic@123"
        ports:
        - containerPort: 9200
          name: http
          protocol: TCP
        - containerPort: 9300
          name: transport
          protocol: TCP
        volumeMounts:
        - name: elasticsearch-data
          mountPath: /usr/share/elasticsearch/data
        - name: elasticsearch-logs
          mountPath: /usr/share/elasticsearch/logs
        - name: elasticsearch-config
          mountPath: /usr/share/elasticsearch/config/elasticsearch.yml
          subPath: elasticsearch.yml
      volumes:
      - name: elasticsearch-data
        emptyDir: {}
      - name: elasticsearch-logs
        emptyDir: {}
      - name: elasticsearch-config
        configMap:
          name: elasticsearch-config
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
---
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch
  namespace: oa-llm
spec:
  type: ClusterIP
  ports:
  - port: 9200
    protocol: TCP
    targetPort: 9200
    name: http
  - port: 9300
    protocol: TCP
    targetPort: 9300
    name: transport
  selector:
    k8s-app: elasticsearch 