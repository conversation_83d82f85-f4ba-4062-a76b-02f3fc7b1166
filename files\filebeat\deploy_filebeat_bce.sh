#!/bin/bash
echo "开始部署BCE日志收集的Filebeat"

# 创建必要目录结构
echo "创建必要目录结构: mkdir -p /data/filebeat/config"
mkdir -p /data/filebeat/config

# BCE日志目录
BCE_LOG_DIR="/data/embedding/embedding-service-bce/logs"

# ES连接信息
ES_HOST="**********"
ES_PORT="9200"
ES_USERNAME="elastic"
ES_PASSWORD="Elastic20250417@#"

# 检查BCE日志目录
echo "检查BCE日志目录是否存在..."
if [ -d "$BCE_LOG_DIR" ]; then
  echo "目录 $BCE_LOG_DIR 存在"
  ls -la $BCE_LOG_DIR/
  # 确保filebeat可以读取日志文件
  chmod -R o+r $BCE_LOG_DIR/
  echo "已修改权限，确保日志文件可读"
else
  echo "警告: BCE日志目录 $BCE_LOG_DIR 不存在，创建目录"
  mkdir -p $BCE_LOG_DIR
  chmod -R 755 $BCE_LOG_DIR
  echo "创建测试日志文件用于验证Filebeat配置"
  echo "[$(date +"%Y-%m-%d %H:%M:%S")] [Test] BCE测试日志内容" > $BCE_LOG_DIR/embedding_log_info
  chmod 644 $BCE_LOG_DIR/embedding_log_*
  echo "测试文件已创建: $BCE_LOG_DIR/embedding_log_info"
fi

# 停止并删除已有的BCE专用filebeat容器
echo "停止并删除已有的BCE专用filebeat容器"
docker rm -f filebeat-bce 2>/dev/null || true

# 创建Filebeat配置文件
cat > /data/filebeat/config/filebeat-bce.yml <<EOF
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /data/embedding/embedding-service-bce/logs/embedding_log_*
  tags: ["embedding", "bce"]
  
  # 添加多行处理配置
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 1000
    timeout: 5s
  
  # 添加处理器以解析JSON格式
  processors:
    - decode_json_fields:
        fields: ["message"]
        process_array: false
        max_depth: 3
        target: ""
        overwrite_keys: true
        when:
          contains:
            message: "{"

output.elasticsearch:
  hosts: ["http://${ES_HOST}:${ES_PORT}"]
  username: "${ES_USERNAME}"
  password: "${ES_PASSWORD}"
  index: "bce-embedding-%{+yyyy}"

# 禁用索引模板
setup.template.enabled: false

# 禁用ILM
setup.ilm.enabled: false

# 日志配置
logging.level: debug
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat-bce
  keepfiles: 7
  permissions: 0644
EOF

echo "Filebeat BCE配置文件创建完成: /data/filebeat/config/filebeat-bce.yml"

# 检查ES连接
echo "检查ES连接..."
if curl -s -m 5 -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}" | grep -q "You Know"; then
  echo "✅ 成功连接到Elasticsearch"
else
  echo "⚠️ 无法连接到Elasticsearch，请检查连接设置和网络。继续部署..."
fi

# 启动Filebeat BCE专用容器
echo "启动Filebeat BCE专用容器"
docker run -d \
  --name=filebeat-bce \
  --user=root \
  --volume="/data/filebeat/config/filebeat-bce.yml:/usr/share/filebeat/filebeat.yml:ro" \
  --volume="$BCE_LOG_DIR:/data/embedding/embedding-service-bce/logs:ro" \
  --net=host \
  docker.elastic.co/beats/filebeat:7.17.14 \
  filebeat -e --strict.perms=false

# 检查容器是否启动成功
echo "检查容器是否启动成功"
if docker ps | grep -q filebeat-bce; then
  echo "Filebeat BCE容器启动成功"
else
  echo "Filebeat BCE容器启动失败"
  docker logs filebeat-bce
  exit 1
fi

# 等待Filebeat启动并处理日志
echo "等待30秒，让Filebeat有足够时间处理日志..."
sleep 30

# 检查BCE日志文件权限和内容
echo "-----------------------------------检查BCE日志文件-----------------------------------"
docker exec filebeat-bce ls -la /data/embedding/embedding-service-bce/logs/
echo "-----------------------------------检查BCE日志文件内容-----------------------------------"
docker exec filebeat-bce head -n 5 /data/embedding/embedding-service-bce/logs/embedding_log_info

# 查看filebeat日志
echo "-----------------------------------查看Filebeat BCE日志-----------------------------------"
docker logs filebeat-bce --tail 20

# 检查索引是否创建
echo "验证索引是否创建..."
current_year=$(date +%Y)
echo "查看所有BCE索引："
curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://${ES_HOST}:${ES_PORT}/_cat/indices/bce-embedding*?v"

# 查询BCE索引数据
echo "-----------------------------------查询BCE索引数据:-----------------------------------"
curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://${ES_HOST}:${ES_PORT}/bce-embedding-${current_year}/_search?q=*&size=1"

# 测试直接向ES发送测试数据
echo "-----------------------------------测试直接向ES发送BCE索引数据-----------------------------------"
TEST_INDEX="bce-embedding-$(date +"%Y")-test"
TEST_DOC='{
  "@timestamp": "'$(date -Iseconds)'",
  "message": "测试BCE日志记录",
  "type": "bce-embedding",
  "tags": ["test", "embedding", "bce"]
}'
echo "发送测试数据到 $TEST_INDEX"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" -X POST "http://${ES_HOST}:${ES_PORT}/${TEST_INDEX}/_doc" -H 'Content-Type: application/json' -d "$TEST_DOC"

echo ""
echo "===== 检查要点 ====="
echo "1. 确认BCE日志文件路径正确"
echo "2. 确认filebeat-bce容器已启动并运行"
echo "3. 检查日志格式是否符合multiline配置"
echo "4. 验证ES索引是否创建"
echo "5. 如有问题，检查详细日志: docker logs filebeat-bce"
echo ""
echo "Filebeat BCE部署完成，将单独收集BCE日志并发送到Elasticsearch" 