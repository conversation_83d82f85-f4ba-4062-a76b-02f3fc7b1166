#!/bin/bash
echo "修复OpenSearch索引映射问题"

# 删除现有索引
echo "删除现有索引..."
for index in "rbac-2025" "qa-2025" "aiknowledge-controller-2025"; do
  echo "删除索引 $index"
  curl -k -u admin:admin -X DELETE "https://10.1.4.16:9200/$index"
done

# 创建索引模板
echo "创建索引模板..."
curl -k -u admin:admin -X PUT "https://10.1.4.16:9200/_template/filebeat_template" -H 'Content-Type: application/json' -d'
{
  "index_patterns": ["rbac-*", "qa-*", "aiknowledge-controller-*"],
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 0
  },
  "mappings": {
    "properties": {
      "@timestamp": { 
        "type": "date"
      },
      "message": {
        "type": "text"
      },
      "fields": {
        "properties": {
          "type": { "type": "keyword" },
          "index": { "type": "keyword" }
        }
      },
      "log": {
        "properties": {
          "level": { "type": "keyword" },
          "logger": { "type": "keyword" },
          "origin": {
            "properties": {
              "function": { "type": "keyword" },
              "file": { 
                "properties": {
                  "name": { "type": "keyword" },
                  "line": { "type": "long" }
                }
              }
            }
          }
        }
      }
    }
  }
}'

# 创建新索引
echo "创建新索引..."
for index in "rbac-2025" "qa-2025" "aiknowledge-controller-2025"; do
  echo "创建索引 $index"
  curl -k -u admin:admin -X PUT "https://10.1.4.16:9200/$index"
done

echo "索引映射修复完成，现在重启Filebeat..."
bash deploy/filebeat/restart_filebeat.sh 