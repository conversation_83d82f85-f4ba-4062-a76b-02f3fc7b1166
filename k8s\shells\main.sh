# bin/bash
# 获取当前目录

CURRENT_DIR=$(pwd)
# 获取父目录
PARENT_DIR=$(dirname ${CURRENT_DIR})
# 询问是否需要创建config.json文件
read -p "是否需要创建config.json文件？(y/n): " CREATE_CONFIG_FILE
if [ "${CREATE_CONFIG_FILE}" == "y" ]; then
    echo "开始创建config.json文件"
    sh ${CURRENT_DIR}/create_config_file.sh
fi
# 读取config.json文件
CONFIG_FILE="${CURRENT_DIR}/config.json"
if [ ! -f ${CONFIG_FILE} ]; then
    echo "config.json文件不存在"
    exit 1
fi

# 下载镜像
read -p "是否需要下载镜像？(y/n): " DOWNLOAD_IMAGES
if [ "${DOWNLOAD_IMAGES}" == "y" ]; then
    sh ${CURRENT_DIR}/download_images.sh
fi
# 是否推送镜像
read -p "是否需要推送镜像？(y/n): " PUSH_IMAGES
if [ "${PUSH_IMAGES}" == "y" ]; then
    sh ${CURRENT_DIR}/push_images.sh
fi  
# 是否将镜像保存到本地
read -p "是否需要将镜像保存到本地？(y/n): " SAVE_IMAGES
if [ "${SAVE_IMAGES}" == "y" ]; then
    sh ${CURRENT_DIR}/save_images.sh
fi

# 替换yml文件
read -p "是否需要替换yml文件？(y/n): " REPLACE_YML
if [ "${REPLACE_YML}" == "y" ]; then
    sh ${CURRENT_DIR}/replace_yml.sh
fi

# 部署服务
read -p "是否需要部署服务？(y/n): " APPLY_SERVICE
if [ "${APPLY_SERVICE}" == "y" ]; then
    sh ${CURRENT_DIR}/apply.sh
fi








