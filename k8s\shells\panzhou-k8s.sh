#kubectl apply -f  ${PARENT_DIR}/k8s/yml/middleware/elasticsearch-nostorage.yml
kubectl apply -f  k8s/yml/panzhou/middleware/kibana.yml
kubectl apply -f  k8s/yml/panzhou/middleware/frontend.yml
kubectl apply -f  k8s/yml/panzhou/middleware/opensearch.yml
kubectl apply -f  k8s/yml/panzhou/middleware/opensearch-dashboards.yml
kubectl apply -f  k8s/yml/panzhou/middleware/rabbitmq.yml
kubectl apply -f  k8s/yml/panzhou/middleware/zookeeper.yml

kubectl apply -f  k8s/yml/panzhou/middleware/postgresql.yml
kubectl apply -f  k8s/yml/panzhou/middleware/postgresql-localpath.yml
kubectl apply -f  k8s/yml/panzhou/middleware/redis.yml
kubectl apply -f  k8s/yml/panzhou/middleware/minio.yml
kubectl apply -f  k8s/yml/panzhou/applications/article-auto-auditor-server.yml
kubectl apply -f  k8s/yml/panzhou/applications/article-auto-compose-server.yml
kubectl apply -f  k8s/yml/panzhou/applications/ai-doc-poc.yml

kubectl apply -f  k8s/yml/panzhou/applications/poc-intelligence-view.yml
kubectl apply -f  k8s/yml/panzhou/applications/poc-intelligence-view-write.yml
kubectl apply -f  k8s/yml/panzhou/applications/jtws.yml

kubectl apply -f  k8s/yml/panzhou/applications/ai-redactor.yml
kubectl apply -f  k8s/yml/panzhou/applications/ai-writer-nostream.yml
