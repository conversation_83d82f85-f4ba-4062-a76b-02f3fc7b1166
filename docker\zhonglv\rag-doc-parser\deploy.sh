#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色
APP_VERSION="v1"

APP_NAME="rag-doc-parser"
MODULE_NAME="znwd"
DEPLOY_DIR="/data/build/$APP_NAME"
DOCKERFILE_NAME="Dockerfile"
DOCKERFILE_PATH="$DEPLOY_DIR/$DOCKERFILE_NAME"
PORT="8098"
APP_DIR="/data/$MODULE_NAME/$APP_NAME"
#APP_CONFIG_PATH="$APP_DIR/config/application.properties"
APP_START_SCRIPT="$APP_DIR/shells/start.sh"
APP_RESTART_SCRIPT="$APP_DIR/restart.sh"
APP_TEST_CURL_SCRIPT="$APP_DIR/test_curl.sh"
APP_LOGS_PATH="$APP_DIR/logs"
APP_SHELLS_PATH="$APP_DIR/shells"
PROJECT_DIR_NAME="web_source_code"
PROJECT_DIR="$DEPLOY_DIR/$PROJECT_DIR_NAME"

# 容器内信息
# 容器启动端口
CONTAINER_START_PORT="8000"
# 容器启动脚本
CONTAINER_START_SCRIPT="start.sh"
# 容器日志路径
CONTAINER_LOG_PATH="/app/web_source_code/log"
# 容器数据路径
CONTAINER_DATA_PATH="/app/web_source_code/local_data"
# 容器shell脚本路径
CONTAINER_START_SCRIPT="/app/shells/start.sh"
BASE_URL="http://************:30002/stream-jwt/CIDC-RP-207/inference-proxy/2185090615607296/aiops-1340745253490184192"
MODEL_VL_URL="$BASE_URL/qwen25-vl-7b/service/8080/v1/chat/completions"
MODEL_OCR_URL=""




echo  "=================容器内信息==================="
echo "容器内启动端口: $CONTAINER_START_PORT"
echo "容器内启动脚本: $CONTAINER_START_SCRIPT"
#echo "容器内配置文件路径: $CONTAINER_CONFIG_PATH"
echo "容器内日志路径: $CONTAINER_LOG_PATH"
echo "容器内数据路径: $CONTAINER_DATA_PATH"
echo "容器内shell脚本路径: $CONTAINER_SHELLS_PATH"
echo "模型VL地址: $MODEL_VL_URL"
echo "项目目录名称: $PROJECT_DIR_NAME"
echo "=================容器内信息==================="


echo  "=================容器宿主机信息==================="
echo "容器名称: $APP_NAME"
echo "容器宿主机端口: $PORT"
echo "容器宿主机目录: $APP_DIR"
#echo "容器宿主机配置文件路径: $APP_CONFIG_PATH"
echo "容器宿主机启动脚本: $APP_START_SCRIPT"
echo "容器宿主机重启脚本: $APP_RESTART_SCRIPT"
echo "容器宿主机测试curl脚本: $APP_TEST_CURL_SCRIPT"
echo "容器宿主机日志路径: $APP_LOGS_PATH"
echo "容器宿主机shell脚本路径: $APP_SHELLS_PATH"
echo "项目目录名称: $PROJECT_DIR_NAME"
echo "=================容器宿主机信息==================="

echo  "=================容器宿主机部署信息==================="
echo "容器宿主机部署目录: $DEPLOY_DIR"
echo "容器宿主机Dockerfile路径: $DOCKERFILE_PATH"
tree $DEPLOY_DIR
echo "=================容器宿主机部署信息==================="   


echo -e "${BLUE}=== $APP_NAME 构建和运行脚本 ===${NC}"

# 创建必要的目录
echo -e "${YELLOW}创建必要的目录...${NC}"
mkdir -p $APP_DIR
rm -rf $APP_DIR/*
mkdir -p $APP_DIR/logs
mkdir -p $APP_DIR/data
mkdir -p $APP_DIR/config
mkdir -p $APP_DIR/shells
chmod 755 -R $APP_DIR

#复制DEPLOY_DIR下application.properties文件到APP_DIR/config/application.properties
echo -e "${YELLOW}复制application.properties文件...${NC}"
cp $DEPLOY_DIR/application.properties $APP_DIR/config/application.properties
#复制DEPLOY_DIR下start.sh文件到APP_DIR/shells/start.sh
echo -e "${YELLOW}复制start.sh文件...${NC}"
cp $DEPLOY_DIR/start.sh $APP_DIR/shells/start.sh
# 确保shell脚本有执行权限
chmod +x $APP_DIR/shells/start.sh
echo -e "${YELLOW}复制restart.sh文件...${NC}"
cp $DEPLOY_DIR/restart.sh $APP_DIR/restart.sh
chmod +x $APP_DIR/restart.sh
echo -e "${YELLOW}显示目录结构...${NC}"
#复制test_curl.sh文件到APP_DIR/test_curl.sh
echo -e "${YELLOW}复制test_curl.sh文件...${NC}"
cp $DEPLOY_DIR/test_curl.sh $APP_DIR/test_curl.sh
chmod +x $APP_DIR/test_curl.sh
echo -e "${YELLOW}显示$APP_DIR目录结构...${NC}"
# 复制web_source_code目录到APP_DIR/web_source_code
echo -e "${YELLOW}复制web_source_code目录...${NC}"
cp -r $DEPLOY_DIR/web_source_code $APP_DIR/web_source_code
tree $APP_DIR
# 检查是否存在同名容器并停止
CONTAINER_NAME="$APP_NAME"
echo -e "${YELLOW}检查是否存在同名容器...${NC}"
if docker ps -a --format '{{.Names}}' | grep -q "^$CONTAINER_NAME$"; then
    echo -e "${YELLOW}发现已存在的$CONTAINER_NAME容器，正在停止并删除...${NC}"
    docker stop $CONTAINER_NAME
    docker rm -f  $CONTAINER_NAME
fi

# 构建镜像
echo "构建镜像"
# 判断是否重新制作镜像,默认是重新制作
# 判断是否重新制作镜像,默认是重新制作
read -p "是否重新制作镜像? (y/n): " REBUILD_IMAGE
if [ "$REBUILD_IMAGE" = "y" ]; then
    # 删除之前的镜像
    echo -e "${YELLOW}删除之前的镜像...${NC}"
    #如果镜像存在，则删除   
    if docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^$MODULE_NAME/$APP_NAME:$APP_VERSION$"; then
        docker rmi -f  $MODULE_NAME/$APP_NAME:$APP_VERSION 
    fi
    # 将none镜像删除
    echo -e "${YELLOW}删除none镜像...${NC}"
    docker rmi -f $(docker images -f "dangling=true" -q)
    
    # 构建Docker镜像
    echo -e "${YELLOW}构建Docker镜像...${NC}"
    
    # 需要将embedding_service_x86目录复制到当前目录作为构建上下文
    echo -e "${YELLOW}准备构建上下文...${NC}"
    BUILD_CONTEXT="/tmp/docker-build-context-$RANDOM"
    mkdir -p $BUILD_CONTEXT
    
    # 复制Dockerfile和源代码目录到构建上下文
    cp $DOCKERFILE_PATH $BUILD_CONTEXT/
    cp -r $PROJECT_DIR $BUILD_CONTEXT/$PROJECT_DIR_NAME
    cp $DEPLOY_DIR/start.sh $BUILD_CONTEXT/start.sh
    cp -r $DEPLOY_DIR/requirements.txt $BUILD_CONTEXT/requirements.txt
    cp -r $DEPLOY_DIR/environment.yml $BUILD_CONTEXT/environment.yml
    
    # 切换到构建上下文目录
    cd $BUILD_CONTEXT
    
    echo "docker build -t $MODULE_NAME/$APP_NAME:$APP_VERSION -f $DOCKERFILE_NAME . --build-arg APP_NAME_IN_CONTAINER=$PROJECT_DIR_NAME --build-arg APP_NAME_OUTSIDE=$PROJECT_DIR_NAME"
    docker build -t $MODULE_NAME/$APP_NAME:$APP_VERSION -f $DOCKERFILE_NAME . --build-arg APP_NAME_IN_CONTAINER=$PROJECT_DIR_NAME --build-arg APP_NAME_OUTSIDE=$PROJECT_DIR_NAME 

    if [ $? -ne 0 ]; then
        echo -e "${RED}镜像构建失败，请检查错误信息${NC}"
        # 清理构建上下文
        cd -
        rm -rf $BUILD_CONTEXT
        exit 1
    fi

    # 清理构建上下文
    cd -
    rm -rf $BUILD_CONTEXT
    
    echo -e "${GREEN}镜像构建成功!${NC}"
else
    echo -e "${YELLOW}本次部署不重新制作镜像${NC}"
fi




# 运行容器
echo -e "${YELLOW}启动容器...${NC}"
echo "docker run -d --name $CONTAINER_NAME \
    --restart always \
    --network jiutianwensi \
    -p $PORT:$CONTAINER_START_PORT \
    -v $APP_LOGS_PATH:$CONTAINER_LOG_PATH \
    -v $APP_SHELLS_PATH/start.sh:$CONTAINER_START_SCRIPT \
    -e TZ=Asia/Shanghai \
    -e MODEL_VL_URL=$MODEL_VL_URL \
    -e MODEL_OCR_URL=$MODEL_OCR_URL \
    $MODULE_NAME/$APP_NAME:$APP_VERSION \
    sh $CONTAINER_START_SCRIPT"
    
docker run -d --name $CONTAINER_NAME \
    --restart always \
    --network jiutianwensi \
    -p $PORT:$CONTAINER_START_PORT \
    -v $APP_LOGS_PATH:$CONTAINER_LOG_PATH \
    -v $APP_SHELLS_PATH/start.sh:$CONTAINER_START_SCRIPT \
    -e TZ=Asia/Shanghai \
    -e MODEL_VL_URL=$MODEL_VL_URL \
    -e MODEL_OCR_URL=$MODEL_OCR_URL \
    $MODULE_NAME/$APP_NAME:$APP_VERSION \
    sh $CONTAINER_START_SCRIPT
if [ $? -ne 0 ]; then
    echo -e "${RED}容器启动失败，请检查错误信息${NC}"
    exit 1
fi

echo -e "${GREEN}容器启动成功!${NC}"

# 显示容器状态
docker ps -f name=$CONTAINER_NAME

echo -e "${BLUE}=== 部署完成 ===${NC}"
echo "可以通过以下命令查看容器日志:"
echo "docker logs $CONTAINER_NAME"
echo
echo "应用访问地址: http://localhost:$PORT"
echo

echo "设置日志和数据目录权限"
echo "chmod 777 $APP_DIR/logs"
chmod 777 $APP_DIR/logs
echo "chmod 777 $APP_DIR/data"
chmod 777 $APP_DIR/data

sleep 10
#从容器中的/app/web_source_code目录拷贝test1.docx文件到APP_DIR/data目录
echo -e "${YELLOW}从容器中的/app/web_source_code目录拷贝test1.docx文件到APP_DIR/data目录...${NC}"
docker cp -r  $CONTAINER_NAME:/app/web_source_code/test1.docx $APP_DIR/test1.docx
cp -r $APP_DIR/test1.docx $DEPLOY_DIR/test1.docx


./test_curl.sh

echo "健康检查API访问地址: http://localhost:$PORT/health"