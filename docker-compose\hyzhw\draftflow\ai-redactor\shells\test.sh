   #!/bin/bash
    # 颜色设置
    GREEN='\033[0;32m'
    BLUE='\033[0;34m'
    RED='\033[0;31m'
    YELLOW='\033[1;33m'
    NC='\033[0m' # 无颜色

    # 读取配置文件
    config_file="/app/ai_hegao_plus_app/application.properties"
    model_url=$(grep "model-url=" $config_file | cut -d'=' -f2)
    model_name=$(grep "model-name=" $config_file | cut -d'=' -f2)
    api_key=$(grep "api-key=" $config_file | cut -d'=' -f2)

    # 读取测试文本并进行转义处理
    test_text=$(cat test.txt | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g' | sed 's/\r//g')

    # ====================== 大模型接口测试 ======================
    echo -e "${BLUE}=== 大模型接口测试 ===${NC}"
    echo "model_url: $model_url"
    echo "model_name: $model_name"
    echo "api_key: $api_key"

    # 读取prompt内容并进行转义处理
    prompt_content=$(cat prompt.txt | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g' | sed 's/\r//g')

    # 构造JSON数据
    json_data='{
      "model": "'$model_name'",
      "messages": [
        {
          "role": "system",
          "content": "'$prompt_content'"
        },
        {
          "role": "user",
          "content": "'$test_text'"
        }
      ]
    }'

    cmd="curl -v -X POST \"${model_url}\" \
      -H \"Content-Type: application/json\" \
      -H \"Authorization: Bearer ${api_key}\" \
      -d '$json_data'"

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行大模型请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}大模型请求耗时: $((end_time - start_time)) 秒${NC}"

    # ====================== 本服务接口测试 ======================
    echo -e "${BLUE}=== 本服务接口测试 ===${NC}"
    
    # 构造本服务请求的JSON数据
    local_json='{
      "content": "'$test_text'",
      "type_list": [],
      "user_id": "123"
    }'

    cmd="curl -v -X POST \"http://localhost:8000/spellcheck/oa\" \
      -H \"Content-Type: application/json\" \
      -d '$local_json'"

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行本服务请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}本服务请求耗时: $((end_time - start_time)) 秒${NC}"
    echo
    echo -e "${GREEN}=== 所有测试完成 ===${NC}"