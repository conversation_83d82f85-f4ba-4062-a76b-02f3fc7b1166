version: '3'

services:
  ai-doc-poc:
    image: redactor/ai-doc-poc:0.0.1-SNAPSHOT
    container_name: ai-doc-poc
    ports:
      - "${PORT:-8093}:8080"
    volumes:
      - ${BASE_DIR:-/data/redactor}/ai-doc-poc/config:/app/config
      - ${BASE_DIR:-/data/redactor}/ai-doc-poc/logs:/app/logs
      - ${BASE_DIR:-/data/redactor}/ai-doc-poc/data:/app/files
      - ${BASE_DIR:-/data/redactor}/ai-doc-poc/shells:/app/shells
    environment:
      - JAVA_OPTS=-Xms512m -Xmx1g
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always

networks:
  jiutianwensi-network:
    external: true 