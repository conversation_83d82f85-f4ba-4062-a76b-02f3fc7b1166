#!/bin/bash
# DeepSeek-70B 镜像部署一键脚本（适用于麒麟/ARM环境）

# ========== 变量定义 ==========
IMAGE_TAR="/home/<USER>/docker.tar"   # 镜像tar包路径
IMAGE_NAME="deepseek-70b:latest"                             # 镜像名（可用 docker images 查看）
CONTAINER_NAME="deepseek70b"                                 # 容器名称
WEIGHTS_PATH="/path-to-weights"                              # 权重文件主机路径
MODEL_WEIGHT_PATH="/data/DeepSeek-R1-Distill-Llama-70B"      # 容器内权重路径
DRIVER_PATH="/usr/local/Ascend/driver"                       # Ascend驱动路径
MINDIE_PATH="/usr/local/Ascend/mindie/latest/mindie-service" # mindie服务路径

# ========== 1. 镜像解压 ==========
echo "加载docker镜像..."
docker load -i ${IMAGE_TAR}

# ========== 2. 启动容器 ==========
echo "启动容器..."
docker run -it -d --privileged=true \
  --net=host --shm-size=1g \
  --name ${CONTAINER_NAME} \
  --device=/dev/davinci_manager \
  --device=/dev/hisi_hdc \
  --device=/dev/devmm_svm \
  --device=/dev/davinci0 \
  --device=/dev/davinci1 \
  --device=/dev/davinci2 \
  --device=/dev/davinci3 \
  --device=/dev/davinci4 \
  --device=/dev/davinci5 \
  --device=/dev/davinci6 \
  --device=/dev/davinci7 \
  -v ${DRIVER_PATH}:${DRIVER_PATH}:ro \
  -v /usr/local/sbin:/usr/local/sbin:ro \
  -v ${WEIGHTS_PATH}:${MODEL_WEIGHT_PATH}:ro \
  ${IMAGE_NAME} bash

# ========== 3. 进入容器 ==========
echo "进入容器..."
docker exec -it ${CONTAINER_NAME} bash

# ========== 4. 复制模型文件到容器目录 ==========
echo "请确保主机和容器都已安装rsync，容器内已新建 /data 目录"
echo "正在同步模型权重到容器..."
rsync -aH ${WEIGHTS_PATH}/ /data/docker/

# ========== 5. 修改配置文件 ==========
echo "请手动修改配置文件："
echo "vim ${MINDIE_PATH}/conf/config.json"
echo "（根据实际情况调整模型路径、端口等参数）"

# ========== 6. 启动服务 ==========
echo "启动mindie服务..."
cd ${MINDIE_PATH}/bin
./mindieservice_daemon

# ========== 7. 测试接口 ==========
echo "测试VLLM接口："
echo "curl 127.0.0.1:1025/generate -d '{\"prompt\": \"What is deep learning?\", \"max_tokens\": 32, \"stream\": false, \"do_sample\":true, \"repetition_penalty\": 1.00, \"temperature\": 0.01, \"top_p\": 0.001, \"top_k\": 1, \"model\": \"llama\"}'"

echo "全部流程已完成，请根据提示完成后续操作。"
