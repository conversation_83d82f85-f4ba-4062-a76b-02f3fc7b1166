#!/bin/bash
# Nginx反向代理配置生成脚本
set -e  # 遇到错误立即退出

# 获取脚本所在目录的绝对路径
CURRENT_DIR=$(cd "$(dirname "$0")" && pwd)
PARENT_DIR=$(dirname "${CURRENT_DIR}")

# 统一定义路径
CONFIG_FILE="${CURRENT_DIR}/config.json"
YML_DIR="${PARENT_DIR}/yml"  # 根据实际目录结构调整
TEMP_DIR="${CURRENT_DIR}/nginx-config"

# 加载工具函数
source "${CURRENT_DIR}/utils/utils.sh"

# 检查依赖
command -v jq >/dev/null 2>&1 || { echo "错误: 需要安装jq"; exit 1; }
command -v kubectl >/dev/null 2>&1 || { echo "错误: 需要安装kubectl"; exit 1; }

# 创建临时目录存放配置
mkdir -p ${TEMP_DIR}/conf.d

# 读取配置
[ ! -f "${CONFIG_FILE}" ] && { echo "错误: 配置文件不存在 ${CONFIG_FILE}"; exit 1; }
REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})

# 读取Nginx全局配置
PROXY_MODEL=$(jq -r '.["proxy-config"]["proxy-model"]' ${CONFIG_FILE})
NGINX_MODE=$(jq -r '.["proxy-config"]["nginx-config"]["mode"]' ${CONFIG_FILE})
NGINX_IMAGE=$(jq -r '.["proxy-config"]["nginx-config"]["image-url"]' ${CONFIG_FILE})


# 检查是否为空，设置默认值

[[ "$NGINX_MODE" == "null" ]] && NGINX_MODE="port"
# 如果NGINX_IMAGE为空，则报错，退出
[[ "$NGINX_IMAGE" == "null" ]] && { echo "错误: NGINX镜像地址为空"; exit 1; }

echo "配置信息:"
echo "代理模式: $PROXY_MODEL"
echo "Nginx模式: $NGINX_MODE"
echo "Nginx镜像: $NGINX_IMAGE"

# 创建主配置文件
cat > ${TEMP_DIR}/nginx.conf <<EOF
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                      '\$status \$body_bytes_sent "\$http_referer" '
                      '"\$http_user_agent" "\$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;
    sendfile        on;
    keepalive_timeout  3000;
    keepalive_requests 1024;
    proxy_connect_timeout 3000;
    proxy_read_timeout 3000;
    proxy_send_timeout 3000;

    # 服务代理配置
    include /etc/nginx/conf.d/*.conf;
}
EOF

# 先清空${TEMP_DIR}/conf.d/
echo "清空所有的配置：${TEMP_DIR}/conf.d/"
rm -rf ${TEMP_DIR}/conf.d/*

# 循环处理每个服务
for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
    # 获取配置
    SERVICE_NAME=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
    YML_FILE_NAME=$(jq -r ".[\"replace-config\"][$i][\"yml-file-name\"]" ${CONFIG_FILE})
    TYPE=$(jq -r ".[\"replace-config\"][$i][\"type\"]" ${CONFIG_FILE})
    PROXY_ENABLE=$(jq -r ".[\"replace-config\"][$i][\"proxy-enable\"]" ${CONFIG_FILE})
    PROXY_PORT=$(jq -r ".[\"replace-config\"][$i][\"proxy-port\"]" ${CONFIG_FILE})

    if [ "$PROXY_ENABLE" == "false" ]; then
        echo "服务: ${SERVICE_NAME} 不启用代理"
        continue
    fi

    # 读取YAML文件
    YML_FILE_PATH="${YML_DIR}/template/${TYPE}/${YML_FILE_NAME}"
    if [ ! -f "${YML_FILE_PATH}" ]; then
        echo "警告: YAML文件不存在 ${YML_FILE_PATH}"
        continue
    fi
    
    echo "正在处理服务: ${SERVICE_NAME}, 配置文件: ${YML_FILE_PATH}"
    
    # 获取服务端口
    TARGET_PORT=$(grep -A5 "ports:" ${YML_FILE_PATH} | grep "targetPort:" | head -1 | awk '{print $2}' | tr -d '[:space:]')
    if [ -z "${TARGET_PORT}" ]; then
        TARGET_PORT=$(grep -A5 "ports:" ${YML_FILE_PATH} | grep "port:" | head -1 | awk '{print $2}' | tr -d '[:space:]')
    fi
    
    if [ -z "${TARGET_PORT}" ]; then
        echo "警告: ${SERVICE_NAME}服务的${YML_FILE_NAME}文件中没有找到端口信息"
        continue
    fi
    
    # 确保端口是纯数字
    if ! [[ "$TARGET_PORT" =~ ^[0-9]+$ ]]; then
        echo "警告: ${SERVICE_NAME}服务的端口格式不正确: ${TARGET_PORT}"
        echo "尝试清理端口值..."
        TARGET_PORT=$(echo "$TARGET_PORT" | sed 's/[^0-9]//g')
        if [ -z "${TARGET_PORT}" ] || ! [[ "$TARGET_PORT" =~ ^[0-9]+$ ]]; then
            echo "错误: 无法获取有效的端口号，将使用默认端口80"
            TARGET_PORT="80"
        else
            echo "已修正端口值为: ${TARGET_PORT}"
        fi
    fi
    
    echo "服务: ${SERVICE_NAME} YML文件: ${YML_FILE_NAME} 提取端口: ${TARGET_PORT}"
    
    # 根据模式创建不同的Nginx配置
    if [ "$NGINX_MODE" == "port" ]; then
        # 使用NodePort模式
        cat > ${TEMP_DIR}/conf.d/${SERVICE_NAME}.conf <<EOF
server {
    listen ${PROXY_PORT};
    
    location / {
        proxy_pass http://${SERVICE_NAME}.oa-llm:${TARGET_PORT};
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        
        # websocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 3000s;
        proxy_read_timeout 3000s;
        proxy_send_timeout 3000s;
    }
}
EOF
    else
        # 使用主机名模式
        DOMAIN_SUFFIX=$(jq -r '.["proxy-config"]["nginx-config"]["service-name-suffix"]' ${CONFIG_FILE})
        [[ "$DOMAIN_SUFFIX" == "null" ]] && DOMAIN_SUFFIX="oallm.jtws.com"
        NGINX_SERVICE_NAME_PORT=$(jq -r '.["proxy-config"]["nginx-config"]["service-name-config"]["port"]' ${CONFIG_FILE})
        
        cat > ${TEMP_DIR}/conf.d/${SERVICE_NAME}.conf <<EOF
server {
    listen ${NGINX_SERVICE_NAME_PORT};
    server_name ${SERVICE_NAME}.${DOMAIN_SUFFIX};
    
    location / {
        proxy_pass http://${SERVICE_NAME}.oa-llm:${TARGET_PORT};
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        
        # websocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 3000s;
        proxy_read_timeout 3000s;
        proxy_send_timeout 3000s;
    }
}
EOF
    fi
done

# 创建默认服务器
cat > ${TEMP_DIR}/conf.d/default.conf <<EOF
server {
    listen 80 default_server;
    server_name _;
    
    location / {
        return 404 "Service not found";
    }
}
EOF

echo "生成的Nginx配置文件已保存到 ${TEMP_DIR}"

# 检查是否存在nginx-config ConfigMap，如果存在则删除
echo "检查是否存在nginx配置的ConfigMap"
if kubectl get configmap nginx-conf -n oa-llm &>/dev/null; then
    echo "删除已存在的nginx-conf ConfigMap"
    kubectl delete configmap nginx-conf -n oa-llm
fi

if kubectl get configmap nginx-confd -n oa-llm &>/dev/null; then
    echo "删除已存在的nginx-confd ConfigMap"
    kubectl delete configmap nginx-confd -n oa-llm
fi

# 创建ConfigMap
echo "创建ConfigMap"
kubectl create configmap nginx-conf --from-file=nginx.conf=${TEMP_DIR}/nginx.conf -n oa-llm
kubectl create configmap nginx-confd --from-file=${TEMP_DIR}/conf.d/ -n oa-llm

# 判断 nginx-proxy 是否存在
if kubectl get deployment nginx-proxy -n oa-llm &>/dev/null; then
    echo "删除已存在的nginx-proxy Deployment"
    kubectl delete deployment nginx-proxy -n oa-llm
fi

# 创建deployment
echo "部署Nginx"
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-proxy
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx-proxy
  template:
    metadata:
      labels:
        app: nginx-proxy
    spec:
      containers:
      - name: nginx
        image: ${NGINX_IMAGE}
        ports:
        - containerPort: 80
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 128Mi
        volumeMounts:
        - name: nginx-conf
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
          readOnly: true
        - name: nginx-confd
          mountPath: /etc/nginx/conf.d
          readOnly: true
      imagePullSecrets:
      - name: oa-llm-imagepullsecret    
      volumes:
      - name: nginx-conf
        configMap:
          name: nginx-conf
      - name: nginx-confd
        configMap:
          name: nginx-confd
EOF

# 判断 nginx-proxy 是否存在
if kubectl get service nginx-proxy -n oa-llm &>/dev/null; then
    echo "删除已存在的nginx-proxy Service"
    kubectl delete service nginx-proxy -n oa-llm
fi

# 创建或更新服务
echo "创建/更新服务"
if [ "$NGINX_MODE" == "port" ]; then
    # 使用NodePort模式，为每个服务创建单独的Service
    for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
        SERVICE_NAME=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
        PROXY_ENABLE=$(jq -r ".[\"replace-config\"][$i][\"proxy-enable\"]" ${CONFIG_FILE})
        PROXY_PORT=$(jq -r ".[\"replace-config\"][$i][\"proxy-port\"]" ${CONFIG_FILE})
        
        if [ "$PROXY_ENABLE" != "false" ]; then
            echo "为服务 ${SERVICE_NAME} 创建NodePort服务，端口: ${PROXY_PORT}"
            kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: ${SERVICE_NAME}-proxy
  namespace: oa-llm
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: ${PROXY_PORT}
    nodePort: ${PROXY_PORT}
    protocol: TCP
  selector:
    app: nginx-proxy
EOF
        fi
    done
else
    NGINX_SERVICE_NAME_PORT=$(jq -r '.["proxy-config"]["nginx-config"]["service-name-config"]["port"]' ${CONFIG_FILE})
    echo "Nginx Server Name 模式服务端口: ${NGINX_SERVICE_NAME_PORT}"
    # 使用主机名模式，创建单个Service
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: nginx-proxy
  namespace: oa-llm
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 80
    nodePort: ${NGINX_SERVICE_NAME_PORT}
    protocol: TCP
  selector:
    app: nginx-proxy
EOF
fi

# 等待服务启动
echo "等待Nginx服务就绪..."
wait_for_service "nginx-proxy" "deployment" "oa-llm" 20

# 检查服务是否已经启动
if [ "$IS_RUNNING" = "false" ]; then
    handle_service_timeout "nginx-proxy" "deployment" "oa-llm"
fi

# 获取节点IP
NODE_IP=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')

echo "================================================"
echo "Nginx代理已部署完成！"
echo "访问指南："

if [ "$NGINX_MODE" == "port" ]; then
    echo "使用NodePort模式，通过以下地址访问各服务："
    for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
        SERVICE_NAME=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
        PROXY_ENABLE=$(jq -r ".[\"replace-config\"][$i][\"proxy-enable\"]" ${CONFIG_FILE})
        PROXY_PORT=$(jq -r ".[\"replace-config\"][$i][\"proxy-port\"]" ${CONFIG_FILE})
        
        if [ "$PROXY_ENABLE" != "false" ]; then
            echo "   - ${SERVICE_NAME}: http://${NODE_IP}:${PROXY_PORT}"
        fi
    done
else
    echo "使用主机名模式，请配置hosts文件或DNS解析，将服务域名指向节点IP: ${NODE_IP}"
    DOMAIN_SUFFIX=$(jq -r '.["proxy-config"]["nginx-config"]["service-name-suffix"]' ${CONFIG_FILE})
    [[ "$DOMAIN_SUFFIX" == "null" ]] && DOMAIN_SUFFIX="oallm.jtws.com"
    
    echo "通过以下地址访问各服务："
    for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
        SERVICE_NAME=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
        PROXY_ENABLE=$(jq -r ".[\"replace-config\"][$i][\"proxy-enable\"]" ${CONFIG_FILE})
        
        if [ "$PROXY_ENABLE" != "false" ]; then
            echo "   - ${SERVICE_NAME}: http://${SERVICE_NAME}.${DOMAIN_SUFFIX}:30001"
        fi
    done
fi

echo "================================================"

# 检查服务状态
echo "检查Nginx服务状态..."
check_k8s_resource "nginx-proxy" "oa-llm"

if [ "$FOUND_RESOURCE" = "true" ]; then
    # 显示资源状态
    show_resource_status "nginx-proxy" "$RESOURCE_TYPE" "oa-llm"
    
    # 获取pod名称
    NGINX_POD=$(kubectl get pods -n oa-llm -l app=nginx-proxy | grep -v NAME | head -1 | awk '{print $1}')
    
    if [ -n "$NGINX_POD" ]; then
        # 询问用户是否进入容器
        read -p "是否进入Nginx容器？(y/n): " input
        if [ "$input" == "y" ]; then
            enter_pod_container "$NGINX_POD" "oa-llm"
        fi
        
        # 询问用户是否查看日志
        read -p "是否查看Nginx日志？(y/n): " input
        if [ "$input" == "y" ]; then
            view_pod_logs "$NGINX_POD" "oa-llm"
        fi
        
        # 询问用户是否诊断问题
        read -p "是否需要诊断Nginx服务问题？(y/n): " input
        if [ "$input" == "y" ]; then
            diagnose_resource_issues "nginx-proxy" "$RESOURCE_TYPE" "oa-llm"
        fi
    else
        echo "错误: 未找到Nginx Pod"
    fi
else
    echo "错误: 未找到Nginx服务"
fi

