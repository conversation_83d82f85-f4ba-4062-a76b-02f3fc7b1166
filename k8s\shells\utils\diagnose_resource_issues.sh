#!/bin/bash

# 深入诊断服务问题
# 参数: $1 - 服务名称, $2 - 资源类型, $3 - 命名空间
function diagnose_resource_issues() {
    local service_name=$1
    local resource_type=$2
    local namespace=$3
    
    echo "====== 开始深入诊断 $service_name 服务问题 ======"
    
    # 检查资源的详细信息
    echo "1. 查看 $resource_type 详细信息:"
    kubectl describe $resource_type $service_name -n $namespace
    
    # 检查相关事件
    echo "2. 检查与 $service_name 相关的事件:"
    kubectl get events -n $namespace | grep $service_name
    
    # 检查节点状态
    echo "3. 检查集群节点状态:"
    kubectl get nodes -o wide
    
    # 根据资源类型进行特定检查
    case $resource_type in
        "deployment"|"statefulset"|"daemonset")
            echo "4. 检查相关 Pod 状态:"
            # 尝试使用标签查找相关的 Pod
            kubectl get pods -n $namespace -l app=$service_name
            
            # 如果没有找到 Pod，尝试使用名称前缀查找
            pod_count=$(kubectl get pods -n $namespace | grep -E "^$service_name-" | wc -l)
            if [ $pod_count -gt 0 ]; then
                echo "找到 $pod_count 个相关 Pod:"
                kubectl get pods -n $namespace | grep -E "^$service_name-"
                
                # 对每个 Pod 进行详细检查
                for pod in $(kubectl get pods -n $namespace | grep -E "^$service_name-" | awk '{print $1}'); do
                    echo "5. 详细检查 Pod $pod:"
                    kubectl describe pod $pod -n $namespace
                    
                    # 检查 Pod 日志
                    echo "6. 查看 Pod $pod 日志 (最新50行):"
                    kubectl logs $pod -n $namespace --tail=50
                done
            else
                echo "找不到任何相关的 Pod，可能是以下原因:"
                echo "  - 资源配额不足"
                echo "  - 镜像拉取失败"
                echo "  - 调度问题"
                echo "  - 持久卷问题"
                
                # 检查集群资源情况
                echo "7. 检查集群资源情况:"
                kubectl describe nodes | grep -A 5 "Allocated resources"
            fi
            
            # 检查持久卷声明
            echo "8. 检查持久卷声明 (PVC):"
            kubectl get pvc -n $namespace | grep $service_name
            
            # 检查存储类
            echo "9. 检查存储类:"
            kubectl get storageclass
            ;;
        "service")
            echo "4. 检查服务端点:"
            kubectl get endpoints $service_name -n $namespace
            ;;
        "configmap"|"secret")
            echo "4. 检查引用此资源的 Pod:"
            kubectl get pods -n $namespace -o yaml | grep -B 5 -A 5 $service_name
            ;;
        "pvc")
            echo "4. 检查 PVC 状态和关联的 PV:"
            kubectl get pv | grep $(kubectl get pvc $service_name -n $namespace -o jsonpath='{.spec.volumeName}')
            ;;
    esac
    
    echo "10. 检查网络策略和服务连接性:"
    kubectl get networkpolicy -n $namespace
    
    echo "11. 推荐的解决方案:"
    case $resource_type in
        "deployment"|"statefulset"|"daemonset")
            echo "  - 检查镜像是否正确且可用"
            echo "  - 确保资源限制和请求合理"
            echo "  - 验证持久卷配置"
            echo "  - 检查容器的健康检查设置"
            echo "  - 查看在应用的配置文件中是否有错误"
            ;;
        "service")
            echo "  - 确认选择器标签与 Pod 标签匹配"
            echo "  - 检查端口配置是否正确"
            echo "  - 验证服务类型是否适合您的需求"
            ;;
        "pvc")
            echo "  - 确保有可用的存储类"
            echo "  - 检查存储容量是否足够"
            echo "  - 验证访问模式是否支持"
            ;;
    esac
    
    echo "====== 诊断完成 ======"
}

# 导出函数
export -f diagnose_resource_issues 