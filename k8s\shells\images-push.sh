#bin/bash
# 获取当前目录
CURRENT_DIR=$(pwd)
# 获取父目录
PARENT_DIR=$(dirname ${CURRENT_DIR})
# 读取config.json文件
CONFIG_FILE="${CURRENT_DIR}/config.json"
if [ ! -f ${CONFIG_FILE} ]; then
    echo "config.json文件不存在"
    exit 1
fi
# 读取config.json文件中的base-config字段    
TARGET_IMAGE_REPO_URL=$(jq -r '.["base-config"]["target-image-repo-url"]' ${CONFIG_FILE})
TARGET_IMAGE_REPO_USER=$(jq -r '.["base-config"]["target-image-repo-user"]' ${CONFIG_FILE})
TARGET_IMAGE_REPO_PASSWORD=$(jq -r '.["base-config"]["target-image-repo-password"]' ${CONFIG_FILE})
# 获取namespace
TARGET_IMAGE_NAMESPACE=$(jq -r '.["base-config"]["target-image-namespace"]' ${CONFIG_FILE})
# 登录目标镜像仓库
echo "登录目标镜像仓库"
docker login ${TARGET_IMAGE_REPO_URL} -u ${TARGET_IMAGE_REPO_USER} -p ${TARGET_IMAGE_REPO_PASSWORD}

# 遍历replace-config中的每一个节点
REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})
for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
    # 获取source-images-url
    SOURCE_IMAGES_URL=$(jq -r ".[\"replace-config\"][$i][\"source-images-url\"]" ${CONFIG_FILE})
    # 获取target-images-url
    TARGET_IMAGES_URL=$(jq -r ".[\"replace-config\"][$i][\"target-images-url\"]" ${CONFIG_FILE})
    # 获取description
    DESCRIPTION=$(jq -r ".[\"replace-config\"][$i][\"description\"]" ${CONFIG_FILE})
    # 获取type
    TYPE=$(jq -r ".[\"replace-config\"][$i][\"type\"]" ${CONFIG_FILE})
    # 推送镜像
    echo "推送[$TYPE:$DESCRIPTION]镜像: ${SOURCE_IMAGES_URL} -> ${TARGET_IMAGES_URL}"
    
    # 使用docker inspect命令检查镜像是否存在
    if ! docker inspect "${TARGET_IMAGES_URL}" &>/dev/null; then
        echo "镜像：${TARGET_IMAGES_URL} 不存在，尝试直接推送..."
        # 尝试推送镜像
        if docker push ${TARGET_IMAGES_URL}; then
            echo "镜像推送成功"
        else
            echo "镜像推送失败，请先执行 docker pull ${SOURCE_IMAGES_URL} 和 docker tag ${SOURCE_IMAGES_URL} ${TARGET_IMAGES_URL}"
        fi
        continue
    fi
    
    # 推送镜像
    echo "docker push ${TARGET_IMAGES_URL}"
    docker push ${TARGET_IMAGES_URL}
done

# 推送filebeat镜像
FILEBEAT_TARGET_IMAGES_URL=$(jq -r '.["filebeat-config"]["target-images-url"]' ${CONFIG_FILE})
FILEBEAT_SOURCE_IMAGES_URL=$(jq -r '.["filebeat-config"]["source-images-url"]' ${CONFIG_FILE})
echo "推送filebeat镜像: ${FILEBEAT_SOURCE_IMAGES_URL} -> ${FILEBEAT_TARGET_IMAGES_URL}"

# 判断filebeat镜像是否存在
if ! docker inspect "${FILEBEAT_TARGET_IMAGES_URL}" &>/dev/null; then
    echo "镜像：${FILEBEAT_TARGET_IMAGES_URL} 不存在，尝试直接推送..."
    # 尝试推送镜像
    if docker push ${FILEBEAT_TARGET_IMAGES_URL}; then
        echo "filebeat镜像推送成功"
    else
        echo "filebeat镜像推送失败，请先执行 docker pull ${FILEBEAT_SOURCE_IMAGES_URL} 和 docker tag ${FILEBEAT_SOURCE_IMAGES_URL} ${FILEBEAT_TARGET_IMAGES_URL}"
    fi
else
    echo "docker push ${FILEBEAT_TARGET_IMAGES_URL}"
    docker push ${FILEBEAT_TARGET_IMAGES_URL}
fi
