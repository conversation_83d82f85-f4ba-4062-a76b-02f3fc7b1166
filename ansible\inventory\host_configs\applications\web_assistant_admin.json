{"app_name": "web_assistant_admin", "module_name": "frontend", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "nginx_image": "m.daocloud.io/docker.io/nginx:1.27.4", "container_port": "80", "host_port": "8092", "app_data_dir": "/usr/share/nginx/html/web_assistant_admin", "app_logs_dir": "/var/log/nginx", "app_config_dir": "/etc/nginx/nginx.conf", "host_data_dir": "{{base_dir}}/nginx/html/web_assistant_admin", "host_logs_dir": "{{base_dir}}/nginx/log", "host_config_dir": "{{base_dir}}/nginx/conf", "restart_script": "{{base_dir}}/nginx/restart.sh", "test_script": "{{base_dir}}/nginx/test_curl.sh", "external_dependencies": [{"type": "service", "name": "rbac", "url": "http://rbac:8080"}, {"type": "service", "name": "aiknowledge-controller", "url": "http://aiknowledge-controller:8080"}], "test_commands": ["curl -s http://localhost:8092/web_assistant_admin/ | grep -i \"<!DOCTYPE html>\""]}