# 下载hyzhw镜像

TAR_DIR=/mnt/e/hyzhw

mkdir -p $TAR_DIR

cd $TAR_DIR

docker login officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn --username=zglyjt --password=Znwd0415@

docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python:v528
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4

docker save -o elasticsearch.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
docker save -o kibana.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14
docker save -o poc-intelligence-view-write.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0
docker save -o poc-intelligence-view.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
docker save -o ai-doc-poc.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT
docker save -o ai-redactor.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18
docker save -o article-auto-compose-server.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT
docker save -o ai-writer-nostream-python.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python:v528
docker save -o filebeat.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
docker save -o nginx.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4

docker tag docker.elastic.co/beats/filebeat:7.17.14 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
