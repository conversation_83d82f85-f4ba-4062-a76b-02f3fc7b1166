[{"app_name": "ai-writer-nostream-python", "description": "智能非流式写作服务Python端", "app_type": "python", "module_name": "ai-writer", "ip_address": "*********", "app_base_dir": "/data/ai-writer/ai-writer-nostream-python", "config_dir": "config", "log_dir": "logs", "docker_image": "ai-writer/ai-writer-nostream-python:v1", "container_name": "ai-writer-nostream-python", "config_dependencies": [{"config_file": "config/application.properties", "key": "model.url", "service_ref": "models/semantic-model", "value_type": "url", "current_value": "http://************:30000/stream-jwt/CIDC-RP-207/inference-proxy/2185090615607296/aiops-1340745253490184192/qwen-32b-instruct/service/8080/v1/chat/completions", "template_value": "{{model.url}}"}, {"config_file": "config/application.properties", "key": "api-key", "value_type": "string", "current_value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3Mzk4MDM2ODIsImtleSI6InpsLTIwMjUwMjE2LTEifQ.vrHTFufptkAyCqzmJIYg9kzvTjpVpuPuiC_EZPwprG8", "template_value": "{{model.api-key}}"}, {"config_file": "config/application.properties", "key": "model-name", "value_type": "string", "current_value": "qwen-32b-instruct", "template_value": "{{model.name}}"}]}, {"app_name": "ai-writer-stream-python", "description": "智能流式写作服务Python端", "app_type": "python", "module_name": "ai-writer", "ip_address": "*********", "app_base_dir": "/data/ai-writer/ai-writer-stream-python", "config_dir": "config", "log_dir": "logs", "docker_image": "ai-writer/ai-writer-stream-python:v1", "container_name": "ai-writer-stream-python", "config_dependencies": [{"config_file": "config.py", "key": "MODEL_URL", "service_ref": "models/semantic-model", "value_type": "url", "current_value": "http://************:30000/stream-jwt/CIDC-RP-207/inference-proxy/2185090615607296/aiops-1340745253490184192/qwen-32b-instruct/service/8080/v1/chat/completions", "template_value": "{{model.url}}"}, {"config_file": "config.py", "key": "API_KEY", "value_type": "string", "current_value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3Mzk4MDM2ODIsImtleSI6InpsLTIwMjUwMjE2LTEifQ.vrHTFufptkAyCqzmJIYg9kzvTjpVpuPuiC_EZPwprG8", "template_value": "{{model.api-key}}"}, {"config_file": "config.py", "key": "MODEL_NAME", "value_type": "string", "current_value": "qwen-32b-instruct", "template_value": "{{model.name}}"}]}, {"app_name": "article-auto-compose-server", "description": "智能写作服务Java端", "app_type": "java", "module_name": "ai-writer", "ip_address": "*********", "app_base_dir": "/data/ai-writer/article-auto-compose-server", "config_dir": "config", "log_dir": "logs", "docker_image": "ai-writer/article-auto-compose-server:0.0.1-SNAPSHOT", "container_name": "article-auto-compose-server", "config_dependencies": [{"config_file": "config/application-prod.yml", "key": "ai-service.providers[0].base-url", "service_ref": "ai-writer/ai-writer-nostream-python", "value_type": "url", "current_value": "http://ai-writer-nostream-python:8080/mubanwriter/v1/service", "template_value": "http://{{ai-writer-nostream-python.ip}}:{{ai-writer-nostream-python.port}}/mubanwriter/v1/service"}]}, {"app_name": "bce-embedding-v1", "description": "BCE向量服务", "app_type": "embedding_model", "module_name": "embedding", "ip_address": "*********", "app_base_dir": "/data/embedding/bce-embedding-v1", "config_dir": "config", "log_dir": "logs", "source_package_path": "embedding/embedding-service-bce:x86-v1", "docker_image": "embedding/embedding-service-bce:x86-v1", "container_name": "bce-embedding-v1"}, {"app_name": "bge-embedding-m3", "description": "BGE向量服务", "app_type": "embedding_model", "module_name": "embedding", "ip_address": "**********", "app_base_dir": "/data/embedding/bge-embedding-m3", "config_dir": "config", "log_dir": "logs", "source_package_path": "embedding/embedding-service-bge-m3:x86.python3.10.v1", "docker_image": "embedding/embedding-service-bge-m3:x86.python3.10.v1", "container_name": "bge-embedding-m3"}, {"app_name": "bge-large-zh", "description": "BGE向量服务", "app_type": "embedding_model", "module_name": "embedding", "ip_address": "**********", "app_base_dir": "/data/embedding/bge-large-zh", "config_dir": "config", "log_dir": "logs", "source_package_path": "embedding/embedding-service-bge-large-zh:x86.python3.10.v1", "docker_image": "embedding/embedding-service-bge-large-zh:x86.python3.10.v1", "container_name": "bge-large-zh"}, {"app_name": "bge-reranker-m3-v2", "description": "BGE重排序服务", "app_type": "embedding_model", "module_name": "embedding", "ip_address": "**********", "app_base_dir": "/data/embedding/bge-reranker-m3-v2", "config_dir": "config", "log_dir": "logs", "source_package_path": "embedding/bge-reranker-v2-m3:x86.python3.10.v1", "docker_image": "embedding/bge-reranker-v2-m3:x86.python3.10.v1", "container_name": "bge-reranker-m3-v2"}, {"app_name": "nginx", "description": "Nginx前端服务", "app_type": "frontend", "module_name": "frontend", "ip_address": "**********", "app_base_dir": "/data/nginx", "config_dir": "config", "log_dir": "logs", "docker_image": "m.daocloud.io/docker.io/nginx:1.27.4", "container_name": "nginx", "static_resources": [{"name": "poc-intelligence-view", "path": "/data/nginx/html/poc-intelligence-view"}, {"name": "web_assistant", "path": "/data/nginx/html/web_assistant"}, {"name": "web_assistant_admin", "path": "/data/nginx/html/web_assistant_admin"}], "config_dependencies": [{"config_file": "config/nginx.conf", "key": "upstream rbac", "service_ref": "znwd/rbac", "value_type": "ip_port", "current_value": "**********:8081", "template_value": "{{rbac.ip}}:8081"}, {"config_file": "config/nginx.conf", "key": "upstream qagws", "service_ref": "znwd/qa", "value_type": "ip_port", "current_value": "**********:8083", "template_value": "{{qa.ip}}:8083"}, {"config_file": "config/nginx.conf", "key": "upstream ai-doc-poc", "service_ref": "hegao/ai-doc-poc", "value_type": "ip_port", "current_value": "*********:8093", "template_value": "{{hegao.ip}}:8093"}, {"config_file": "config/nginx.conf", "key": "upstream ai-compose-poc", "service_ref": "ai-writer/article-auto-compose-server", "value_type": "ip_port", "current_value": "*********:8094", "template_value": "{{article-auto-compose-server.ip}}:{{article-auto-compose-server.port}}"}]}, {"app_name": "ai-doc-poc", "description": "智能核稿服务Java端", "app_type": "java", "module_name": "hegao", "ip_address": "*********", "app_base_dir": "/data/hegao/ai-doc-poc", "config_dir": "config", "log_dir": "logs", "source_package_path": "/data/ai-doc-poc/app.jar", "docker_image": "hegao/ai-doc:2025.4.1", "container_name": "ai-doc-poc", "config_dependencies": [{"config_file": "config/application-prod.yml", "key": "ai-service.providers[0].base-url", "value_type": "url", "current_value": "http://ai-hegao-python:8080/spellcheck/oa", "template_value": "http://{{ai-hegao-python.ip}}:{{ai-hegao-python.port}}/spellcheck/oa"}]}, {"app_name": "ai-hegao-python", "description": "智能核稿服务Python端", "app_type": "python", "module_name": "hegao", "ip_address": "*********", "app_base_dir": "/data/hegao/ai-hegao-python", "config_dir": "config", "log_dir": "logs", "source_package_path": "hegao/ai-hegao-python:v1", "docker_image": "hegao/ai-hegao-python:v1", "container_name": "ai-hegao-python", "config_dependencies": [{"config_file": "config/application.properties", "key": "model.url", "service_ref": "models/semantic-model", "value_type": "url", "current_value": "http://************:30000/stream-jwt/CIDC-RP-207/inference-proxy/2185090615607296/aiops-1340745253490184192/qwen-32b-instruct/service/8080/v1/chat/completions", "template_value": "{{model.url}}"}, {"config_file": "config/application.properties", "key": "api-key", "value_type": "string", "current_value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3Mzk4MDM2ODIsImtleSI6InpsLTIwMjUwMjE2LTEifQ.vrHTFufptkAyCqzmJIYg9kzvTjpVpuPuiC_EZPwprG8", "template_value": "{{model.api-key}}"}, {"config_file": "config/application.properties", "key": "model-name", "value_type": "string", "current_value": "qwen-32b-instruct", "template_value": "{{model.name}}"}]}, {"app_name": "elasticsearch", "description": "Elasticsearch服务", "app_type": "middleware", "module_name": "middleware", "ip_address": "**********", "app_base_dir": "/data/middleware/elasticsearch", "config_dir": "config", "log_dir": "logs", "source_package_path": "docker.elastic.co/elasticsearch/elasticsearch:7.17.14", "docker_image": "docker.elastic.co/elasticsearch/elasticsearch:7.17.14", "container_name": "elasticsearch", "config_dependencies": []}, {"app_name": "kibana", "description": "Kibana服务", "app_type": "middleware", "module_name": "middleware", "ip_address": "**********", "app_base_dir": "/data/middleware/kibana", "config_dir": "config", "log_dir": "logs", "source_package_path": "docker.elastic.co/kibana/kibana:7.17.14", "docker_image": "docker.elastic.co/kibana/kibana:7.17.14", "container_name": "kibana", "config_dependencies": []}, {"app_name": "minio", "description": "Minio对象存储服务", "app_type": "middleware", "module_name": "middleware", "ip_address": "*********", "app_base_dir": "/data/middleware/minio", "config_dir": "config", "log_dir": "logs", "source_package_path": "m.daocloud.io/docker.io/minio/minio:RELEASE.2023-04-28T18-11-17Z", "docker_image": "m.daocloud.io/docker.io/minio/minio:RELEASE.2023-04-28T18-11-17Z", "container_name": "minio-server", "config_dependencies": []}, {"app_name": "opensearch", "description": "OpenSearch服务", "app_type": "middleware", "module_name": "middleware", "ip_address": "*********", "app_base_dir": "/data/middleware/opensearch", "config_dir": "config", "log_dir": "logs", "source_package_path": "public.ecr.aws/opensearchproject/opensearch:2.9.0", "docker_image": "public.ecr.aws/opensearchproject/opensearch:2.9.0", "container_name": "opensearch", "config_dependencies": []}, {"app_name": "opensearch-dashboards", "description": "OpenSearch Dashboards服务", "app_type": "middleware", "module_name": "middleware", "ip_address": "*********", "app_base_dir": "/data/middleware/opensearch-dashboards", "config_dir": "config", "log_dir": "logs", "source_package_path": "public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0", "docker_image": "public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0", "container_name": "opensearch-dashboards", "config_dependencies": []}, {"app_name": "postgresql", "description": "PostgreSQL数据库服务", "app_type": "middleware", "module_name": "middleware", "ip_address": "*********", "app_base_dir": "/data/middleware/postgresql", "config_dir": "config", "log_dir": "logs", "source_package_path": "m.daocloud.io/docker.io/postgres:12.18", "docker_image": "m.daocloud.io/docker.io/postgres:12.18", "container_name": "postgres-server", "config_dependencies": []}, {"app_name": "rabbitmq", "description": "RabbitMQ消息队列服务", "app_type": "middleware", "module_name": "middleware", "ip_address": "********", "app_base_dir": "/data/middleware/rabbitmq", "config_dir": "config", "log_dir": "logs", "source_package_path": "m.daocloud.io/docker.io/rabbitmq:3.12-management", "docker_image": "m.daocloud.io/docker.io/rabbitmq:3.12-management", "container_name": "rabbitmq", "config_dependencies": []}, {"app_name": "redis", "description": "Redis缓存服务", "app_type": "middleware", "module_name": "middleware", "ip_address": "********", "app_base_dir": "/data/middleware/redis", "config_dir": "config", "log_dir": "logs", "source_package_path": "m.daocloud.io/docker.io/redis:7.4.1", "docker_image": "m.daocloud.io/docker.io/redis:7.4.1", "container_name": "redis", "config_dependencies": []}, {"app_name": "zookeeper", "description": "Zookeeper服务", "app_type": "middleware", "module_name": "middleware", "ip_address": "********", "app_base_dir": "/data/middleware/zookeeper", "config_dir": "config", "log_dir": "logs", "source_package_path": "m.daocloud.io/docker.io/zookeeper:3.9.3", "docker_image": "m.daocloud.io/docker.io/zookeeper:3.9.3", "container_name": "zookeeper", "config_dependencies": []}, {"app_name": "aiknowledge-controller", "description": "智能知识服务Java端", "app_type": "java", "module_name": "znwd", "ip_address": "**********", "app_base_dir": "/data/znwd/aiknowledge-controller", "config_dir": "config", "log_dir": "logs", "source_package_path": "/data/znwd/aiknowledge-controller/app.jar", "docker_image": "znwd/aiknowledge-controller:1.0-SNAPSHOT", "container_name": "aiknowledge-controller", "config_dependencies": [{"config_file": "config/application-prod.yml", "key": "spring.datasource.url", "service_ref": "middleware/postgresql", "value_type": "jdbc_url", "current_value": "*************************************", "template_value": "jdbc:postgresql://{{postgresql.ip}}:{{postgresql.port}}/aidb"}, {"config_file": "config/application-prod.yml", "key": "spring.datasource.username", "value_type": "string", "current_value": "admin", "template_value": "{{postgresql.username}}"}, {"config_file": "config/application-prod.yml", "key": "spring.datasource.password", "value_type": "string", "current_value": "PgSQL@2025", "template_value": "{{postgresql.password}}"}, {"config_file": "config/application-prod.yml", "key": "spring.redis.host", "service_ref": "middleware/redis", "value_type": "ip", "current_value": "********", "template_value": "{{redis.ip}}"}, {"config_file": "config/application-prod.yml", "key": "spring.redis.password", "value_type": "string", "current_value": "admin123", "template_value": "{{redis.password}}"}, {"config_file": "config/application-prod.yml", "key": "capabilityapi.models.deepseek32b.docChatUrl", "value_type": "url", "current_value": "http://rag-qa-online:8080/Rag/QAOnlineSSE", "template_value": "http://{{rag-qa-online.ip}}:{{rag-qa-online.port}}/Rag/QAOnlineSSE"}, {"config_file": "config/application-prod.yml", "key": "capabilityapi.models.docParserChunkUrl", "value_type": "string", "current_value": "http://rag-doc-parser:8000/doc_generate_qa/v1/service", "template_value": "http://{{rag-doc-parser.ip}}:{{rag-doc-parser.port}}/doc_generate_qa/v1/service"}, {"config_file": "config/application-prod.yml", "key": "capabilityapi.models.knowledgeBaseApiUrl", "value_type": "string", "current_value": "http://rag-kg-os:8080/kg/aud", "template_value": "http://{{rag-kg-os.ip}}:{{rag-kg-os.port}}/kg/aud"}, {"config_file": "config/application-prod.yml", "key": "capabilityapi.models.qaChangeApiUrl", "value_type": "string", "current_value": "http://rag-kg-os:8080/qa/aud", "template_value": "http://{{rag-kg-os.ip}}:{{rag-kg-os.port}}/qa/aud"}, {"config_file": "config/application-prod.yml", "key": "capabilityapi.models.qaBatchAddApiUrl", "value_type": "string", "current_value": "http://rag-kg-os:8080/qa/add/batch", "template_value": "http://{{rag-kg-os.ip}}:{{rag-kg-os.port}}/qa/add/batch"}, {"config_file": "config/application-prod.yml", "key": "opensearch.hostname", "value_type": "string", "current_value": "*********", "template_value": "{{opensearch.ip}}"}, {"config_file": "config/application-prod.yml", "key": "opensearch.port", "value_type": "int", "current_value": "9200", "template_value": "{{opensearch.port}}"}, {"config_file": "config/application-prod.yml", "key": "opensearch.username", "value_type": "string", "current_value": "admin", "template_value": "{{opensearch.username}}"}, {"config_file": "config/application-prod.yml", "key": "opensearch.password", "value_type": "string", "current_value": "admin", "template_value": "{{opensearch.password}}"}, {"config_file": "config/application-prod.yml", "key": "opensearch.segment-url", "value_type": "url", "current_value": "http://query-parser:7890/Rag/QueryParser", "template_value": "http://{{query-parser.ip}}:{{query-parser.port}}/Rag/QueryParser"}, {"config_file": "config/application-prod.yml", "key": "opensearch.embedding-url", "value_type": "url", "current_value": "http://**********:8101/Rag/BgeEmbeddingService", "template_value": "http://{{bge-embedding-m3.ip}}:{{bge-embedding-m3.port}}/Rag/BgeEmbeddingService"}, {"config_file": "config/application-prod.yml", "key": "api.auth.systems.afsoprmv.callbackUrl", "value_type": "url", "current_value": "http://rbac:8080/knowledge/doc/callback", "template_value": "http://{{rbac.ip}}:{{rbac.port}}/knowledge/doc/callback"}]}, {"app_name": "qa", "description": "智能问答服务Java端", "app_type": "java", "module_name": "znwd", "ip_address": "**********", "app_base_dir": "/data/znwd/qa", "config_dir": "config", "log_dir": "logs", "source_package_path": "/data/znwd/qa/app.jar", "docker_image": "znwd/qa:0.0.1-SNAPSHOT", "container_name": "qa", "config_dependencies": [{"config_file": "config/application-prod.yml", "key": "spring.datasource.url", "service_ref": "middleware/postgresql", "value_type": "jdbc_url", "current_value": "*************************************", "template_value": "jdbc:postgresql://{{postgresql.ip}}:{{postgresql.port}}/aidb"}, {"config_file": "config/application-prod.yml", "key": "spring.datasource.username", "value_type": "string", "current_value": "admin", "template_value": "{{postgresql.username}}"}, {"config_file": "config/application-prod.yml", "key": "spring.datasource.password", "value_type": "string", "current_value": "PgSQL@2025", "template_value": "{{postgresql.password}}"}, {"config_file": "config/application-prod.yml", "key": "spring.redis.host", "service_ref": "middleware/redis", "value_type": "ip", "current_value": "********", "template_value": "{{redis.ip}}"}, {"config_file": "config/application-prod.yml", "key": "spring.redis.password", "value_type": "string", "current_value": "admin123", "template_value": "{{redis.password}}"}]}, {"app_name": "query-parser", "description": "智能问答segment分词Python端", "app_type": "python", "module_name": "znwd", "ip_address": "**********", "app_base_dir": "/data/znwd/query-parser", "config_dir": "config", "log_dir": "logs", "source_package_path": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/query-parser:v1", "docker_image": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/query-parser:v1", "container_name": "query-parser", "config_dependencies": []}, {"app_name": "rag-doc-parser", "description": "智能问答文档解析Python端", "app_type": "python", "module_name": "znwd", "ip_address": "**********", "app_base_dir": "/data/znwd/rag-doc-parser", "config_dir": "config", "log_dir": "logs", "source_package_path": "znwd/rag-doc-parser:v1", "docker_image": "znwd/rag-doc-parser:v1", "container_name": "rag-doc-parser"}, {"app_name": "rag-kg-os", "description": "智能问答知识库服务Python端", "app_type": "python", "module_name": "znwd", "ip_address": "**********", "app_base_dir": "/data/znwd/rag-kg-os", "config_dir": "config", "log_dir": "logs", "source_package_path": "znwd/rag-kg-os:v1", "docker_image": "znwd/rag-kg-os:v1", "container_name": "rag-kg-os", "config_dependencies": [{"config_file": "configs.py", "key": "OS_URL", "service_ref": "middleware/opensearch", "value_type": "url", "current_value": "https://*********:9200", "template_value": "https://{{opensearch.ip}}:{{opensearch.port}}"}, {"config_file": "configs.py", "key": "BGE_URL", "service_ref": "embedding/bge-large-zh", "value_type": "url", "current_value": "http://**********:8104/Rag/BgeEmbeddingService", "template_value": "http://{{bge-large-zh.ip}}:{{bge-large-zh.port}}/Rag/BgeEmbeddingService"}, {"config_file": "configs.py", "key": "BGE_M3_URL", "service_ref": "embedding/bge-embedding-m3", "value_type": "url", "current_value": "http://**********:8101/Rag/BgeM3Embedding", "template_value": "http://{{bge-embedding-m3.ip}}:{{bge-embedding-m3.port}}/Rag/BgeM3Embedding"}, {"config_file": "configs.py", "key": "SEG_URL", "service_ref": "znwd/query-parser", "value_type": "url", "current_value": "http://query-parser:7890/Rag/QueryParser", "template_value": "http://{{query-parser.ip}}:{{query-parser.port}}/Rag/QueryParser"}, {"config_file": "configs.py", "key": "BCE_URL", "service_ref": "embedding/bce-embedding-v1", "value_type": "url", "current_value": "http://*********:8100", "template_value": "http://{{bce-embedding-v1.ip}}:{{bce-embedding-v1.port}}"}]}, {"app_name": "rag-qa-online", "description": "智能问答在线服务Python端", "app_type": "python", "module_name": "znwd", "ip_address": "**********", "app_base_dir": "/data/znwd/rag-qa-online", "config_dir": "config", "log_dir": "logs", "source_package_path": "znwd/rag-qa-online:v2", "docker_image": "znwd/rag-qa-online:v2", "container_name": "rag-qa-online", "config_dependencies": [{"config_file": "services.json", "key": "algorithm.Recall[0].url", "service_ref": "middleware/opensearch", "value_type": "url", "current_value": "https://*********:9200", "template_value": "https://{{opensearch.ip}}:{{opensearch.port}}"}, {"config_file": "services.json", "key": "algorithm.Recall[1].url", "service_ref": "embedding/bce-embedding-v1", "value_type": "url", "current_value": "http://*********:8100", "template_value": "http://{{bce-embedding-v1.ip}}:{{bce-embedding-v1.port}}"}, {"config_file": "services.json", "key": "algorithm.Recall[2].url", "service_ref": "embedding/bge-large-zh", "value_type": "url", "current_value": "http://**********:8104", "template_value": "http://{{bge-large-zh.ip}}:{{bge-large-zh.port}}"}, {"config_file": "services.json", "key": "algorithm.Rerank[0].url", "service_ref": "embedding/bge-reranker-m3-v2", "value_type": "url", "current_value": "http://**********:8103", "template_value": "http://{{bge-reranker-m3-v2.ip}}:{{bge-reranker-m3-v2.port}}"}, {"config_file": "services.json", "key": "algorithm.QueryParser[0].url", "service_ref": "znwd/query-parser", "value_type": "url", "current_value": "http://query-parser:7890", "template_value": "http://{{query-parser.ip}}:{{query-parser.port}}"}, {"config_file": "models.yaml", "key": "default.url", "value_type": "url", "current_value": "http://************:30000", "template_value": "http://{{model.ip}}:{{model.port}}"}, {"config_file": "models.yaml", "key": "llm_stream.path", "value_type": "string", "current_value": "/stream-jwt/CIDC-RP-207/inference-proxy/2185090615607296/aiops-1340745253490184192/qwen-32b-instruct/service/8080/v1/chat/completions", "template_value": "{{model.path}}"}, {"config_file": "models.yaml", "key": "llm_stream.model", "value_type": "string", "current_value": "qwen-32b-instruct", "template_value": "{{model.name}}"}]}, {"app_name": "rbac", "description": "智能问答权限服务Java端", "app_type": "java", "module_name": "znwd", "ip_address": "**********", "app_base_dir": "/data/znwd/rbac", "config_dir": "config", "log_dir": "logs", "docker_image": "znwd/rbac:1.0-SNAPSHOT", "container_name": "rbac", "config_dependencies": [{"config_file": "config/application-prod.yml", "key": "spring.datasource.url", "service_ref": "middleware/postgresql", "value_type": "jdbc_url", "current_value": "*************************************", "template_value": "jdbc:postgresql://{{postgresql.ip}}:{{postgresql.port}}/aidb"}, {"config_file": "config/application-prod.yml", "key": "spring.datasource.username", "service_ref": "middleware/postgresql", "value_type": "string", "current_value": "admin", "template_value": "{{postgresql.username}}"}, {"config_file": "config/application-prod.yml", "key": "spring.datasource.password", "service_ref": "middleware/postgresql", "value_type": "string", "current_value": "PgSQL@2025", "template_value": "{{postgresql.password}}"}, {"config_file": "config/application-prod.yml", "key": "spring.redis.host", "service_ref": "middleware/redis", "value_type": "ip", "current_value": "********", "template_value": "{{redis.ip}}"}, {"config_file": "config/application-prod.yml", "key": "spring.redis.port", "service_ref": "middleware/redis", "value_type": "int", "current_value": "6379", "template_value": "{{redis.port}}"}, {"config_file": "config/application-prod.yml", "key": "spring.redis.password", "value_type": "string", "current_value": "admin123", "template_value": "{{redis.password}}"}, {"config_file": "config/application-prod.yml", "key": "minio.endpoint", "service_ref": "middleware/minio", "value_type": "url", "current_value": "http://*********:9000", "template_value": "http://{{minio.ip}}:{{minio.port}}"}, {"config_file": "config/application-prod.yml", "key": "minio.access-key", "value_type": "string", "current_value": "admin", "template_value": "{{minio.access-key}}"}, {"config_file": "config/application-prod.yml", "key": "minio.secret-key", "value_type": "string", "current_value": "MiniO@2025", "template_value": "{{minio.secret-key}}"}, {"config_file": "config/application-prod.yml", "key": "knowledge.document.upload-url", "value_type": "string", "current_value": "http://ai-knowledge-controller:8080/openapi/v1/document/upload/fileIds", "template_value": "http://{{ai-knowledge-controller.ip}}:{{ai-knowledge-controller.port}}/openapi/v1/document/upload/fileIds"}, {"config_file": "config/application-prod.yml", "key": "knowledge.document.delete-url", "value_type": "string", "current_value": "http://ai-knowledge-controller:8080/openapi/v1/document/delete", "template_value": "http://{{ai-knowledge-controller.ip}}:{{ai-knowledge-controller.port}}/openapi/v1/document/delete"}, {"config_file": "config/application-prod.yml", "key": "knowledge.manage.save-url", "value_type": "string", "current_value": "http://ai-knowledge-controller:8080/openapi/v1/knowledge/create", "template_value": "http://{{ai-knowledge-controller.ip}}:{{ai-knowledge-controller.port}}/openapi/v1/knowledge/create"}, {"config_file": "config/application-prod.yml", "key": "knowledge.manage.update-url", "value_type": "string", "current_value": "http://ai-knowledge-controller:8080/openapi/v1/knowledge/update", "template_value": "http://{{ai-knowledge-controller.ip}}:{{ai-knowledge-controller.port}}/openapi/v1/knowledge/update"}, {"config_file": "config/application-prod.yml", "key": "knowledge.manage.delete-url", "value_type": "string", "current_value": "http://ai-knowledge-controller:8080/openapi/v1/knowledge/delete", "template_value": "http://{{ai-knowledge-controller.ip}}:{{ai-knowledge-controller.port}}/openapi/v1/knowledge/delete"}]}]