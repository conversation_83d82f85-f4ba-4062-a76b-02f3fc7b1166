apiVersion: apps/v1
kind: Deployment
metadata:
  name: elasticsearch
  namespace: oa-llm
  labels:
    k8s-app: elasticsearch
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s-app: elasticsearch
  template:
    metadata:
      labels:
        k8s-app: elasticsearch
    spec:
      containers:
      - image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/elasticsearch:7.17.14
        name: elasticsearch
        resources:
          limits:
            cpu: 2
            memory: 2Gi
          requests:
            cpu: 2
            memory: 2Gi
        env:
          - name: "discovery.type"
            value: "single-node"
          - name: ES_JAVA_OPTS
            value: "-Xms512m -Xmx2g"
        ports:
        - containerPort: 9200
          name: db
          protocol: TCP
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
---
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch
  namespace: oa-llm
spec:
  type: ClusterIP
  ports:
  - port: 9200
    protocol: TCP
    targetPort: 9200
  selector:
    k8s-app: elasticsearch 