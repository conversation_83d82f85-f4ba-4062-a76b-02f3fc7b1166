# ====================== 文档检查API测试 ======================
echo -e "${BLUE}=== 文档检查API测试 ===${NC}"
echo -e "${YELLOW}正在测试文档检查接口...${NC}"

# 测试网络是否连通
echo -e "${YELLOW}正在测试网络是否连通...${NC}"
# 测试接口是否能访问
echo -e "${YELLOW}正在测试接口是否能访问...${NC}"
echo "curl -v http://ai-doc-poc:8080"
curl -v http://ai-doc-poc:8080
#记录开始时间
start_time=$(date +%s)
# 测试直连
echo -e "${YELLOW}正在测试直连...${NC}"
curl -v -X POST \
    -F "file=@/test.txt;type=text/plain" \
    -F "accessMode=STANDALONE" \
    -F "checkUnitId=BALANCEABILITY" \
    -F "fileType=HTML" \
    -F "extension={\"title\":\"请在此输入标题\",\"mainSubmit\":[],\"copySubmit\":[]}" \
    http://ai-doc-poc:8080/ai-doc-poc/api/v1/check
#记录结束时间
end_time=$(date +%s)
#计算执行时间
execution_time=$((end_time - start_time))
echo "执行时间: $execution_time 秒"
echo

# 测试文档为test.txt，请确保该文件存在
echo -e "${YELLOW}正在测试文档检查代理接口...${NC}"
#记录开始时间
start_time=$(date +%s)
curl -v -X POST \
    -F "file=@/test.txt;type=text/plain" \
    -F "accessMode=STANDALONE" \
    -F "checkUnitId=BALANCEABILITY" \
    -F "fileType=HTML" \
    -F "extension={\"title\":\"请在此输入标题\",\"mainSubmit\":[],\"copySubmit\":[]}" \
    http://localhost/ai-doc-poc/api/v1/check
#记录结束时间
end_time=$(date +%s)
#计算执行时间
execution_time=$((end_time - start_time))
echo "执行时间: $execution_time 秒"
echo
echo -e "${GREEN}=== 文档检查API测试结束 ===${NC}"