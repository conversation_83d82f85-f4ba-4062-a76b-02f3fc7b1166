#!/bin/bash
# TinyProxy Docker部署脚本
# 在************服务器上使用Docker部署TinyProxy代理服务

# 设置颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# 代理服务器配置
PROXY_PORT=8888
CONTAINER_NAME=tinyproxy
DOCKER_IMAGE="vimagick/tinyproxy:latest"  # 使用现成的Docker镜像

echo -e "${BLUE}=== TinyProxy Docker部署脚本 ===${NC}"
echo "此脚本将在服务器上部署TinyProxy代理服务"
echo "代理端口: $PROXY_PORT"
echo "容器名称: $CONTAINER_NAME"
echo "使用镜像: $DOCKER_IMAGE"
echo "配置: 允许任何IP地址访问代理服务器"
echo

# 检查Docker是否安装
echo -e "${YELLOW}检查Docker是否安装...${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker未安装，请先安装Docker${NC}"
    echo "可以使用以下命令安装Docker:"
    echo "curl -fsSL https://get.docker.com | sh"
    exit 1
fi
echo -e "${GREEN}Docker已安装${NC}"

# 创建工作目录
echo -e "${YELLOW}创建工作目录...${NC}"
WORK_DIR="/data/tinyproxy-docker"
mkdir -p $WORK_DIR
cd $WORK_DIR

# 创建tinyproxy.conf配置文件
echo -e "${YELLOW}创建TinyProxy配置文件...${NC}"
cat > tinyproxy.conf << EOF
# tinyproxy配置文件
User nobody
Group nobody
Port $PROXY_PORT
Timeout 600
DefaultErrorFile "/usr/share/tinyproxy/default.html"
StatFile "/usr/share/tinyproxy/stats.html"
LogLevel Info
MaxClients 100
MinSpareServers 5
MaxSpareServers 20
StartServers 10
MaxRequestsPerChild 0
# 允许所有IP访问代理服务器
Allow 0.0.0.0/0
ViaProxyName "tinyproxy"
ConnectPort 443
ConnectPort 563
EOF
echo -e "${GREEN}配置文件已创建${NC}"

# 检查是否已存在同名容器并停止
echo -e "${YELLOW}检查现有容器...${NC}"
if docker ps -a --format '{{.Names}}' | grep -q "^$CONTAINER_NAME$"; then
    echo -e "${YELLOW}发现已存在的$CONTAINER_NAME容器，正在停止并删除...${NC}"
    docker stop $CONTAINER_NAME >/dev/null 2>&1
    docker rm $CONTAINER_NAME >/dev/null 2>&1
fi

# 拉取镜像
echo -e "${YELLOW}拉取TinyProxy镜像...${NC}"
docker pull $DOCKER_IMAGE

# 启动Docker容器
echo -e "${YELLOW}启动Docker容器...${NC}"
docker run -d --name $CONTAINER_NAME \
    --restart always \
    -p $PROXY_PORT:$PROXY_PORT \
    -v $WORK_DIR/tinyproxy.conf:/etc/tinyproxy/tinyproxy.conf \
    $DOCKER_IMAGE

# 检查容器是否成功启动
echo -e "${YELLOW}验证容器是否成功启动...${NC}"
sleep 2
if docker ps --format '{{.Names}}' | grep -q "^$CONTAINER_NAME$"; then
    echo -e "${GREEN}TinyProxy容器已成功启动!${NC}"
    echo "容器ID: $(docker ps -f name=$CONTAINER_NAME --format '{{.ID}}')"
    echo "容器状态: $(docker ps -f name=$CONTAINER_NAME --format '{{.Status}}')"
    
    # 获取服务器IP
    SERVER_IP=$(hostname -I | awk '{print $1}')
    echo -e "${GREEN}代理服务器地址: ${SERVER_IP}:${PROXY_PORT}${NC}"
    
    # 测试代理是否工作
    echo -e "${YELLOW}测试代理连接...${NC}"
    if curl -s --connect-timeout 5 -x http://${SERVER_IP}:${PROXY_PORT} http://www.baidu.com > /dev/null; then
        echo -e "${GREEN}代理服务器工作正常!${NC}"
    else
        echo -e "${RED}代理连接测试失败，请检查配置${NC}"
    fi
else
    echo -e "${RED}容器启动失败，请检查日志:${NC}"
    docker logs $CONTAINER_NAME
fi

echo
echo -e "${BLUE}=== 部署完成 ===${NC}"
echo "使用以下命令查看容器日志:"
echo "docker logs $CONTAINER_NAME"
echo
echo "使用以下设置连接代理服务器:"
echo "代理服务器: ${SERVER_IP:-************}"
echo "代理端口: $PROXY_PORT"
echo "代理URL: http://${SERVER_IP:-************}:$PROXY_PORT"
echo
echo "在容器内使用以下命令测试代理:"
echo "export http_proxy=http://${SERVER_IP:-************}:$PROXY_PORT"
echo "export https_proxy=http://${SERVER_IP:-************}:$PROXY_PORT"
echo "curl -I https://www.baidu.com"

# 清理工作目录但保留配置文件
echo -e "${YELLOW}保留配置文件在 ${WORK_DIR}${NC}"
echo -e "${GREEN}完成!${NC}" 

echo "export http_proxy=http://************:3128"
echo "export https_proxy=https://************:3128"

echo "export http_proxy=http://************:3128" >> ~/.bashrc
echo "export https_proxy=https://************:3128" >> ~/.bashrc

source ~/.bashrc
    