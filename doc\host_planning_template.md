# 九天·文思系统主机规划表使用说明

## 1. 概述

主机规划表是自动部署系统的核心配置文件，用于定义各组件的部署位置和基础路径。本文档详细说明主机规划表的格式、内容和使用方法，用于指导客户正确填写部署信息。

## 2. 规划表格式

主机规划表采用CSV（逗号分隔值）格式，便于编辑和处理。文件使用UTF-8编码保存，避免中文字符乱码问题。

## 3. 规划表字段说明

规划表包含以下字段：

| 字段名称 | 数据类型 | 是否必填 | 说明 |
|---------|---------|----------|------|
| host_group | 字符串 | 是 | 主机分组，用于标识组件类型，如middleware, apps, models等 |
| app_name | 字符串 | 是 | 应用名称，与JSON配置文件名保持一致 |
| module_name | 字符串 | 是 | 模块名称，如znwd, hega<PERSON>, ai-writer等 |
| ip_address | 字符串 | 是 | 部署主机IP地址 |
| hostname | 字符串 | 否 | 主机名，可选 |
| base_dir | 字符串 | 是 | 基础安装目录，如/data |
| description | 字符串 | 否 | 组件描述，便于管理 |
| is_enabled | 布尔值 | 是 | 是否启用该组件，true/false |
| deploy_order | 整数 | 是 | 部署顺序，数字越小越先部署 |
| depends_on | 字符串 | 否 | 依赖的其他组件，多个依赖用分号分隔 |

## 4. 规划表示例

```csv
host_group,app_name,module_name,ip_address,hostname,base_dir,description,is_enabled,deploy_order,depends_on
middleware,redis,middleware,********,redis-server,/data,Redis缓存服务,true,10,
middleware,postgresql,middleware,*********,postgres-server,/data,PostgreSQL数据库,true,20,
middleware,rabbitmq,middleware,********,rabbitmq-server,/data,RabbitMQ消息队列,true,30,
middleware,zookeeper,middleware,********,zookeeper-server,/data,Zookeeper服务,true,40,
middleware,elasticsearch,middleware,**********,es-server,/data,Elasticsearch搜索引擎,true,50,
middleware,opensearch,middleware,*********,opensearch-server,/data,OpenSearch搜索引擎,true,60,
middleware,minio,middleware,*********,minio-server,/data,MinIO对象存储,true,70,
models,embedding-service-bge-m3,znwd,**********,embedding-bge-m3,/data,BGE-M3嵌入模型,true,100,
models,bge-reranker-v2-m3,znwd,**********,reranker-bge-m3,/data,BGE重排序模型,true,110,
models,embedding-service-bge-large-zh,znwd,**********,embedding-bge-large,/data,BGE-Large模型,true,120,
models,embedding-service-bce,znwd,**********,embedding-bce,/data,BCE嵌入模型,true,130,
apps,rbac,znwd,10.1.4.120,znwd-rbac,/data,智能问答权限服务,true,200,"redis;postgresql"
apps,qa,znwd,10.1.4.120,znwd-qa,/data,智能问答QA服务,true,210,"rbac;aiknowledge-controller"
apps,aiknowledge-controller,znwd,10.1.4.120,znwd-aiknowledge,/data,AI知识控制器,true,205,"elasticsearch;opensearch;embedding-service-bge-m3"
apps,query-parser,znwd,10.1.4.120,znwd-query-parser,/data,分词模型,true,220,"embedding-service-bge-m3"
apps,rag-qa-online,znwd,10.1.4.120,znwd-rag-qa,/data,RAG问答服务,true,230,"query-parser;embedding-service-bge-m3;bge-reranker-v2-m3"
apps,rag-doc-parser,znwd,10.1.4.120,znwd-rag-doc,/data,文档解析服务,true,240,"embedding-service-bge-m3;opensearch"
apps,rag-kg-os,znwd,10.1.4.120,znwd-rag-kg,/data,知识库向量管理,true,250,"opensearch;embedding-service-bge-m3"
apps,ai-doc-poc,hegao,*********,hegao-ai-doc,/data,核稿后端服务,true,300,"postgresql;ai-hegao-python"
apps,ai-hegao-python,hegao,*********,hegao-ai-python,/data,核稿算法服务,true,290,
apps,article-auto-compose-server,ai-writer,*********,writer-server,/data,写作后端服务,true,350,"postgresql;ai-writer-stream-python;ai-writer-nostream-python"
apps,ai-writer-stream-python,ai-writer,*********,writer-stream,/data,写作流式服务,true,330,
apps,ai-writer-nostream-python,ai-writer,*********,writer-nostream,/data,写作非流式服务,true,340,
frontend,web_assistant,frontend,**********,znwd-frontend,/data,智能问答前端,true,400,"rbac;qa"
frontend,web_assistant_admin,frontend,**********,znwd-admin,/data,智能问答管理端,true,410,"rbac;aiknowledge-controller"
frontend,poc-intelligence-view,frontend,**********,hegao-frontend,/data,核稿前端,true,420,"ai-doc-poc"
```

## 5. 规划表使用流程

1. **获取模板**：从安装包中获取CSV规划表模板文件 `host_planning_template.csv`
2. **填写信息**：按照实际部署环境填写主机信息、IP地址和目录路径
3. **保存文件**：将填写好的CSV文件保存为 `host_planning.csv`，放置在部署工具的根目录
4. **执行检查**：运行检查脚本验证规划表格式和内容的正确性:
   ```bash
   ./check_planning.sh host_planning.csv
   ```
5. **开始部署**：使用规划表启动自动部署流程:
   ```bash
   ./deploy.sh -f host_planning.csv
   ```

## 6. 注意事项

1. **目录权限**：确保规划表中指定的base_dir目录具有足够的读写权限
2. **依赖关系**：正确填写depends_on字段，确保组件按照依赖顺序部署
3. **组件选择**：通过is_enabled字段控制是否部署特定组件
4. **IP地址准确性**：确保IP地址正确且网络可达
5. **路径唯一性**：不同组件的安装路径不应出现冲突

## 7. 常见问题

1. **Q: 如何调整部署顺序？**  
   A: 修改deploy_order字段的数值，数值越小越先部署。

2. **Q: 如何只部署特定组件？**  
   A: 将需要部署的组件is_enabled设置为true，其他设置为false。

3. **Q: 主机规划表可以中途修改吗？**  
   A: 可以修改尚未部署的组件信息，但已部署组件的修改可能需要重新部署。

4. **Q: 多个组件可以部署到同一台主机吗？**  
   A: 可以，只需保证base_dir目录下的路径不冲突。

5. **Q: 部署失败后如何恢复？**  
   A: 系统会记录每步部署状态，可以从失败处继续执行而不必重新开始。 