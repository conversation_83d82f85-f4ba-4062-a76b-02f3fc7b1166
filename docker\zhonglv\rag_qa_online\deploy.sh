#!/bin/bash
APP_NAME=rag-qa-online
MODULE_NAME=znwd
APP_DIR=/data/$MODULE_NAME/$APP_NAME
APP_CONTAINER_PATH=/home/<USER>
APP_CONTAINER_SERVICE_JSON_CONFIG_PATH=$APP_CONTAINER_PATH/config/services.json
APP_CONTAINER_MODEL_YAML_CONFIG_PATH=$APP_CONTAINER_PATH/config/models.yaml
# 容器内日志文件路径
APP_CONTAINER_LOGS_DIR=$APP_CONTAINER_PATH/logs
APP_CONTAINER_LOG_DIR=$APP_CONTAINER_PATH/log
IMAGE_NAME=officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/rag-qa-online:v1
APP_CONTAINER_START_CMD="python -u /home/<USER>/service.py"
DEPLOY_DIR=/data/build/rag_qa_online
echo "=====================容器内信息======================="
echo "容器内路径: $APP_CONTAINER_PATH"
echo "容器内日志目录1: $APP_CONTAINER_LOGS_DIR"
echo "容器内日志目录2: $APP_CONTAINER_LOG_DIR"
echo "容器内服务配置文件路径: $APP_CONTAINER_SERVICE_JSON_CONFIG_PATH"
echo "容器内模型配置文件路径: $APP_CONTAINER_MODEL_YAML_CONFIG_PATH"
echo "容器内启动命令: $APP_CONTAINER_START_CMD"
echo "=====================容器内信息======================="

echo "=====================宿主机信息======================="
echo "宿主机路径: $APP_DIR"
echo "宿主机日志目录1: $APP_DIR/logs    "
echo "宿主机日志目录2: $APP_DIR/log"
echo "宿主机服务配置文件路径: $APP_DIR/config/services.json"
echo "宿主机模型配置文件路径: $APP_DIR/config/models.yaml"
echo "=====================宿主机信息======================="

# 创建目录
echo "创建目录: $APP_DIR"
sudo mkdir -p $APP_DIR
echo "创建日志目录: $APP_DIR/logs"
mkdir -p $APP_DIR/logs
echo "创建日志目录: $APP_DIR/log"
mkdir -p $APP_DIR/log
echo "创建服务配置文件目录: $APP_DIR/config"
mkdir -p $APP_DIR/config

echo "设置权限: chmod -R 755 $APP_DIR"
sudo chmod -R 755 $APP_DIR
# 创建/data/build/rag_qa_online/目录
echo "创建/data/build/rag_qa_online/目录"
mkdir -p $DEPLOY_DIR
echo "将配置文件从/data/build/rag_qa_online目录的json和yaml文件复制到宿主机目录"
cp -r $DEPLOY_DIR/*.json $APP_DIR/config
cp -r $DEPLOY_DIR/*.yaml $APP_DIR/config
tree $DEPLOY_DIR
tree $APP_DIR
# 查看容器是否存在，存在则删除
CONTAINER_ID=$(docker ps -a -q --filter "name=$APP_NAME")
if [ -n "$CONTAINER_ID" ]; then
    echo "容器存在，删除容器"
    docker rm -f $APP_NAME
fi  

CONTAINER_CMD="docker run -itd --name $APP_NAME  \
    --restart=always \
    --network=jiutianwensi \
    -p 8085:7890 \
    -v $APP_DIR/logs:$APP_CONTAINER_LOGS_DIR \
    -v $APP_DIR/log:$APP_CONTAINER_LOG_DIR \
    -v $APP_DIR/config/models.yaml:$APP_CONTAINER_MODEL_YAML_CONFIG_PATH \
    -v $APP_DIR/config/services.json:$APP_CONTAINER_SERVICE_JSON_CONFIG_PATH \
    $IMAGE_NAME $APP_CONTAINER_START_CMD"
echo "启动容器命令: $CONTAINER_CMD"

# 执行启动命令
$CONTAINER_CMD

# 获取容器id
CONTAINER_ID=$(docker ps -q --filter "name=$APP_NAME")
echo "容器id: $CONTAINER_ID"
# 查看 容器状态
docker ps  --filter "name=$APP_NAME"
# 进入容器获取容器内目录结构，树状展示
echo "获取容器内目录结构，树状展示：docker exec -it $APP_NAME /bin/bash -c "ls -R /home/<USER>""
docker exec -it $APP_NAME /bin/bash -c "ls -R /home/<USER>"
# 等待10秒
echo "等待10秒"
sleep 10

# 获取容器日志
echo "获取容器日志"
docker logs  $APP_NAME

# 验证容器是否启动成功
echo "验证容器是否启动成功"
docker ps  --filter "name=$APP_NAME"



# 验证接口
echo '验证接口：curl -X POST -H "Content-Type: application/json" -d {"text": "研发项目立项有哪些环节", "history": []} http://localhost:8085/Rag/QAOnlineSSE'
curl -X POST -H "Content-Type: application/json" \
     -d '{"text": "研发项目立项有哪些环节", "history": []}' \
     http://localhost:8085/Rag/QAOnlineSSE

./test_url.sh
echo -e "\n接口验证完成"
