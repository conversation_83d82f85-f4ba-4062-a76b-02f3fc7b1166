events {
    worker_connections 1024;
}                                                                                                                  
                                                                                                                   
                                                                                                                                                                                                                             
http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式和日志配置
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log  main;
    error_log   /var/log/nginx/error.log  debug;
    
    # 基本性能设置
    sendfile        on;
    keepalive_timeout  65;
    
    # 解析域名时需要（可选）
    resolver *******;
    
    # 定义共享内存区域，用于连接限制和请求限制
    limit_conn_zone $server_name zone=auth_conn:10m;
    limit_req_zone $server_name zone=auth_req:10m rate=10r/s;
                                                                                                                  
    # 通用代理配置（复用配置）
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
                                                                                                                  
    # RabbitMQ管理界面代理 (HTTP 15672)
    server {
        listen 80;
        server_name rabbitmq.jiutian.wensi.com;
                                                                                                                  
        location / {
            proxy_pass http://********:15672;  # RabbitMQ管理界面地址
            proxy_set_header Host $host;
        }
    }
    
    # 主服务配置
    server {
        listen       8092;
        server_name  _;
        client_max_body_size 100m;

        # 授权相关变量
        set $is_args_2 $is_args;
        set $args_2 $args;
        auth_request_set $userId $upstream_http_userId;proxy_set_header userId $userId;
        auth_request_set $userName $upstream_http_userName;proxy_set_header userName $userName;
        auth_request_set $deptId $upstream_http_deptId;proxy_set_header deptId $deptId;
        auth_request_set $roles $upstream_http_roles;proxy_set_header roles $roles;
        
        # RBAC backend 授权
        location ~ ^/authorization {
            internal;
            proxy_pass http://rbac$uri$is_args_2$args_2;
            proxy_pass_request_body off;
            proxy_set_header Content-Length "";
            proxy_set_header path $request_uri;
            proxy_http_version 1.1;
        }
        
        # 授权失败处理
        error_page 401 = @fallback;
        location @fallback {
            return 401 "401";
        }

        # 登录相关接口
        location ~ ^/(captcha|login|sms|authenticateBySMS|logout|m/login|m/sms) {
            limit_conn auth_conn 2;
            limit_req zone=auth_req burst=4 delay=1;
            proxy_pass http://rbac;
            proxy_http_version 1.1;
        }
        
        # API 路由
        location ~ ^/(sys|sys/knowledge|knowledge/doc) {
            auth_request /authorization/userHasPermission;
            proxy_pass http://rbac;
            proxy_http_version 1.1;
        }
        
        location ~ ^/(examples/redis|aiChat/(sendMessage|chat|qryDialogInfos|qryDialogDetails)) {
            auth_request /authorization/userHasPermission;
            proxy_pass http://qagws;
            proxy_http_version 1.1;
        }
        # 核稿服务
        location ~ ^/(ai-doc-poc) {
            proxy_pass http://ai-doc-poc;
            proxy_http_version 1.1;
            # 设置超时
            proxy_connect_timeout 60s;
            proxy_read_timeout 60s;
            proxy_send_timeout 60s;
        }
             # 写作服务
        location ~ ^/(ai-compose-poc) {
            proxy_pass http://ai-compose-poc;
            proxy_http_version 1.1;
            # 设置超时
            proxy_connect_timeout 60s;
            proxy_read_timeout 60s;
            proxy_send_timeout 60s;
        }
        
        # 用户侧前端-灵犀助手智能问答管理端
        # 处理没有尾部斜杠的请求，重定向到带斜杠的URL
        location = /web_assistant_admin {
            return 301 /web_assistant_admin/;
        }

        location ^~ /web_assistant_admin/ {
            alias /usr/share/nginx/html/web_assistant_admin/;
            index index.html;
            try_files $uri $uri/ /web_assistant_admin/index.html;
        }
        
        # 用户侧前端-灵犀助手
        # 处理没有尾部斜杠的请求，重定向到带斜杠的URL
        location = /web_assistant {
            return 301 /web_assistant/;
        }

        location ^~ /web_assistant/ {
            alias /usr/share/nginx/html/web_assistant/;
            index index.html;
            try_files $uri $uri/ /web_assistant/index.html;
        }
        
        # favicon.ico
        location = /favicon.ico {
            alias /usr/share/nginx/html/web_assistant/favicon.ico;
        }
        
        # 核稿前端
        # 处理没有尾部斜杠的请求，重定向到带斜杠的URL
        location = /poc-intelligence-view {
            return 301 /poc-intelligence-view/;
        }

        location ^~ /poc-intelligence-view/ {
            alias /usr/share/nginx/html/poc-intelligence-view/;
            index index.html;
            try_files $uri $uri/ /poc-intelligence-view/index.html;
        }
        
        # 重定向到灵犀助手
        location = / {
            return 302 /web_assistant/;
        }
    }

    # 上游服务定义
    upstream rbac {
        server {{rbac.ip}}:{{rbac.port}};
    }

    upstream qagws {
        server {{qa.ip}}:{{qa.port}};
    }
    # 核稿服务
    upstream ai-doc-poc {
        server {{ai-doc-poc.ip}}:{{ai-doc-poc.port}};
    }
    # 写作服务
    upstream ai-compose-poc {
        server {{article-auto-compose-server.ip}}:{{article-auto-compose-server.port}};
    }
} 