#!/bin/bash
set -e

#######################################
# 帮助信息
#######################################
function show_usage {
    echo "用法: $0 -a <应用名称> -m <模块名称> -p <主机端口> -b <构建版本号> -d <Dockerfile路径>"
    echo "选项:"
    echo "  -a, --app        应用名称"
    echo "  -m, --module     模块名称"
    echo "  -p, --port       主机端口"
    echo "  -b, --build      构建版本号"
    echo "  -d, --dockerfile Dockerfile路径"
    echo "  -h, --help       显示帮助信息"
    exit 1
}

#######################################
# 解析命令行参数
#######################################
APP_NAME=""
MODUL_NAME=""
HOST_PORT=""
BUILD_NUMBER=""
DOCKERFILE_PATH=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -a|--app)
            APP_NAME="$2"
            shift 2
            ;;
        -m|--module)
            MODUL_NAME="$2"
            shift 2
            ;;
        -p|--port)
            HOST_PORT="$2"
            shift 2
            ;;
        -b|--build)
            BUILD_NUMBER="$2"
            shift 2
            ;;
        -d|--dockerfile)
            DOCKERFILE_PATH="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            ;;
        *)
            echo "未知参数: $1"
            show_usage
            ;;
    esac
done

# 检查必要参数
if [[ -z "$APP_NAME" || -z "$MODUL_NAME" || -z "$HOST_PORT" || -z "$BUILD_NUMBER" ]]; then
    echo "错误: 缺少必要参数"
    show_usage
fi

#######################################
# 打印所有输入参数
#######################################
echo "======== DEPLOY PARAMETERS ========"
echo "Application Name: ${APP_NAME}"
echo "Module Name: ${MODUL_NAME}"
echo "Host Port: ${HOST_PORT}"
echo "Build Number: ${BUILD_NUMBER}"
echo "Dockerfile Path: ${DOCKERFILE_PATH}"
echo "==================================="

#获取当前的主机是arm64还是x86_64
ARCH=$(uname -m)
if [ "$ARCH" = "arm64" -o "$ARCH" = "aarch64" ]; then
    echo "当前主机是arm64架构"
    BUILD_NUMBER="arm64-$BUILD_NUMBER"
else
    echo "当前主机是x86_64架构"
    BUILD_NUMBER="x86_64-$BUILD_NUMBER"
fi
echo "BUILD_NUMBER: $BUILD_NUMBER"

#######################################
# 创建应用数据目录结构
#######################################
UPLOAD_DIR=/data/build/${APP_NAME}
APP_DATA_DIR=/data/${MODUL_NAME}/${APP_NAME}
# 询问是否从/data/${MODUL_NAME}/${APP_NAME}目录中拷贝jar文件
read -p "是否从/data/${MODUL_NAME}/${APP_NAME}目录中拷贝文件app.jar? (y/n): " COPY_FILE
if [ "${COPY_FILE}" = "y" ]; then
    echo "复制文件 执行命令: sudo cp -r /data/${MODUL_NAME}/${APP_NAME}/app.jar ${UPLOAD_DIR}/app.jar"
    sudo cp -r /data/${MODUL_NAME}/${APP_NAME}/app.jar ${UPLOAD_DIR}/app.jar    
fi

echo "清理目录 执行命令: sudo rm -rf ${APP_DATA_DIR}/*"
sudo  rm -rf ${APP_DATA_DIR}/*
echo "创建应用数据目录结构 执行命令: sudo mkdir -p ${APP_DATA_DIR}/config"
sudo  mkdir -p ${APP_DATA_DIR}/config
echo "创建应用数据目录结构 执行命令: sudo mkdir -p ${APP_DATA_DIR}/logs"
sudo  mkdir -p ${APP_DATA_DIR}/logs
echo "创建应用数据目录结构 执行命令: sudo mkdir -p ${APP_DATA_DIR}/files"
sudo  mkdir -p ${APP_DATA_DIR}/files
echo "创建应用数据目录结构 执行命令: sudo mkdir -p ${APP_DATA_DIR}/shells"
sudo  mkdir -p ${APP_DATA_DIR}/shells
echo "设置应用数据目录结构权限 执行命令: sudo chmod -R 755 ${APP_DATA_DIR}"
#######################################
# 复制文件
#######################################



echo "上传目录: ${UPLOAD_DIR}"
tree ${UPLOAD_DIR}
echo "复制最新的jar文件"
# 获取修改时间最新的jar文件
echo "获取修改时间最新的jar文件 执行命令: ls -t ${UPLOAD_DIR}/*.jar | head -1"
JAR_FILE=$(ls -t ${UPLOAD_DIR}/*.jar | head -1)
echo "JAR文件路径: ${JAR_FILE}"
JAR_FILE_NAME=$(basename ${JAR_FILE})
echo "找到JAR文件: ${JAR_FILE_NAME}"
echo "复制JAR文件：sudo cp ${JAR_FILE} ${APP_DATA_DIR}/app.jar"
sudo  cp ${JAR_FILE} ${APP_DATA_DIR}/app.jar
echo "设置应用数据目录结构权限 执行命令: sudo chmod -R 755 ${APP_DATA_DIR}"
sudo  chmod -R 755 ${APP_DATA_DIR}
sudo  chmod -R 777 ${APP_DATA_DIR}/logs



echo "复制jar文件....."
echo "复制文件 执行命令: sudo cp -r ${UPLOAD_DIR}/*.jar /data/${MODUL_NAME}/${APP_NAME}"
sudo cp -r ${UPLOAD_DIR}/*.jar /data/${MODUL_NAME}/${APP_NAME}

echo "复制restart.sh文件....."
# 先判断有没有文件，没有文件则不复制，只是打印日志
if [ -f ${UPLOAD_DIR}/restart.sh ]; then
    echo "复制文件 执行命令: sudo cp -r ${UPLOAD_DIR}/restart.sh /data/${MODUL_NAME}/${APP_NAME}/shells"
    sudo cp -r ${UPLOAD_DIR}/restart.sh /data/${MODUL_NAME}/${APP_NAME}/shells
    ls -la /data/${MODUL_NAME}/${APP_NAME}/shells
else
    echo "没有找到文件: ${UPLOAD_DIR}/restart.sh"
fi


# 先判断有没有文件，没有文件则不复制，只是打印日志
echo "复制配置文件....."
if ls ${UPLOAD_DIR}/*.yml >/dev/null 2>&1; then
    echo "复制文件 执行命令: sudo cp -r ${UPLOAD_DIR}/*.yml /data/${MODUL_NAME}/${APP_NAME}/config"
    sudo cp -r ${UPLOAD_DIR}/*.yml /data/${MODUL_NAME}/${APP_NAME}/config
    ls -la /data/${MODUL_NAME}/${APP_NAME}/config
else
    echo "没有找到文件: ${UPLOAD_DIR}/*.yml"
fi
if ls ${UPLOAD_DIR}/*.properties >/dev/null 2>&1; then
    echo "复制文件 执行命令: sudo cp -r ${UPLOAD_DIR}/*.properties /data/${MODUL_NAME}/${APP_NAME}/config"
    sudo cp -r ${UPLOAD_DIR}/*.properties /data/${MODUL_NAME}/${APP_NAME}/config
    ls -la /data/${MODUL_NAME}/${APP_NAME}/config
else
    echo "没有找到文件: ${UPLOAD_DIR}/*.properties"
fi
if ls ${UPLOAD_DIR}/*.xml >/dev/null 2>&1; then
    echo "复制文件 执行命令: sudo cp -r ${UPLOAD_DIR}/*.xml /data/${MODUL_NAME}/${APP_NAME}/config"
    sudo cp -r ${UPLOAD_DIR}/*.xml /data/${MODUL_NAME}/${APP_NAME}/config
    ls -la /data/${MODUL_NAME}/${APP_NAME}/config
else
    echo "没有找到文件: ${UPLOAD_DIR}/*.xml"
fi

# 先从外部拷贝start.sh文件到容器中，如果没有，则使用默认的启动命令
echo "复制start.sh文件....."
if [ -f ${UPLOAD_DIR}/start.sh ]; then
    echo "start.sh文件存在"
    echo "复制start.sh文件 ${UPLOAD_DIR}/start.sh 到 ${APP_DATA_DIR}/shells"
    sudo cp ${UPLOAD_DIR}/start.sh ${APP_DATA_DIR}/shells
    ls -la ${APP_DATA_DIR}/shells
else
    echo "start.sh文件不存在，使用默认的启动命令 "
    echo "默认的启动命令: java -jar /app/app.jar --spring.config.location=/app/config/ --server.port=8080 --server.servlet.context-path=/ --server.context.path=/"
    echo "java -jar /app/app.jar --spring.config.location=/app/config/ --server.port=8080 --server.servlet.context-path=/ --server.context.path=/" > ${APP_DATA_DIR}/start.sh
fi

# 获取JAR文件名称
# 获取修改时间最新的JAR文件
JAR_FILE=$(ls -t ${APP_DATA_DIR}/*.jar | head -1)
echo "JAR文件路径: ${JAR_FILE}"
JAR_FILE_NAME=$(basename ${JAR_FILE})
echo "找到JAR文件: ${JAR_FILE_NAME}"
echo "应用数据目录结构: "
tree ${APP_DATA_DIR}
#######################################
# 容器生命周期管理
#######################################
# 停止并删除容器
# 先判断容器是否存在，不存在则不停止，只是打印日志  
if docker ps -a | grep -q ${APP_NAME}; then
    echo "停止容器 执行命令: sudo docker stop ${APP_NAME} || true"
    sudo docker stop  ${APP_NAME} || true
    echo "删除容器 执行命令: sudo docker rm -f ${APP_NAME} || true"
    sudo docker rm -f ${APP_NAME} || true
    echo "容器已停止并删除"
else
    echo "容器不存在: ${APP_NAME}"
fi

# 根据用户选择是否重新构建镜像
read -p "是否重新构建镜像? (y/n): " REBUILD_IMAGE
if [ "${REBUILD_IMAGE}" = "y" ]; then
    echo "重新构建镜像"
    # 先删除镜像
    # 先判断镜像是否存在，不存在则不删除，只是打印日志
    if sudo docker images ${MODUL_NAME}/${APP_NAME} | grep -q ${BUILD_NUMBER}; then
        echo "删除镜像 执行命令: sudo docker rmi -f ${MODUL_NAME}/${APP_NAME}:${BUILD_NUMBER}"
        sudo docker rmi -f ${MODUL_NAME}/${APP_NAME}:${BUILD_NUMBER}
        echo "删除镜像成功"
    else
        echo "镜像不存在: ${MODUL_NAME}/${APP_NAME}:${BUILD_NUMBER}"
    fi

    # 删除none镜像
    NONE_IMAGES=$(sudo docker images | grep "^<none>" | awk '{print $3}')
    if [ -n "$NONE_IMAGES" ]; then
        echo "删除none镜像 执行命令: sudo docker rmi -f $NONE_IMAGES"
        sudo docker rmi -f $NONE_IMAGES
        echo "删除none镜像成功"
    else
        echo "none镜像不存在"
    fi

    # 创建临时构建目录
    BUILD_TMP_DIR=$(mktemp -d)
    echo "创建临时构建目录: ${BUILD_TMP_DIR}"

    # 进入临时目录
    cd ${BUILD_TMP_DIR}

    # 复制JAR文件到当前目录并重命名为app.jar
    echo "复制JAR文件 ${JAR_FILE} 到当前目录的app.jar"
    sudo cp ${JAR_FILE} app.jar

    # 复制配置文件
    mkdir -p config
    echo "复制配置文件 ${APP_DATA_DIR}/config/* 到 config/ 目录"
    sudo cp -r ${APP_DATA_DIR}/config/* config/ 2>/dev/null || true
    echo "复制start.sh文件 ${APP_DATA_DIR}/shells/start.sh 到当前目录"
    mkdir -p shells
    cp ${APP_DATA_DIR}/shells/start.sh shells/start.sh
    echo "临时构建目录结构: "
    tree ${BUILD_TMP_DIR}
    echo "构建镜像 执行命令: sudo docker build  --no-cache -t ${MODUL_NAME}/${APP_NAME}:${BUILD_NUMBER} --build-arg JAR_FILE=${JAR_FILE} --build-arg APP_NAME=${APP_NAME} --build-arg APP_DIR=${APP_DATA_DIR} -f ${DOCKERFILE_PATH} ."
    sudo docker build --no-cache -t  ${MODUL_NAME}/${APP_NAME}:${BUILD_NUMBER} --build-arg JAR_FILE=${JAR_FILE} --build-arg APP_NAME=${APP_NAME} --build-arg APP_DIR=${APP_DATA_DIR} -f ${DOCKERFILE_PATH}  .
    # 返回原目录并清理临时目录
    cd -
    echo "清理临时目录 执行命令: rm -rf ${BUILD_TMP_DIR}"
    rm -rf ${BUILD_TMP_DIR}
else
    echo "不重新构建镜像"
fi


# 创建network，如果存在则不创建
if ! docker network ls | grep -q jiutianwensi; then
    echo "创建network 执行命令: sudo docker network create jiutianwensi"
    sudo docker network create jiutianwensi
else
    echo "network已存在: jiutianwensi"
fi

echo "启动容器 执行命令: sudo docker run -itd \
    --name ${APP_NAME} \
    --network jiutianwensi \
    -v ${APP_DATA_DIR}/config:/app/config \
    -v ${APP_DATA_DIR}/logs:/app/logs \
    -v ${APP_DATA_DIR}/files:/app/files \
    -v ${APP_DATA_DIR}/shells:/app/shells \
    -p ${HOST_PORT}:8080 \

    ${MODUL_NAME}/${APP_NAME}:${BUILD_NUMBER}"
sudo docker run -itd \
    --name ${APP_NAME} \
    --network jiutianwensi \
    -v ${APP_DATA_DIR}/config:/app/config \
    -v ${APP_DATA_DIR}/logs:/app/logs \
    -v ${APP_DATA_DIR}/files:/app/files \
    -v ${APP_DATA_DIR}/shells:/app/shells \
    -p ${HOST_PORT}:8080 \
    ${MODUL_NAME}/${APP_NAME}:${BUILD_NUMBER}

#######################################
# 清理旧镜像（保留最近5个版本）
#######################################
sudo docker images ${MODUL_NAME}/${APP_NAME} |
    awk -v current=${BUILD_NUMBER} 'NR>1 && $2 != current {print $1":"$2}' |
    tail -n +6 |
    xargs -r sudo docker rmi
# 查看当前启动的容器是否正常启动，若存在，则打印日志，否则打印错误日志
if sudo docker ps  | grep ${APP_NAME}; then
    echo "容器已启动"
    # 打印容器具体信息
    echo "容器具体信息： sudo docker ps  | grep ${APP_NAME}"
    sudo docker ps  | grep ${APP_NAME}
    #echo "容器详细信息： sudo docker inspect ${APP_NAME}"
    #sudo docker inspect ${APP_NAME}
else
    echo "容器未启动"
    echo "容器未启动： sudo docker ps  | grep ${APP_NAME}"
    sudo docker ps  | grep ${APP_NAME}
    echo "容器未启动： sudo docker inspect ${APP_NAME}"
    sudo docker inspect ${APP_NAME}
fi

#######################################
# 记录部署日志
#######################################
echo "Build ${BUILD_NUMBER} deployed at $(date)" >> ${APP_DATA_DIR}/deploy.log

#######################################
# 打印部署日志
#######################################
cat ${APP_DATA_DIR}/deploy.log

#######################################
# 打印容器日志
#######################################
echo "等待10秒后打印容器日志"
sleep 10
echo "打印容器日志 执行命令: sudo docker logs  ${APP_NAME}"
sudo docker logs  ${APP_NAME}

echo "设置logs目录的权限"
echo "设置logs目录的权限 执行命令: sudo chmod -R 777 ${APP_DATA_DIR}/logs"
sudo chmod -R 777 ${APP_DATA_DIR}/logs

echo "设置files目录的权限"
echo "设置files目录的权限 执行命令: sudo chmod -R 777 ${APP_DATA_DIR}/files"
sudo chmod -R 777 ${APP_DATA_DIR}/files
# 重启一下filebeat
echo "重启一下filebeat"
# 先判断filebeat是否存在，不存在则不重启，只是打印日志
if sudo docker ps | grep -q filebeat; then
    sudo docker restart filebeat
else
    echo "filebeat不存在"
fi
# 询问是否需要推送镜像
read -p "是否需要推送镜像? (y/n): " PUSH_IMAGE
if [ "${PUSH_IMAGE}" = "y" ]; then
    echo "推送镜像"
    echo "推送镜像 执行命令: ./push.sh ${APP_NAME} ${MODUL_NAME} ${BUILD_NUMBER}"
    ./push.sh ${APP_NAME} ${MODUL_NAME} ${BUILD_NUMBER}
fi


