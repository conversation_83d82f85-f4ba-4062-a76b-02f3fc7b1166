#!/bin/bash

# 创建必要目录结构
mkdir -p /data/jenkins/{data,config,logs,plugins}

# 拉取Jenkins镜像（使用DaoCloud加速）
docker pull m.daocloud.io/docker.io/jenkins/jenkins:lts-jdk17

docker rm -f jenkins
docker run -d --name jenkins -p 8080:8080 -p 50000:50000  -e JENKINS_ADMIN_PASSWORD=admin123 -v /data/jenkins/data:/var/jenkins_home -v $(which docker):/usr/bin/docker -v /var/run/docker.sock:/var/run/docker.sock -v /home/<USER>/opt/data -u 0 --restart=on-failure:3 m.daocloud.io/docker.io/jenkins/jenkins:lts-jdk17

  
# 密码：ea46d1b272224c23bd9d2d75700958b7



# 用户名/密码  admin/Jiutianwensi0411
