#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# ====================== API测试命令 ======================
echo -e "${BLUE}=== 文档生成API测试 ===${NC}"
HOST="************:32006"
prompt="请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求```1800```字以上。"

# 简单请求 - 只需text字段
echo -e "${YELLOW}执行简单请求...${NC}"

# 构建请求命令
SIMPLE_CMD="curl -X POST 'http://${HOST}/mubanwriter/v1/service' \
  -H 'Content-Type: application/json' \
  -d '{
    \"text\": \"${prompt}\"
  }'"

# 打印请求命令
echo -e "${BLUE}请求命令:${NC}"
echo -e "${YELLOW}${SIMPLE_CMD}${NC}"

# 开始时间
START_TIME=$(date +%s)

# 执行请求
eval "${SIMPLE_CMD}"

echo -e "\n${GREEN}=== 文档生成API测试结束 ===${NC}"
# 结束时间
END_TIME=$(date +%s)
# 计算时间差
EXEC_TIME=$((END_TIME - START_TIME))
echo -e "${YELLOW}请求耗时: ${EXEC_TIME} 秒${NC}"

# 带extension的请求
echo -e "\n${BLUE}=== 带extension的请求测试 ===${NC}"

# 构建请求命令
EXTENSION_CMD="curl -X POST 'http://${HOST}/mubanwriter/v1/service' \
  -H 'Content-Type: application/json' \
  -d '{
    \"text\": \"${prompt}\",
    \"extension\": {
      \"docInfo\": {
        \"sourceText\": \"关于召开项目进度评审会议的通知\",
        \"universalType\": \"通知\",
        \"subTypeName\": \"会议通知\",
        \"mainDeliveryUnit\": \"各部门负责人\"
      },
      \"fileContent\": {
        \"modelEssay\": [\"为确保项目按计划推进，解决项目过程中的问题，特召开本次项目进度评审会议。\"]
      }
    }
  }'"

# 打印请求命令
echo -e "${BLUE}请求命令:${NC}"
echo -e "${YELLOW}${EXTENSION_CMD}${NC}"

# 开始时间
START_TIME=$(date +%s)

# 执行请求
eval "${EXTENSION_CMD}"

echo -e "\n${GREEN}=== 带extension的请求测试结束 ===${NC}"
# 结束时间
END_TIME=$(date +%s)
# 计算时间差
EXEC_TIME=$((END_TIME - START_TIME))
echo -e "${YELLOW}请求耗时: ${EXEC_TIME} 秒${NC}"

# 进入k8s容器中运行
echo -e "\n${BLUE}=== 进入k8s容器中运行 ===${NC}"

# 进入k8s容器中运行大模型接口
echo -e "\n${BLUE}=== 进入k8s容器中运行大模型接口 ===${NC}"
HOST="localhost:8080"
cmd="curl -X POST 'http://${HOST}/mubanwriter/v1/service' \
  -H 'Content-Type: application/json' \
  -d '{
    \"text\": \"请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求```1800```字以上。\"
  }'"
echo -e "${YELLOW}${cmd}${NC}"
eval "${cmd}"












