name: embedding_service
channels:
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - bzip2=1.0.8
  - ca-certificates=2025.2.25
  - libffi=3.4.4
  # - ncurses=6.4
  # 特殊情况安装
  # conda install vc
  # conda install -c conda-forge datrie==0.8.2
  - openssl=3.0.16
  - pip=25.0
  - python=3.10.16
  # - readline=8.2
  - setuptools=75.8.0
  - sqlite=3.45.3
  - tk=8.6.14
  - wheel=0.45.1
  - xz=5.6.4
  - zlib=1.2.13
  - pip:
      - aiohappyeyeballs==2.6.1
      - aspose-slides==25.4.0
      - aiohttp==3.11.18
      - aiosignal==1.3.2
      - async-timeout==5.0.1
      - attrs==25.3.0
      - bcembedding==0.1.5
      - bs4==0.0.2
      - beautifulsoup4==4.13.4
      - certifi==2025.1.31
      - charset-normalizer==3.4.1
      - cachetools==5.5.2
      - cffi==1.17.1
      - cn2an==0.5.23
      - colorama==0.4.6
      - cryptography==44.0.2
      - click==8.1.8
      - coloredlogs==15.0.1
      - datasets==3.5.0
      - dill==0.3.8
      - Django==5.0.6
      - djangorestframework==3.15.1
      - datrie==0.8.2
      - python-docx==1.1.2
      - emoji==2.12.1
      - elastic-transport==8.17.1
      - elasticsearch==8.18.0
      - elasticsearch-dsl==8.18.0
      - exceptiongroup==1.2.2
      - et_xmlfile==2.0.0
      - filelock==3.18.0
      - frozenlist==1.6.0
      - fsspec==2023.10.0
      - flatbuffers==25.2.10
      - huggingface-hub==0.30.2
      - hanziconv==0.3.2
      - html_text==0.6.2
      - html-to-json==2.0.0
      - html2text==2025.4.15
      - humanfriendly==10.0
      - idna==3.10
      - jinja2==3.1.6
      - joblib==1.4.2
      - jieba==0.42.1
      - lxml_html_clean==0.2.0
      - lxml==5.3.2
      - markupsafe==3.0.2
      - mpmath==1.3.0
      - multidict==6.4.3
      - multiprocess==0.70.16
      - Markdown==3.4.3
      - networkx==3.4.2
      - numpy==1.26.4
      - nltk==3.9.1
      - opencv-python-headless==********
      - opencv-contrib-python==*********
      - opencv-python==*********
      - openpyxl==3.1.5
      - onnxruntime-gpu==1.19.0
      - outcome==1.3.0.post0
      - packaging==24.2
      - pandas==2.2.3
      - pillow==11.2.1
      - propcache==0.3.1
      - pyarrow==19.0.1
      - python-dateutil==2.9.0.post0
      - pdfminer.six==20231228
      - pytz==2025.2
      - pyyaml==6.0.2
      - pdfplumber==0.11.1
      - pypdf==4.3.1
      - pycryptodome==3.20.0
      - pycryptodomex==3.22.0
      - pyclipper==1.3.0.post6
      - PyPDF2==3.0.1
      - pypdfium2==4.30.1
      - python-pptx==1.0.2
      - pyreadline3==3.5.4
      - proces==0.1.7
      - protobuf==6.30.2
      - pycparser==2.22
      - regex==2024.11.6
      - requests==2.32.3
      - roman-numbers==1.0.2
      - ruamel.yaml==0.18.10
      - ruamel.yaml.clib==0.2.12
      - readability-lxml==0.8.1
      - safetensors==0.5.3
      - scikit-learn==1.6.1
      - scipy==1.15.2
      - sentence-transformers==2.7.0
      - six==1.17.0
      - sympy==1.13.1
      - sortedcontainers==2.4.0
      - StrEnum==0.4.15
      - shapely==2.1.0
      - sniffio==1.3.1
      - soupsieve==2.6
      - threadpoolctl==3.6.0
      - tokenizers==0.15.2
      - torch==2.6.0
      - tornado==6.2
      - tqdm==4.67.1
      - trio==0.29.0
      - transformers==4.36.2
      - typing_extensions==4.13.2
      - typing-extensions==4.13.2
      - tzdata==2025.2
      - tika==2.6.0
      - tiktoken==0.6.0
      - urllib3==2.4.0
      - word2number==1.1
      - xxhash==3.5.0
      - xgboost==3.0.0
      - XlsxWriter==3.2.3
      - yarl==1.20.0
      - zhconv==1.4.3
prefix: /opt/miniconda3/envs/embedding_service
