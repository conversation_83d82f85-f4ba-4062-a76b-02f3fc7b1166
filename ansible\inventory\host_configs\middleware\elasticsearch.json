{"middleware_name": "elasticsearch", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "docker.elastic.co/elasticsearch/elasticsearch:7.17.14", "host_port": "9200", "container_port": "9200", "host_transport_port": "9300", "container_transport_port": "9300", "username": "elastic", "password": "Elastic20250417@#", "host_data_dir": "{{base_dir}}/elasticsearch/data", "host_logs_dir": "{{base_dir}}/elasticsearch/logs", "host_config_dir": "{{base_dir}}/elasticsearch/config", "container_data_dir": "/usr/share/elasticsearch/data", "container_logs_dir": "/usr/share/elasticsearch/logs", "container_config_dir": "/usr/share/elasticsearch/config", "restart_script": "{{base_dir}}/elasticsearch/restart.sh", "test_script": "{{base_dir}}/elasticsearch/test_curl.sh", "env_vars": [{"name": "discovery.type", "value": "single-node"}, {"name": "ES_JAVA_OPTS", "value": "-Xms1g -Xmx1g"}, {"name": "xpack.security.enabled", "value": "true"}, {"name": "ELASTIC_PASSWORD", "value": "Elastic20250417@#"}, {"name": "bootstrap.memory_lock", "value": "true"}], "config": {"cluster.name": "es-docker-cluster", "node.name": "node-1", "network.host": "0.0.0.0", "discovery.type": "single-node", "xpack.security.enabled": "true"}, "test_commands": ["curl -u elastic:Elastic20250417@# -X GET 'http://localhost:9200/'", "curl -u elastic:Elastic20250417@# -X PUT 'http://localhost:9200/test'"]}