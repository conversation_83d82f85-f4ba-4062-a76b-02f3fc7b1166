#设置基础镜像☆☆☆☆☆☆☆☆☆☆根据工程运行需要配置带有相应环境的运行用途镜像☆☆☆☆☆☆☆☆☆☆
FROM artifactory.dep.devops.cmit.cloud:20101/tools/panji-base-images/euler-openjdk:17 AS final
#配置工作目录
ARG JAR_FILE
ARG APP_NAME
ARG APP_DIR
RUN mkdir -p /app
RUN mkdir -p /app/logs
RUN mkdir -p /app/config
RUN mkdir -p /app/files
RUN mkdir -p /app/shells
RUN mkdir -p /home/<USER>
RUN groupadd -g 1000 wensi
RUN useradd -u 1000 -d /home/<USER>/bin/bash  -g wensi wensi
RUN export LANG="zh_CN.UTF-8"
ENV LANG=zh_CN.UTF-8 LC_ALL=zh_CN.UTF-8
# 用阿里的yum源
#[openEuler]
#name=openEuler
#baseurl=https://mirrors.aliyun.com/openeuler/openEuler-22.03-LTS-SP2/OS/x86_64/
#enabled=1
#gpgcheck=1
#gpgkey=https://mirrors.aliyun.com/openeuler/openEuler-22.03-LTS-SP2/OS/x86_64/RPM-GPG-KEY-openEuler
RUN echo "[openEuler]" > /etc/yum.repos.d/openEuler.repo
RUN echo "name=openEuler" >> /etc/yum.repos.d/openEuler.repo
RUN echo "baseurl=https://mirrors.aliyun.com/openeuler/openEuler-22.03-LTS-SP2/OS/x86_64/" >> /etc/yum.repos.d/openEuler.repo
RUN echo "enabled=1" >> /etc/yum.repos.d/openEuler.repo
RUN echo "gpgcheck=1" >> /etc/yum.repos.d/openEuler.repo
RUN echo "gpgkey=https://mirrors.aliyun.com/openeuler/openEuler-22.03-LTS-SP2/OS/x86_64/RPM-GPG-KEY-openEuler" >> /etc/yum.repos.d/openEuler.repo
# 安装tree
#RUN yum install -y tree
#RUN apk update && apk add busybox-extras
RUN chmod -Rf 755 /app/
RUN chown -R wensi:wensi /app/
RUN chown -R wensi:wensi /home/<USER>/
WORKDIR /app
RUN echo "将应用JAR文件复制到容器中，JAR文件: ${JAR_FILE}"

# 使用一个简单的技巧：通过命令行方式复制JAR文件
COPY app.jar /app/app.jar
# 使用配置文件复制
COPY config/ /app/config/
COPY shells/ /app/shells/
RUN ls -la /app

# 创建启动脚本，确保使用Unix行结束符和正确的执行权限
RUN echo '#!/bin/bash' > /app/shells/start.sh && \
    echo 'java -jar /app/app.jar --spring.config.location=/app/config/ --server.port=8080 --server.servlet.context-path=/ --server.context.path=/' >> /app/shells/start.sh && \
    chmod +x /app/shells/start.sh && \
    cat /app/shells/start.sh

USER wensi
CMD ["/bin/bash", "/app/shells/start.sh"]