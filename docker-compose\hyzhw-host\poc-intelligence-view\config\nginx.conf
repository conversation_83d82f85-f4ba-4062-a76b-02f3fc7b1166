events {
    worker_connections  1000;
}

error_log  /var/log/nginx/error.log debug;

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  3000;

    log_format  main  '$remote_addr:$remote_port - $remote_user [$time_local] '
                            '"$request_method $request_uri $server_protocol" '
                            '$status $body_bytes_sent '
                            '"$http_referer" "$http_user_agent" "$upstream_addr" '
                            '"$request_body"';
    access_log  /var/log/nginx/access.log main;

    # 设置全局超时时间
    proxy_connect_timeout 3000s;
    proxy_read_timeout 3000s;
    proxy_send_timeout 3000s;
    proxy_buffer_size 1280k;
    proxy_buffers 4 2560k;
    proxy_busy_buffers_size 2560k;

    limit_conn_zone $server_name zone=auth_conn:200m;
    limit_req_zone $server_name zone=auth_req:200m rate=1r/s;

    server {
        listen       20003;
        server_name  localhost;

        # 开启gzip压缩
        gzip on;
        gzip_min_length 1k;
        gzip_comp_level 6;
        gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
        gzip_vary on;
        gzip_disable "MSIE [1-6]\.";

        # 静态资源缓存设置
        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
            expires 7d;
        }

        location ^~ /poc-intelligence-view/ {
            alias /usr/share/nginx/html/poc-intelligence-view/;
            index index.html;
            try_files $uri $uri/ /poc-intelligence-view/index.html;
        }

        # 核稿服务
        location /ai-doc-poc {
            proxy_pass http://localhost:20005/ai-doc-poc;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $host;
        }
    }
}