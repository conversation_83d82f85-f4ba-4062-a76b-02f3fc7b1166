CURRENT_DIR=$(pwd)
# 获取父目录
PARENT_DIR=$(dirname ${CURRENT_DIR})
# 检查命令
# 如果jq不存在，则安装jq
if ! command -v jq &> /dev/null; then
    echo "jq不存在，安装jq"
    if command -v apt-get &> /dev/null; then
        sudo apt-get install -y jq
    elif command -v yum &> /dev/null; then
        sudo yum install -y jq
    else
        echo "无法安装jq，请手动安装"
        exit 1
    fi
    if [ $? -ne 0 ]; then
        echo "安装jq失败"
        exit 1
    fi
fi

# 读取config-template.json文件
# 生成一个新的工作的config.json文件
# 将config-template.json文件中{{字段}}替换为config.json文件中的值
CONFIG_TEMPLATE_FILE="${CURRENT_DIR}/config-template.json"
CONFIG_FILE="${CURRENT_DIR}/config.json"
if [ ! -f ${CONFIG_TEMPLATE_FILE} ]; then
    echo "${CONFIG_TEMPLATE_FILE}文件不存在"
    exit 1
fi
create_config_file() {  
    cp ${CONFIG_TEMPLATE_FILE} ${CONFIG_FILE}   
    # 读取base-config部分
    # 读取base-config中的字段
    BASE_CPU_ARCH=$(jq -r '.["base-config"]["cpu-arch"]' ${CONFIG_FILE})
    # 读取target-image-repo-url
    BASE_TARGET_REPO_URL=$(jq -r '.["base-config"]["target-image-repo-url"]' ${CONFIG_FILE})
    # 读取target-image-repo-user
    BASE_TARGET_REPO_USER=$(jq -r '.["base-config"]["target-image-repo-user"]' ${CONFIG_FILE})
    # 读取target-image-repo-password
    BASE_TARGET_REPO_PASSWORD=$(jq -r '.["base-config"]["target-image-repo-password"]' ${CONFIG_FILE})
    # 读取target-image-namespace
    BASE_TARGET_NAMESPACE=$(jq -r '.["base-config"]["target-image-namespace"]' ${CONFIG_FILE})
    # 读取source-image-repo-url
    BASE_SOURCE_REPO_URL=$(jq -r '.["base-config"]["source-image-repo-url"]' ${CONFIG_FILE})
    # 读取source-image-repo-user
    BASE_SOURCE_REPO_USER=$(jq -r '.["base-config"]["source-image-repo-user"]' ${CONFIG_FILE})
    # 读取source-image-repo-password
    BASE_SOURCE_REPO_PASSWORD=$(jq -r '.["base-config"]["source-image-repo-password"]' ${CONFIG_FILE})
    # 读取source-image-namespace
    BASE_SOURCE_NAMESPACE=$(jq -r '.["base-config"]["source-image-namespace"]' ${CONFIG_FILE})
    #如果filebeat-config存在，则替换filebeat-config中的所有{{字段}}   
    if jq -r '.["filebeat-config"]' ${CONFIG_FILE} > /dev/null 2>&1; then
        # 替换filebeat-config中的所有{{字段}}   
        FILEBEAT_SOURCE_IMAGES_URL=$(jq -r '.["filebeat-config"]["source-images-url"]' ${CONFIG_FILE})
        FILEBEAT_TARGET_IMAGES_URL=$(jq -r '.["filebeat-config"]["target-images-url"]' ${CONFIG_FILE})
        # 替换filebeat-config中的所有{{字段}}
        FILEBEAT_SOURCE_IMAGES_URL=${FILEBEAT_SOURCE_IMAGES_URL//\{\{source-image-repo-url\}\}/$BASE_SOURCE_REPO_URL}
        FILEBEAT_SOURCE_IMAGES_URL=${FILEBEAT_SOURCE_IMAGES_URL//\{\{source-image-namespace\}\}/$BASE_SOURCE_NAMESPACE}
        FILEBEAT_SOURCE_IMAGES_URL=${FILEBEAT_SOURCE_IMAGES_URL//\{\{cpu-arch\}\}/$BASE_CPU_ARCH}   
        FILEBEAT_TARGET_IMAGES_URL=${FILEBEAT_TARGET_IMAGES_URL//\{\{target-image-repo-url\}\}/$BASE_TARGET_REPO_URL}
        FILEBEAT_TARGET_IMAGES_URL=${FILEBEAT_TARGET_IMAGES_URL//\{\{target-image-namespace\}\}/$BASE_TARGET_NAMESPACE}
        FILEBEAT_TARGET_IMAGES_URL=${FILEBEAT_TARGET_IMAGES_URL//\{\{cpu-arch\}\}/$BASE_CPU_ARCH}
        
        # 将替换后的值写回配置文件
        jq ".\"filebeat-config\".\"source-images-url\" = \"$FILEBEAT_SOURCE_IMAGES_URL\"" ${CONFIG_FILE} > ${CONFIG_FILE}.tmp && mv ${CONFIG_FILE}.tmp ${CONFIG_FILE}
        jq ".\"filebeat-config\".\"target-images-url\" = \"$FILEBEAT_TARGET_IMAGES_URL\"" ${CONFIG_FILE} > ${CONFIG_FILE}.tmp && mv ${CONFIG_FILE}.tmp ${CONFIG_FILE}
    else
        echo "filebeat-config不存在"
    fi

    # 如果proxy-config.nginx-config.image-url存在，则替换proxy-config.nginx-config.image-url中的所有{{字段}}
    if jq -r '.["proxy-config"]["nginx-config"]["image-url"]' ${CONFIG_FILE} > /dev/null 2>&1; then
        # 替换proxy-config.nginx-config.image-url中的所有{{字段}}
        NGINX_IMAGE_URL=$(jq -r '.["proxy-config"]["nginx-config"]["image-url"]' ${CONFIG_FILE})
        NGINX_IMAGE_URL=${NGINX_IMAGE_URL//\{\{target-image-repo-url\}\}/$BASE_TARGET_REPO_URL}
        NGINX_IMAGE_URL=${NGINX_IMAGE_URL//\{\{target-image-namespace\}\}/$BASE_TARGET_NAMESPACE}
        NGINX_IMAGE_URL=${NGINX_IMAGE_URL//\{\{cpu-arch\}\}/$BASE_CPU_ARCH}
        # 将替换后的值写回配置文件
        jq ".\"proxy-config\".\"nginx-config\".\"image-url\" = \"$NGINX_IMAGE_URL\"" ${CONFIG_FILE} > ${CONFIG_FILE}.tmp && mv ${CONFIG_FILE}.tmp ${CONFIG_FILE}
    else
        echo "proxy-config.nginx-config.image-url不存在"
    fi
    # 替换replace-config中的所有{{字段}}
    # 遍历replace-config数组中的每个元素
    REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})
    
    for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
        # 获取当前元素的source-images-url和target-images-url
        SOURCE_URL=$(jq -r ".\"replace-config\"[$i][\"source-images-url\"]" ${CONFIG_FILE})
        TARGET_URL=$(jq -r ".\"replace-config\"[$i][\"target-images-url\"]" ${CONFIG_FILE})
        
        # 替换{{字段}}
        SOURCE_URL=${SOURCE_URL//\{\{source-image-repo-url\}\}/$BASE_SOURCE_REPO_URL}
        SOURCE_URL=${SOURCE_URL//\{\{source-image-namespace\}\}/$BASE_SOURCE_NAMESPACE}
        SOURCE_URL=${SOURCE_URL//\{\{cpu-arch\}\}/$BASE_CPU_ARCH}
        TARGET_URL=${TARGET_URL//\{\{target-image-repo-url\}\}/$BASE_TARGET_REPO_URL}
        TARGET_URL=${TARGET_URL//\{\{target-image-namespace\}\}/$BASE_TARGET_NAMESPACE}
        TARGET_URL=${TARGET_URL//\{\{cpu-arch\}\}/$BASE_CPU_ARCH}
        
        # 更新config.json文件
        jq ".\"replace-config\"[$i][\"source-images-url\"] = \"$SOURCE_URL\"" ${CONFIG_FILE} > ${CONFIG_FILE}.tmp && mv ${CONFIG_FILE}.tmp ${CONFIG_FILE}
        jq ".\"replace-config\"[$i][\"target-images-url\"] = \"$TARGET_URL\"" ${CONFIG_FILE} > ${CONFIG_FILE}.tmp && mv ${CONFIG_FILE}.tmp ${CONFIG_FILE}
    done
    echo "已生成config.json文件，并替换了所有{{字段}}"
}



# 判断config.json文件是否存在
if [ ! -f ${CONFIG_FILE} ]; then
    echo "config.json文件不存在,开始创建config.json文件"
    create_config_file
    if [ $? -ne 0 ]; then
        echo "创建config.json文件失败"
        exit 1
    fi
    echo "创建config.json文件成功，路径为${CONFIG_FILE}"
else
    echo "config.json文件存在，路径为${CONFIG_FILE}"
    #询问是否需要重新创建config.json文件
    read -p "是否需要重新创建config.json文件？(y/n): " RECREATE_CONFIG_FILE
    if [ "${RECREATE_CONFIG_FILE}" == "y" ]; then
        echo "开始重新创建config.json文件"
        create_config_file
        if [ $? -ne 0 ]; then
            echo "重新创建config.json文件失败"
            exit 1
        fi
        echo "重新创建config.json文件成功，路径为${CONFIG_FILE}"
    fi
fi

