
echo "创建持久化目录:"
echo "mkdir -p /data/zookeeper/data"
mkdir -p /data/zookeeper/data
echo "启动容器:"
echo "docker run -itd --name zookeeper  -p 2181:2181 -v /data/zookeeper/data:/data    -e TZ="Asia/Shanghai"       --restart always            m.daocloud.io/docker.io/zookeeper:3.9.3"
docker run -itd --name zookeeper  -p 2181:2181 -v /data/zookeeper/data:/data    -e TZ="Asia/Shanghai"       --restart always            m.daocloud.io/docker.io/zookeeper:3.9.3
echo "连接zookeeper:"
echo "docker exec -it zookeeper bash ./bin/zkCli.sh -server localhost:2181"
docker exec -it zookeeper bash ./bin/zkCli.sh -server localhost:2181
echo "创建测试节点:"
echo "docker exec -it zookeeper zkCli.sh -server localhost:2181 create /test 'hello'"
docker exec -it zookeeper zkCli.sh -server localhost:2181 create /test 'hello'
echo "查看节点:"
echo "docker exec -it zookeeper zkCli.sh -server localhost:2181 ls /"
docker exec -it zookeeper zkCli.sh -server localhost:2181 ls /
echo "删除节点:"
echo "docker exec -it zookeeper zkCli.sh -server localhost:2181 delete /test"
docker exec -it zookeeper zkCli.sh -server localhost:2181 delete /test
