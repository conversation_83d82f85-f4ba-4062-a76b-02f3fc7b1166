#!/bin/bash
#执行当前目录下的所有测试脚本，脚本中的执行结果时间，最后形成一个表格
# 应用执行的顺序
# 核稿
# 1. poc-intelligence-view
# 2. ai-doc-poc
# 3. ai-redactor
# 4. 调用大模型
# 拟稿
# 1. poc-intelligence-view-write
# 2. article-auto-compose-server
# 3. ai-writer-nostream
# 4. 调用大模型

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# 创建结果数组
declare -A results

# 打印表头
echo -e "\n${YELLOW}服务性能测试结果${NC}"
echo -e "${BLUE}===============================================${NC}"
echo -e "${BLUE}服务名称\t\t容器内时间\t容器外时间\t内部处理时间${NC}"
echo -e "${BLUE}-----------------------------------------------${NC}"

# 核稿流程测试
echo -e "\n${YELLOW}开始核稿流程测试${NC}"

# 1. poc-intelligence-view
echo -e "\n${GREEN}测试 poc-intelligence-view 服务${NC}"
START_TIME=$(date +%s)
./test-poc-intelligence-view.sh > /dev/null 2>&1
END_TIME=$(date +%s)
IN_TIME=$((END_TIME - START_TIME))
results["poc-intelligence-view_in"]=$IN_TIME

START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST 'http://10.7.202.242:20004/poc-intelligence-view/api/v1/check' -F 'file=@test.txt;type=text/plain')
END_TIME=$(date +%s)
OUT_TIME=$((END_TIME - START_TIME))
results["poc-intelligence-view_out"]=$OUT_TIME

# 提取内部处理时间
PROCESS_TIME=$(echo $RESPONSE | grep -o '"processTime":[0-9]*' | cut -d':' -f2)
if [ -z "$PROCESS_TIME" ]; then
    PROCESS_TIME="N/A"
fi
results["poc-intelligence-view_process"]=$PROCESS_TIME

echo -e "poc-intelligence-view\t${IN_TIME}s\t\t${OUT_TIME}s\t\t${PROCESS_TIME}ms"

# 2. ai-doc-poc
echo -e "\n${GREEN}测试 ai-doc-poc 服务${NC}"
START_TIME=$(date +%s)
./test-ai-doc-poc.sh > /dev/null 2>&1
END_TIME=$(date +%s)
IN_TIME=$((END_TIME - START_TIME))
results["ai-doc-poc_in"]=$IN_TIME

START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST 'http://10.7.202.242:20005/ai-doc-poc/api/v1/check' -F 'file=@test.txt;type=text/plain')
END_TIME=$(date +%s)
OUT_TIME=$((END_TIME - START_TIME))
results["ai-doc-poc_out"]=$OUT_TIME

# 提取内部处理时间
PROCESS_TIME=$(echo $RESPONSE | grep -o '"processTime":[0-9]*' | cut -d':' -f2)
if [ -z "$PROCESS_TIME" ]; then
    PROCESS_TIME="N/A"
fi
results["ai-doc-poc_process"]=$PROCESS_TIME

echo -e "ai-doc-poc\t\t${IN_TIME}s\t\t${OUT_TIME}s\t\t${PROCESS_TIME}ms"

# 3. ai-redactor
echo -e "\n${GREEN}测试 ai-redactor 服务${NC}"
START_TIME=$(date +%s)
./test-ai-redactor.sh > /dev/null 2>&1
END_TIME=$(date +%s)
IN_TIME=$((END_TIME - START_TIME))
results["ai-redactor_in"]=$IN_TIME

START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST 'http://10.7.202.242:20006/ai-redactor/api/v1/check' -F 'file=@test.txt;type=text/plain')
END_TIME=$(date +%s)
OUT_TIME=$((END_TIME - START_TIME))
results["ai-redactor_out"]=$OUT_TIME

# 提取内部处理时间
PROCESS_TIME=$(echo $RESPONSE | grep -o '"processTime":[0-9]*' | cut -d':' -f2)
if [ -z "$PROCESS_TIME" ]; then
    PROCESS_TIME="N/A"
fi
results["ai-redactor_process"]=$PROCESS_TIME

echo -e "ai-redactor\t\t${IN_TIME}s\t\t${OUT_TIME}s\t\t${PROCESS_TIME}ms"

# 4. 大模型调用（核稿）
echo -e "\n${GREEN}测试大模型调用（核稿）${NC}"
START_TIME=$(date +%s)
./test-llm.sh > /dev/null 2>&1
END_TIME=$(date +%s)
LLM_TIME=$((END_TIME - START_TIME))
results["llm_check_time"]=$LLM_TIME
echo -e "大模型调用（核稿）\t${LLM_TIME}s"

# 拟稿流程测试
echo -e "\n${YELLOW}开始拟稿流程测试${NC}"

# 1. poc-intelligence-view-write
echo -e "\n${GREEN}测试 poc-intelligence-view-write 服务${NC}"
START_TIME=$(date +%s)
./test-poc-intelligence-view-write.sh > /dev/null 2>&1
END_TIME=$(date +%s)
IN_TIME=$((END_TIME - START_TIME))
results["poc-intelligence-view-write_in"]=$IN_TIME

START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST 'http://10.7.202.242:20003/poc-intelligence-view-write/api/v1/check' -F 'file=@test.txt;type=text/plain')
END_TIME=$(date +%s)
OUT_TIME=$((END_TIME - START_TIME))
results["poc-intelligence-view-write_out"]=$OUT_TIME

# 提取内部处理时间
PROCESS_TIME=$(echo $RESPONSE | grep -o '"processTime":[0-9]*' | cut -d':' -f2)
if [ -z "$PROCESS_TIME" ]; then
    PROCESS_TIME="N/A"
fi
results["poc-intelligence-view-write_process"]=$PROCESS_TIME

echo -e "poc-intelligence-view-write\t${IN_TIME}s\t\t${OUT_TIME}s\t\t${PROCESS_TIME}ms"

# 2. article-auto-compose-server
echo -e "\n${GREEN}测试 article-auto-compose-server 服务${NC}"
START_TIME=$(date +%s)
./test-article-auto-compose-server.sh > /dev/null 2>&1
END_TIME=$(date +%s)
IN_TIME=$((END_TIME - START_TIME))
results["article-auto-compose-server_in"]=$IN_TIME

START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST 'http://10.7.202.242:20007/ai-compose-poc/api/v1/compose' -H 'Content-Type: application/json' -d '{"composeUnitId":"glmwriter","text":"test"}')
END_TIME=$(date +%s)
OUT_TIME=$((END_TIME - START_TIME))
results["article-auto-compose-server_out"]=$OUT_TIME

# 提取内部处理时间
PROCESS_TIME=$(echo $RESPONSE | grep -o '"processTime":[0-9]*' | cut -d':' -f2)
if [ -z "$PROCESS_TIME" ]; then
    PROCESS_TIME="N/A"
fi
results["article-auto-compose-server_process"]=$PROCESS_TIME

echo -e "article-auto-compose-server\t${IN_TIME}s\t\t${OUT_TIME}s\t\t${PROCESS_TIME}ms"

# 3. ai-writer-nostream
echo -e "\n${GREEN}测试 ai-writer-nostream 服务${NC}"
START_TIME=$(date +%s)
./test-ai-writer-nostream.sh > /dev/null 2>&1
END_TIME=$(date +%s)
IN_TIME=$((END_TIME - START_TIME))
results["ai-writer-nostream_in"]=$IN_TIME

START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST 'http://10.7.202.242:20008/mubanwriter/v1/service' -H 'Content-Type: application/json' -d '{"text":"test"}')
END_TIME=$(date +%s)
OUT_TIME=$((END_TIME - START_TIME))
results["ai-writer-nostream_out"]=$OUT_TIME

# 提取内部处理时间
PROCESS_TIME=$(echo $RESPONSE | grep -o '"processTime":[0-9]*' | cut -d':' -f2)
if [ -z "$PROCESS_TIME" ]; then
    PROCESS_TIME="N/A"
fi
results["ai-writer-nostream_process"]=$PROCESS_TIME

echo -e "ai-writer-nostream\t${IN_TIME}s\t\t${OUT_TIME}s\t\t${PROCESS_TIME}ms"

# 4. 大模型调用（拟稿）
echo -e "\n${GREEN}测试大模型调用（拟稿）${NC}"
START_TIME=$(date +%s)
./test-llm.sh > /dev/null 2>&1
END_TIME=$(date +%s)
LLM_TIME=$((END_TIME - START_TIME))
results["llm_compose_time"]=$LLM_TIME
echo -e "大模型调用（拟稿）\t${LLM_TIME}s"

# 打印总结
echo -e "\n${YELLOW}测试总结${NC}"
echo -e "${BLUE}===============================================${NC}"
echo -e "${BLUE}服务名称\t\t容器内时间\t容器外时间\t内部处理时间${NC}"
echo -e "${BLUE}-----------------------------------------------${NC}"

# 核稿流程总结
echo -e "\n${GREEN}核稿流程${NC}"
echo -e "poc-intelligence-view\t${results[poc-intelligence-view_in]}s\t\t${results[poc-intelligence-view_out]}s\t\t${results[poc-intelligence-view_process]}ms"
echo -e "ai-doc-poc\t\t${results[ai-doc-poc_in]}s\t\t${results[ai-doc-poc_out]}s\t\t${results[ai-doc-poc_process]}ms"
echo -e "ai-redactor\t\t${results[ai-redactor_in]}s\t\t${results[ai-redactor_out]}s\t\t${results[ai-redactor_process]}ms"
echo -e "大模型调用（核稿）\t${results[llm_check_time]}s"

# 拟稿流程总结
echo -e "\n${GREEN}拟稿流程${NC}"
echo -e "poc-intelligence-view-write\t${results[poc-intelligence-view-write_in]}s\t\t${results[poc-intelligence-view-write_out]}s\t\t${results[poc-intelligence-view-write_process]}ms"
echo -e "article-auto-compose-server\t${results[article-auto-compose-server_in]}s\t\t${results[article-auto-compose-server_out]}s\t\t${results[article-auto-compose-server_process]}ms"
echo -e "ai-writer-nostream\t${results[ai-writer-nostream_in]}s\t\t${results[ai-writer-nostream_out]}s\t\t${results[ai-writer-nostream_process]}ms"
echo -e "大模型调用（拟稿）\t${results[llm_compose_time]}s"

# 计算总时间
total_in_time=$((results[poc-intelligence-view_in] + results[ai-doc-poc_in] + results[ai-redactor_in] + 
                results[poc-intelligence-view-write_in] + results[article-auto-compose-server_in] + 
                results[ai-writer-nostream_in]))

total_out_time=$((results[poc-intelligence-view_out] + results[ai-doc-poc_out] + results[ai-redactor_out] + 
                 results[poc-intelligence-view-write_out] + results[article-auto-compose-server_out] + 
                 results[ai-writer-nostream_out]))

total_llm_time=$((results[llm_check_time] + results[llm_compose_time]))

# 计算平均内部处理时间
total_process_time=0
count=0
for service in "poc-intelligence-view" "ai-doc-poc" "ai-redactor" "poc-intelligence-view-write" "article-auto-compose-server" "ai-writer-nostream"; do
    if [ "${results[${service}_process]}" != "N/A" ]; then
        total_process_time=$((total_process_time + results[${service}_process]))
        count=$((count + 1))
    fi
done

if [ $count -gt 0 ]; then
    avg_process_time=$((total_process_time / count))
else
    avg_process_time="N/A"
fi

echo -e "\n${YELLOW}总执行时间${NC}"
echo -e "${BLUE}===============================================${NC}"
echo -e "容器内总时间: ${total_in_time}秒"
echo -e "容器外总时间: ${total_out_time}秒"
echo -e "大模型调用总时间: ${total_llm_time}秒"
echo -e "平均内部处理时间: ${avg_process_time}ms"


