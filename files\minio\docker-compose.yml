version: '3'

services:
  minio-server:
    image: m.daocloud.io/docker.io/minio/minio:RELEASE.2023-04-28T18-11-17Z
    container_name: minio-server
    hostname: minio
    ports:
      - "${MINIO_API_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    volumes:
      - ${BASE_DIR:-/data/MinIO}/data:/data
      - ${BASE_DIR:-/data/MinIO}/logs:/var/log/minio
      - ${BASE_DIR:-/data/MinIO}/config:/root/.minio
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-admin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-MiniO@2025}
      - TZ=Asia/Shanghai
    command: server /data --console-address ":9001"
    restart: always
    networks:
      - jiutianwensi-network

networks:
  jiutianwensi-network:
    driver: bridge 