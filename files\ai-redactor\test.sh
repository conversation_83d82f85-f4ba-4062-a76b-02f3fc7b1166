#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# ====================== API测试命令 ======================
echo -e "${BLUE}=== 文档生成API测试 ===${NC}"

echo -e "${GREEN}=== 带extension的请求测试结束 ===${NC}"

# ====================== 拼写检查API测试 ======================
echo -e "${BLUE}=== 拼写检查API测试 ===${NC}"
echo -e "${YELLOW}正在测试拼写检查API...${NC}"

echo "curl -X POST "http://localhost:8097/spellcheck/oa" \
  -H "Content-Type: application/json" \
  -d '{"content":"请输入你的测试文笨。","type_list":[],"user_id":"123"}'"
  
curl -X POST "http://localhost:8097/spellcheck/oa" \
  -H "Content-Type: application/json" \
  -d '{"content":"请输入你的测试文笨。","type_list":[],"user_id":"123"}'

echo
echo -e "${GREEN}=== 拼写检查API测试结束 ===${NC}"


