#!/bin/bash
# 部署根目录设置
echo "部署根目录设置"
DEPLOY_DIR="/data/nginx"
echo "创建三级目录结构"
mkdir -p ${DEPLOY_DIR}/{conf,log,html}
echo "设置权限"
chmod -R 755 ${DEPLOY_DIR}
echo "删除容器"
# 创建jiutianwensi的网络，如果存在就不再创建
echo "创建jiutianwensi的网络"
docker network ls | grep jiutianwensi
if [ $? -ne 0 ]; then
    docker network create jiutianwensi
fi
# 删除容器，如果不存在就不删除
echo "删除容器"
docker rm -f nginx

echo "启动命令："
echo "docker run -itd --network jiutianwensi \
-p 30912:80 \
-p 8092:8092 \
-p 6379:6379 \
-p 5672:5672 \
-p 2181:2181 \
--name nginx \
-v ${DEPLOY_DIR}/conf/nginx.conf:/etc/nginx/nginx.conf \
-v ${DEPLOY_DIR}/html:/usr/share/nginx/html \
-v ${DEPLOY_DIR}/log:/var/log/nginx \
m.daocloud.io/docker.io/nginx:1.27.4"

echo "启动容器"
docker run -itd --network jiutianwensi \
-p 30912:80 \
-p 8092:8092 \
-p 6379:6379 \
-p 5672:5672 \
-p 2181:2181 \
--name nginx \
-v ${DEPLOY_DIR}/conf/nginx.conf:/etc/nginx/nginx.conf \
-v ${DEPLOY_DIR}/html:/usr/share/nginx/html \
-v ${DEPLOY_DIR}/log:/var/log/nginx \
m.daocloud.io/docker.io/nginx:1.27.4

echo "启动成功"
echo "查看容器状态"
docker ps | grep nginx
echo "查看日志"
docker logs  nginx