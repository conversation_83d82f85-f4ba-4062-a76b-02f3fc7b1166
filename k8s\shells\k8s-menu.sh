#!/bin/bash
# K8s服务管理菜单
# 用于简化Nginx代理和K8s服务管理操作

# 颜色定义
GREEN="\033[0;32m"
BLUE="\033[0;34m"
RED="\033[0;31m"
YELLOW="\033[1;33m"
NC="\033[0m" # 恢复默认颜色

# 获取脚本所在目录的绝对路径
CURRENT_DIR=$(cd "$(dirname "$0")" && pwd)
# 获取k8s目录的绝对路径（CURRENT_DIR的父目录）
K8S_DIR=$(dirname "${CURRENT_DIR}")
# 导出为环境变量，供子脚本使用
export K8S_DIR
export SHELLS_DIR="${CURRENT_DIR}"

echo -e "${BLUE}K8s根目录: ${K8S_DIR}${NC}"
echo -e "${BLUE}脚本目录: ${SHELLS_DIR}${NC}"

# 检测是否有JTWS_WORK_DIR环境变量，如果没有则设置为当前目录的父目录
WORK_DIR=${JTWS_WORK_DIR:-$K8S_DIR}
echo -e "${BLUE}工作目录: ${WORK_DIR}${NC}"

# 加载工具函数
source "${SHELLS_DIR}/utils/utils.sh"
echo -e "${BLUE}加载工具函数: ${SHELLS_DIR}/utils/utils.sh${NC}"

# 清屏函数
clear_screen() {
    clear
}

# 显示标题
show_title() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}           K8s服务管理控制台 v1.6              ${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}K8s根目录: ${K8S_DIR}${NC}"
    echo
}

# 显示主菜单
show_main_menu() {
    clear_screen
    show_title
    echo -e "${GREEN}1. 准备配置文件${NC}"
    echo -e "${GREEN}2. 下载镜像${NC}"
    echo -e "${GREEN}3. 替换配置文件${NC}"
    echo -e "${BLUE}--------------------------------${NC}"
    echo -e "${GREEN}4. 服务部署菜单${NC}"
    echo -e "${GREEN}5. 查看所有服务状态${NC}"
    echo -e "${GREEN}6. 管理特定服务${NC}"
    echo -e "${GREEN}7. 显示访问指南${NC}"
    echo -e "${GREEN}8. 清理资源${NC}"
    echo -e "${GREEN}9. 管理调试Pod${NC}"
    echo -e "${GREEN}10. 管理端口转发${NC}"
    echo -e "${RED}0. 退出${NC}"
    echo
    echo -e "${YELLOW}请选择操作 [0-10]:${NC} "
}

# 显示服务管理菜单
show_service_menu() {
    local service_name=$1
    clear_screen
    show_title
    echo -e "${BLUE}当前管理服务: ${service_name}${NC}"
    echo
    echo -e "${GREEN}1. 查看服务状态${NC}"
    echo -e "${GREEN}2. 查看Pod日志${NC}" 
    echo -e "${GREEN}3. 进入容器${NC}"
    echo -e "${GREEN}4. 诊断服务问题${NC}"
    echo -e "${GREEN}5. 重启服务${NC}"
    echo -e "${GREEN}6. 检测资源类型${NC}"
    echo -e "${GREEN}7. 调试服务容器${NC}"
    echo -e "${RED}8. 删除服务${NC}"
    echo -e "${YELLOW}9. 返回主菜单${NC}"
    echo -e "${RED}0. 退出${NC}"
    echo
    echo -e "${YELLOW}请选择操作 [0-9]:${NC} "
}

# 准备配置文件
prepare_config() {
    clear_screen
    show_title
    echo -e "${BLUE}准备配置文件...${NC}"
    
    # 检查配置文件是否存在
    if [ -f "${SHELLS_DIR}/config.json" ]; then
        read -p "配置文件已存在，是否重新创建？(y/n): " recreate
        if [ "$recreate" != "y" ]; then
            echo -e "${YELLOW}跳过配置文件创建${NC}"
            return
        fi
    fi
    
    # 执行配置文件创建脚本
    bash ${SHELLS_DIR}/create_config_file.sh
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}配置文件创建成功！${NC}"
    else
        echo -e "${RED}配置文件创建失败！${NC}"
    fi
    
    echo
    echo -e "${GREEN}按任意键返回主菜单${NC}"
    read -n 1
}

# 下载镜像
# 下载完成后询问是否推送镜像
# 若用户同意则调用images-push.sh
# 其余逻辑不变
download_images() {
    clear_screen
    show_title
    echo -e "${BLUE}开始下载镜像...${NC}"
    
    # 检查配置文件是否存在
    if [ ! -f "${SHELLS_DIR}/config.json" ]; then
        echo -e "${RED}错误：配置文件不存在，请先准备配置文件！${NC}"
        echo
        echo -e "${GREEN}按任意键返回主菜单${NC}"
        read -n 1
        return
    fi
    
    # 执行镜像下载脚本
    bash ${SHELLS_DIR}/download-images.sh
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}镜像下载完成！${NC}"
        echo
        read -p "是否立即推送镜像到目标仓库？(y/n): " push_choice
        if [[ "$push_choice" == "y" || "$push_choice" == "Y" ]]; then
            echo -e "${BLUE}开始推送镜像...${NC}"
            bash ${SHELLS_DIR}/images-push.sh
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}镜像推送完成！${NC}"
            else
                echo -e "${RED}镜像推送失败！${NC}"
            fi
        fi
    else
        echo -e "${RED}镜像下载失败！${NC}"
    fi
    
    echo
    echo -e "${GREEN}按任意键返回主菜单${NC}"
    read -n 1
}

# 替换配置文件
replace_configs() {
    clear_screen
    show_title
    echo -e "${BLUE}开始替换配置文件...${NC}"
    
    # 检查配置文件是否存在
    if [ ! -f "${SHELLS_DIR}/config.json" ]; then
        echo -e "${RED}错误：配置文件不存在，请先准备配置文件！${NC}"
        echo
        echo -e "${GREEN}按任意键返回主菜单${NC}"
        read -n 1
        return
    fi
    
    # 执行配置文件替换脚本
    bash ${SHELLS_DIR}/replace-ymls.sh
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}配置文件替换完成！${NC}"
    else
        echo -e "${RED}配置文件替换失败！${NC}"
    fi
    
    echo
    echo -e "${GREEN}按任意键返回主菜单${NC}"
    read -n 1
}

# 查看所有服务状态
view_all_services() {
    clear_screen
    show_title
    echo -e "${BLUE}获取所有服务状态...${NC}"
    echo
    
    # 获取所有服务
    echo -e "${YELLOW}服务列表:${NC}"
    kubectl get pods -n oa-llm
    echo
    echo -e "${YELLOW}服务详情:${NC}"
    kubectl get services -n oa-llm
    echo
    
    # 获取Nginx代理状态
    echo -e "${YELLOW}Nginx代理状态:${NC}"
    check_k8s_resource "nginx-proxy" "oa-llm"
    if [ "$FOUND_RESOURCE" = "true" ]; then
        kubectl get pods -n oa-llm -l app=nginx-proxy
    else
        echo -e "${RED}Nginx代理未部署或无法找到${NC}"
    fi
    
    echo
    echo -e "${GREEN}操作完成，按任意键返回主菜单${NC}"
    read -n 1
}

# 显示访问指南
show_access_guide() {
    clear_screen
    show_title
    echo -e "${BLUE}服务访问指南${NC}"
    echo

    # 读取配置信息
    if [ -f "${SHELLS_DIR}/config.json" ]; then
        REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${SHELLS_DIR}/config.json)
        NGINX_PORT=$(jq -r '.["proxy-config"]["nginx-config"]["port"]' ${SHELLS_DIR}/config.json)
        DOMAIN_SUFFIX=$(jq -r '.["proxy-config"]["nginx-config"]["service-name-suffix"]' ${CURRENT_DIR}/config.json)
        
        # 检查是否为空，设置默认值
        [[ "$NGINX_PORT" == "null" ]] && NGINX_PORT="30001"
        [[ "$DOMAIN_SUFFIX" == "null" ]] && DOMAIN_SUFFIX="oallm.jtws.com"
        
        # 获取节点IP
        NODE_IP=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
        
        echo -e "${YELLOW}访问信息:${NC}"
        echo -e "Nginx端口: ${NGINX_PORT}"
        echo -e "域名后缀: ${DOMAIN_SUFFIX}"
        echo -e "节点IP: ${NODE_IP}"
        echo
        
        echo -e "${YELLOW}各服务访问地址:${NC}"
        for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
            SERVICE_NAME=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CURRENT_DIR}/config.json)
            echo -e "${SERVICE_NAME}: http://${SERVICE_NAME}.${DOMAIN_SUFFIX}:${NGINX_PORT}"
        done
        
        echo
        echo -e "${YELLOW}hosts文件配置示例:${NC}"
        echo -e "${NODE_IP} *.${DOMAIN_SUFFIX}"
    else
        echo -e "${RED}配置文件不存在，无法显示访问信息${NC}"
    fi
    
    echo
    echo -e "${GREEN}操作完成，按任意键返回主菜单${NC}"
    read -n 1
}

# 管理特定服务
manage_specific_service() {
    clear_screen
    show_title
    echo -e "${BLUE}选择要管理的服务${NC}"
    echo
    
    # 获取服务列表
    local services=($(kubectl get pods -n oa-llm -o jsonpath='{.items[*].metadata.name}'))
    
    if [ ${#services[@]} -eq 0 ]; then
        echo -e "${RED}未找到任何服务${NC}"
        echo
        echo -e "${GREEN}按任意键返回主菜单${NC}"
        read -n 1
        return
    fi
    
    echo -e "${YELLOW}可用服务:${NC}"
    for i in "${!services[@]}"; do
        echo -e "${GREEN}$((i+1)). ${services[$i]}${NC}"
    done
    
    echo
    read -p "选择服务编号 [1-${#services[@]}] (或输入 0 返回): " choice
    
    if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${#services[@]} ]; then
        local selected_pod=${services[$((choice-1))]}
        local selected_service=$(echo $selected_pod | sed 's/-[^-]*-[^-]*$//')
        
        # 处理服务管理菜单
        while true; do
            show_service_menu $selected_pod
            read -p "" service_choice
            
            case $service_choice in
                1)
                    # 查看服务状态
                    clear_screen
                    show_title
                    echo -e "${BLUE}查看服务 ${selected_pod} 状态${NC}"
                    echo
                    kubectl describe pod $selected_pod -n oa-llm
                    echo
                    echo -e "${GREEN}操作完成，按任意键返回${NC}"
                    read -n 1
                    ;;
                2)
                    # 查看Pod日志
                    clear_screen
                    show_title
                    echo -e "${BLUE}查看服务 ${selected_pod} 日志${NC}"
                    echo
                    view_pod_logs "$selected_pod" "oa-llm"
                    ;;
                3)
                    # 进入容器
                    clear_screen
                    show_title
                    echo -e "${BLUE}进入服务 ${selected_pod} 容器${NC}"
                    echo
                    enter_pod_container "$selected_pod" "oa-llm"
                    ;;
                4)
                    # 诊断服务问题
                    clear_screen
                    show_title
                    echo -e "${BLUE}诊断服务 ${selected_pod} 问题${NC}"
                    echo
                    # 尝试获取资源类型
                    check_k8s_resource $selected_service "oa-llm"
                    if [ "$FOUND_RESOURCE" = "true" ]; then
                        diagnose_resource_issues "$selected_service" "$RESOURCE_TYPE" "oa-llm"
                    else
                        echo -e "${RED}无法确定服务资源类型，使用Pod诊断${NC}"
                        kubectl describe pod $selected_pod -n oa-llm
                    fi
                    
                    echo
                    echo -e "${GREEN}操作完成，按任意键返回${NC}"
                    read -n 1
                    ;;
                5)
                    # 重启服务
                    clear_screen
                    show_title
                    echo -e "${BLUE}重启服务 ${selected_pod}${NC}"
                    echo
                    
                    # 使用新的detect_k8s_resource.sh脚本检测资源类型
                    # 捕获脚本的JSON输出，日志信息会输出到终端
                    RESULT=$(${SHELLS_DIR}/utils/detect_k8s_resource.sh "$selected_pod" "oa-llm")
                    DETECT_STATUS=$?  # 获取脚本的退出状态码
                    
                    # 检查是否安装了jq工具
                    if ! command -v jq &> /dev/null; then
                        echo -e "${RED}错误: 未找到jq工具，无法解析JSON结果${NC}"
                        echo -e "${YELLOW}请使用命令安装: apt-get install jq -y${NC}"
                        read -p "按任意键继续..." -n1
                        break
                    fi
                    
                    # 解析JSON结果
                    FOUND_RESOURCE=$(echo $RESULT | jq -r '.found')
                    RESOURCE_TYPE=$(echo $RESULT | jq -r '.type')
                    RESOURCE_NAME=$(echo $RESULT | jq -r '.name')
                    
                    if [ "$FOUND_RESOURCE" = "true" ]; then
                        echo -e "${YELLOW}确认重启 ${RESOURCE_TYPE} ${RESOURCE_NAME}?${NC}"
                        read -p "输入 'yes' 确认: " confirm
                        if [ "$confirm" = "yes" ]; then
                            kubectl rollout restart $RESOURCE_TYPE $RESOURCE_NAME -n oa-llm
                            echo -e "${GREEN}服务重启指令已发送${NC}"
                        else
                            echo -e "${YELLOW}操作已取消${NC}"
                        fi
                    else
                        echo -e "${RED}无法确定服务资源类型，只能删除Pod${NC}"
                        echo -e "${RED}警告: 删除Pod可能导致临时服务中断${NC}"
                        read -p "输入 'yes' 确认删除Pod: " confirm
                        if [ "$confirm" = "yes" ]; then
                            kubectl delete pod $selected_pod -n oa-llm
                            echo -e "${GREEN}Pod已删除，等待重建${NC}"
                        else
                            echo -e "${YELLOW}操作已取消${NC}"
                        fi
                    fi
                    
                    echo
                    echo -e "${GREEN}操作完成，按任意键返回${NC}"
                    read -n 1
                    ;;
                6)
                    # 检测资源类型
                    clear_screen
                    show_title
                    echo -e "${BLUE}检测服务 ${selected_pod} 的资源类型${NC}"
                    echo
                    
                    # 调用资源检测脚本，捕获JSON输出
                    RESULT=$(${SHELLS_DIR}/utils/detect_k8s_resource.sh "$selected_pod" "oa-llm")
                    DETECT_STATUS=$?  # 获取脚本的退出状态码
                    
                    # 检查是否安装了jq工具
                    if ! command -v jq &> /dev/null; then
                        echo -e "${RED}错误: 未找到jq工具，无法解析JSON结果${NC}"
                        echo -e "${YELLOW}请使用命令安装: apt-get install jq -y${NC}"
                        read -p "按任意键继续..." -n1
                        break
                    fi
                    
                    # 显示JSON格式结果
                    echo -e "${YELLOW}检测结果 (JSON):${NC}"
                    echo $RESULT | jq '.'
                    
                    # 解析JSON并显示人性化结果
                    FOUND_RESOURCE=$(echo $RESULT | jq -r '.found')
                    RESOURCE_TYPE=$(echo $RESULT | jq -r '.type')
                    RESOURCE_NAME=$(echo $RESULT | jq -r '.name')
                    NAMESPACE=$(echo $RESULT | jq -r '.namespace')
                    
                    echo
                    echo -e "${BLUE}检测结果摘要:${NC}"
                    if [ "$FOUND_RESOURCE" = "true" ]; then
                        echo -e "${GREEN}✓ 已找到资源控制器${NC}"
                        echo -e "${GREEN}• 资源类型: ${RESOURCE_TYPE}${NC}"
                        echo -e "${GREEN}• 资源名称: ${RESOURCE_NAME}${NC}"
                        echo -e "${GREEN}• 命名空间: ${NAMESPACE}${NC}"
                        
                        # 根据资源类型显示不同的重启命令
                        echo
                        echo -e "${BLUE}重启命令:${NC}"
                        if [[ "$RESOURCE_TYPE" == "deployment" || "$RESOURCE_TYPE" == "statefulset" || "$RESOURCE_TYPE" == "daemonset" ]]; then
                            echo -e "${YELLOW}kubectl rollout restart ${RESOURCE_TYPE} ${RESOURCE_NAME} -n ${NAMESPACE}${NC}"
                        else
                            echo -e "${RED}此资源类型 (${RESOURCE_TYPE}) 无法使用rollout restart命令${NC}"
                        fi
                    else
                        echo -e "${RED}✗ 未找到资源控制器${NC}"
                        echo -e "${RED}• 可能只能通过删除Pod方式重启${NC}"
                        echo -e "${RED}• 命令: kubectl delete pod ${selected_pod} -n ${NAMESPACE}${NC}"
                    fi
                    
                    echo
                    echo -e "${GREEN}操作完成，按任意键返回${NC}"
                    read -n 1
                    ;;
                7)
                    # 调试服务容器
                    clear_screen
                    show_title
                    echo -e "${BLUE}调试服务 ${selected_pod} 容器${NC}"
                    echo
                    
                    echo -e "${YELLOW}请选择操作:${NC}"
                    echo -e "${GREEN}1. 进入调试模式${NC} (修改容器命令以保持运行并进入shell)"
                    echo -e "${RED}2. 删除资源${NC} (删除Pod或控制器)"
                    echo -e "${YELLOW}0. 返回${NC}"
                    echo
                    read -p "请选择 [0-2]: " debug_choice
                    
                    case $debug_choice in
                        1)
                            echo -e "${YELLOW}进入调试模式...${NC}"
                            echo -e "${YELLOW}此操作将修改容器命令以保持容器运行${NC}"
                            echo -e "${YELLOW}在完成调试后可以选择恢复原始配置${NC}"
                            read -p "是否继续? (yes/no): " confirm
                            if [ "$confirm" != "yes" ]; then
                                echo -e "${YELLOW}操作已取消${NC}"
                                echo
                                echo -e "${GREEN}按任意键返回${NC}"
                                read -n 1
                                continue
                            fi
                            
                            # 检查debug_pod.sh脚本是否存在
                            if [ -f "${SHELLS_DIR}/debug/debug_pod.sh" ]; then
                                # 调用调试脚本
                                bash "${SHELLS_DIR}/debug/debug_pod.sh" "$selected_pod" "oa-llm" "debug"
                            else
                                echo -e "${RED}错误: 未找到调试脚本 (${SHELLS_DIR}/debug/debug_pod.sh)${NC}"
                            fi
                            ;;
                        2)
                            echo -e "${RED}进入删除模式...${NC}"
                            read -p "确认要执行删除操作? (yes/no): " confirm
                            if [ "$confirm" != "yes" ]; then
                                echo -e "${YELLOW}操作已取消${NC}"
                                echo
                                echo -e "${GREEN}按任意键返回${NC}"
                                read -n 1
                                continue
                            fi
                            
                            # 检查debug_pod.sh脚本是否存在
                            if [ -f "${SHELLS_DIR}/debug/debug_pod.sh" ]; then
                                # 调用调试脚本的删除功能
                                bash "${SHELLS_DIR}/debug/debug_pod.sh" "$selected_pod" "oa-llm" "delete"
                            else
                                echo -e "${RED}错误: 未找到调试脚本 (${SHELLS_DIR}/debug/debug_pod.sh)${NC}"
                            fi
                            ;;
                        0|*)
                            echo -e "${YELLOW}返回上级菜单${NC}"
                            ;;
                    esac
                    
                    echo
                    echo -e "${GREEN}操作结束，按任意键返回${NC}"
                    read -n 1
                    ;;
                8)
                    # 删除服务
                    clear_screen
                    show_title
                    echo -e "${RED}删除服务 ${selected_pod}${NC}"
                    echo
                    
                    # 使用detect_k8s_resource.sh检测资源类型
                    RESULT=$(${SHELLS_DIR}/utils/detect_k8s_resource.sh "$selected_pod" "oa-llm")
                    DETECT_STATUS=$?
                    
                    # 检查是否安装了jq工具
                    if ! command -v jq &> /dev/null; then
                        echo -e "${RED}错误: 未找到jq工具，无法解析JSON结果${NC}"
                        echo -e "${YELLOW}请使用命令安装: apt-get install jq -y${NC}"
                        read -p "按任意键继续..." -n1
                        continue
                    fi
                    
                    # 解析JSON结果
                    FOUND_RESOURCE=$(echo $RESULT | jq -r '.found')
                    RESOURCE_TYPE=$(echo $RESULT | jq -r '.type')
                    RESOURCE_NAME=$(echo $RESULT | jq -r '.name')
                    
                    if [ "$FOUND_RESOURCE" = "true" ]; then
                        echo -e "${RED}警告: 此操作将删除整个 ${RESOURCE_TYPE} ${RESOURCE_NAME}!${NC}"
                        echo -e "${RED}所有相关的Pod和服务都将被删除!${NC}"
                        echo
                        echo -e "${YELLOW}如果您只想删除单个Pod，请使用重启服务功能${NC}"
                        echo
                        read -p "请输入服务名称 [${RESOURCE_NAME}] 确认删除: " confirm
                        
                        if [ "$confirm" = "$RESOURCE_NAME" ]; then
                            echo -e "${YELLOW}正在删除 ${RESOURCE_TYPE} ${RESOURCE_NAME}...${NC}"
                            kubectl delete ${RESOURCE_TYPE} ${RESOURCE_NAME} -n oa-llm
                            echo -e "${GREEN}服务已删除${NC}"
                        else
                            echo -e "${YELLOW}删除操作已取消${NC}"
                        fi
                    else
                        echo -e "${YELLOW}无法确定服务资源类型，只能删除单个Pod${NC}"
                        echo -e "${RED}警告: 删除Pod可能不会完全移除服务，控制器可能会重新创建Pod${NC}"
                        read -p "输入 'yes' 确认仅删除Pod ${selected_pod}: " confirm
                        
                        if [ "$confirm" = "yes" ]; then
                            kubectl delete pod ${selected_pod} -n oa-llm
                            echo -e "${GREEN}Pod已删除${NC}"
                        else
                            echo -e "${YELLOW}删除操作已取消${NC}"
                        fi
                    fi
                    
                    echo
                    echo -e "${GREEN}操作完成，按任意键返回${NC}"
                    read -n 1
                    # 返回主菜单，因为当前服务可能已经被删除
                    break
                    ;;
                9)
                    # 返回主菜单
                    break
                    ;;
                0)
                    # 退出程序
                    clear_screen
                    exit 0
                    ;;
                *)
                    echo -e "${RED}无效选择，请重试${NC}"
                    sleep 1
                    ;;
            esac
        done
    elif [ "$choice" = "0" ]; then
        return
    else
        echo -e "${RED}无效选择，请重试${NC}"
        sleep 1
        manage_specific_service
    fi
}

# 清理资源
cleanup_resources() {
    clear_screen
    show_title
    echo -e "${RED}警告: 此操作将删除Nginx代理相关资源${NC}"
    echo
    read -p "输入 'yes' 确认删除: " confirm
    
    if [ "$confirm" = "yes" ]; then
        echo -e "${YELLOW}删除Nginx代理...${NC}"
        
        # 删除服务
        kubectl delete service nginx-proxy -n oa-llm 2>/dev/null || true
        
        # 删除Deployment
        kubectl delete deployment nginx-proxy -n oa-llm 2>/dev/null || true
        
        # 删除ConfigMap
        kubectl delete configmap nginx-config -n oa-llm 2>/dev/null || true
        
        echo -e "${GREEN}清理完成${NC}"
    else
        echo -e "${YELLOW}操作已取消${NC}"
    fi
    
    echo
    echo -e "${GREEN}按任意键返回主菜单${NC}"
    read -n 1
}

# 管理调试Pod
manage_debug_pods() {
    # 调用manage_debug_pod.sh脚本进行调试Pod管理
    bash "${SHELLS_DIR}/debug/manage_debug_pod.sh" interactive "oa-llm"
}

# 显示部署子菜单
show_deploy_submenu() {
    # 检查部署子菜单脚本是否存在
    if [ -f "${SHELLS_DIR}/menu/apply_menu.sh" ]; then
        # 调用部署子菜单脚本
        bash "${SHELLS_DIR}/menu/apply_menu.sh"
    else
        clear_screen
        show_title
        echo -e "${RED}错误: 部署子菜单脚本不存在 (${SHELLS_DIR}/menu/apply_menu.sh)${NC}"
        echo -e "${YELLOW}请确保该文件存在且有执行权限${NC}"
        echo
        echo -e "${GREEN}按任意键返回主菜单${NC}"
        read -n 1
    fi
}

# 管理端口转发
manage_port_forward() {
    # 检查端口转发管理脚本是否存在
    if [ -f "${SHELLS_DIR}/menu/forward-port-manage.sh" ]; then
        # 调用端口转发管理脚本，传递K8S目录路径
        echo -e "${BLUE}启动端口转发管理工具...${NC}"
        echo -e "${BLUE}K8s根目录: ${K8S_DIR}${NC}"
        echo -e "${BLUE}脚本目录: ${SHELLS_DIR}${NC}"
        bash "${SHELLS_DIR}/menu/forward-port-manage.sh" "${K8S_DIR}"
    else
        show_title
        echo -e "${RED}错误: 端口转发管理脚本不存在 (${SHELLS_DIR}/menu/forward-port-manage.sh)${NC}"
        echo -e "${YELLOW}请确保该文件存在且有执行权限${NC}"
        echo
        echo -e "${GREEN}按任意键返回主菜单${NC}"
        read -n 1
    fi
}

# 主函数
main() {
    while true; do
        show_main_menu
        read -p "" choice
        
        case $choice in
            1)
                prepare_config
                ;;
            2)
                download_images
                ;;
            3)
                replace_configs
                ;;
            4)
                # 调用服务部署子菜单
                if [ -f "${SHELLS_DIR}/menu/apply_menu.sh" ]; then
                    bash "${SHELLS_DIR}/menu/apply_menu.sh"
                else
                    clear_screen
                    show_title
                    echo -e "${RED}错误: 部署子菜单脚本不存在 (${SHELLS_DIR}/menu/apply_menu.sh)${NC}"
                    echo -e "${YELLOW}请确保该文件存在且有执行权限${NC}"
                    echo
                    echo -e "${GREEN}按任意键返回主菜单${NC}"
                    read -n 1
                fi
                ;;
            5)
                view_all_services
                ;;
            6)
                manage_specific_service
                ;;
            7)
                show_access_guide
                ;;
            8)
                cleanup_resources
                ;;
            9)
                manage_debug_pods
                ;;
            10)
                manage_port_forward
                ;;
            0)
                echo -e "${GREEN}感谢使用，再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效的选择，请重试${NC}"
                sleep 1
                ;;
        esac
    done
}

# 启动主程序
main
