apiVersion: apps/v1
kind: Deployment
metadata:
  name: opensearch
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: opensearch
  template:
    metadata:
      labels:
        app: opensearch
    spec:
      containers:
      - name: opensearch
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/opensearch:2.9.0
        ports:
        - containerPort: 9200
        - containerPort: 9600
        env:
        - name: discovery.type
          value: "single-node"
        - name: OPENSEARCH_JVM_HEAP
          value: "2g"
        - name: OPENSEARCH_INITIAL_ADMIN_PASSWORD
          value: "QmffyH1zxSWE5Nke"
        volumeMounts:
        - name: os-data
          mountPath: /usr/share/opensearch/data
        - name: os-logs
          mountPath: /usr/share/opensearch/logs
        securityContext:
          runAsUser: 1000
          runAsGroup: 1000
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      volumes:
      - name: os-data
        persistentVolumeClaim:
          claimName: opensearch-data-pvc
      - name: os-logs
        persistentVolumeClaim:
          claimName: opensearch-logs-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: opensearch
  namespace: oa-llm
spec:
  ports:
  - port: 9200
    targetPort: 9200
    name: http
  - port: 9600
    targetPort: 9600
    name: metrics
  selector:
    app: opensearch
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: opensearch-data-pvc
  namespace: oa-llm
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: local-path
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: opensearch-logs-pvc
  namespace: oa-llm
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
  storageClassName: local-path