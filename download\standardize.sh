#!/bin/bash

set -e

# 应用名参数
APP_NAME="$1"

if [ -z "$APP_NAME" ]; then
  echo "错误: 缺少应用名参数"
  echo "用法: $0 <app_name>"
  exit 1
fi

log() {
  echo -e "\033[36m[STD:$APP_NAME]\033[0m $1"
}

log "开始标准化处理应用: $APP_NAME"

# 检查应用目录是否存在
if [ ! -d "$APP_NAME" ]; then
  log "错误: 找不到应用目录 $APP_NAME"
  exit 1
fi

# 显示应用目录结构（方便调试）
log "应用目录结构:"
find "$APP_NAME" -type d | sort | sed 's/^/  /'

# 清理日志目录
if [ -d "$APP_NAME/logs" ]; then
  log "清理日志目录 $APP_NAME/logs"
  rm -rf "$APP_NAME/logs"/*
  touch "$APP_NAME/logs/.gitkeep"
fi

if [ -d "$APP_NAME/log" ]; then
  log "清理日志目录 $APP_NAME/log"
  rm -rf "$APP_NAME/log"/*
  touch "$APP_NAME/log/.gitkeep"
fi

# 特殊处理 nginx 应用
if [ "$APP_NAME" = "nginx" ]; then
  log "处理 nginx 应用"
  
  # 确保 html 目录存在
  if [ ! -d "$APP_NAME/html" ]; then
    log "创建 nginx/html 目录"
    mkdir -p "$APP_NAME/html"
    
    # 如果 html 目录在根目录，复制其内容
    if [ -d "html" ]; then
      log "复制 html 目录内容到 nginx/html"
      cp -r html/* "$APP_NAME/html/"
    fi
  fi
  
  # 清理可能存在的压缩文件
  find "$APP_NAME/html" -name "*.zip" -delete 2>/dev/null || true
fi

# 配置参数替换
# 从JSON配置中解析，当前简化为示例处理
log "执行配置参数替换"

# 在conf目录中查找所有配置文件
find "$APP_NAME" -name "*.conf" -o -name "*.properties" -o -name "*.yml" -o -name "*.yaml" -o -name "*.json" | while read -r config_file; do
  log "处理配置文件: $config_file"
  
  # 示例替换 - 实际应用中应从配置中读取替换参数
  # sed -i 's/{{service.port}}/8080/g' "$config_file"
  # sed -i 's/{{service.host}}/localhost/g' "$config_file"
  # sed -i 's/{{db.url}}/******************************************' "$config_file"
  
  # 提示已处理完成
  log "- 完成配置替换: $config_file"
done

log "应用 $APP_NAME 标准化处理完成" 