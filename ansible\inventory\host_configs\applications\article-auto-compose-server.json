{"app_name": "article-auto-compose-server", "module_name": "ai-writer", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "0.0.1-SNAPSHOT", "image_name": "ai-writer/article-auto-compose-server:0.0.1-SNAPSHOT", "container_port": "8080", "host_port": "8094", "app_data_dir": "/app/files", "app_logs_dir": "/app/logs", "app_config_dir": "/app/config", "host_data_dir": "{{base_dir}}/article-auto-compose-server/data", "host_logs_dir": "{{base_dir}}/article-auto-compose-server/log", "host_config_dir": "{{base_dir}}/article-auto-compose-server/config", "restart_script": "{{base_dir}}/article-auto-compose-server/restart.sh", "test_script": "{{base_dir}}/article-auto-compose-server/test_curl.sh", "runtime": "java17", "env_vars": [{"name": "JAVA_OPTS", "value": "-Xms512m -Xmx1g"}], "external_dependencies": [{"type": "service", "name": "ai-writer-stream-python", "url": "http://ai-writer-stream-python:8080"}, {"type": "service", "name": "ai-writer-nostream-python", "url": "http://ai-writer-nostream-python:8080"}, {"type": "middleware", "name": "postgresql", "url": "postgresql:5432"}], "test_commands": ["curl -s http://localhost:8094/actuator/health | grep UP"]}