apiVersion: v1
kind: Service
metadata:
  labels:
    cmcc-gitops-file-name: article-auto-compose-server.yaml
    cmcc-gitops-project-tag: oallm
  name: article-auto-compose-server
  namespace: oa-llm
spec:
  ports:
  - port: 8080
    targetPort: 8080
  selector:
    app: article-auto-compose-server
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    cmcc-gitops-file-name: article-auto-compose-server.yaml
    cmcc-gitops-project-tag: oallm
  name: article-auto-compose-server
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: article-auto-compose-server
  serviceName: article-auto-compose-server
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: article-auto-compose-server
    spec:
      containers:
      - env:
        - name: spring.profiles.active
          value: prod
        - name: spring.config.location
          value: /app/config/
        image: {{article-auto-compose-server.image-url}}
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        name: article-auto-compose-server
        ports:
        - containerPort: 8080
          protocol: TCP
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 20
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 10
        resources: {}
        volumeMounts:
        - mountPath: /app/config/application-prod.yml
          name: article-auto-compose-server-config
          subPath: application-prod.yml
        - mountPath: /app/logs
          name: short-term-logs
        - mountPath: /app/shells
          name: article-auto-compose-server-shells
      - args:
        - -c
        - /opt/filebeat/filebeat.yml
        - -e
        image: {{filebeat.image-url}}
        imagePullPolicy: Always
        name: filebeat
        resources: {}
        terminationMessagePath: /var/log/err.log
        volumeMounts:
        - mountPath: /opt/filebeat/filebeat.yml
          name: article-auto-compose-server-filebeat-cm
          subPath: filebeat.yml
        - mountPath: /article-auto-compose-server/data/logs 
          name: short-term-logs
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: short-term-logs
      - configMap:
          defaultMode: 420
          name: article-auto-compose-server-config
        name: article-auto-compose-server-config
      - configMap:
          defaultMode: 420
          name: article-auto-compose-server-shells
        name: article-auto-compose-server-shells
      - configMap:
          defaultMode: 420
          name: article-auto-compose-server-filebeat-cm
        name: article-auto-compose-server-filebeat-cm
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
  updateStrategy: {}
status:
  replicas: 0
  availableReplicas: 0
  currentReplicas: 0
  updatedReplicas: 0
  currentRevision: ""
  updateRevision: ""
  collisionCount: 0
  conditions: []
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: article-auto-compose-server-config
  namespace: oa-llm
data:
  application-prod.yml: |-
    server:
      port: 8080
      servlet:
        context-path: /ai-compose-poc
      shutdown: graceful
    spring:
      application:
        name: ai-compose
      web:
        resources:
          static-locations: classpath:/
      servlet:
        multipart:
          location: /app/files
          max-request-size: 100MB #总文件大小
          max-file-size: 100MB #单个文件大小
      jackson:
        date-format: yyyy-MM-dd HH:mm:ss.SSS
        time-zone: GMT+8
        defaultPropertyInclusion: non_null # always #非空属性才序列化
        deserialization:
          fail_on_unknown_properties: false #未定义的key不序列化
    #配置文件加密
    jasypt:
      encryptor:
        algorithm: PBEWithMD5AndDES
        ivGeneratorClassname: org.jasypt.iv.NoIvGenerator
        password: EbfYkitulv73I2p0mXI50JMXoaxZTKJ7
    # 变更说明：原ai-service.providers.id 指移动分配应用ID。 现在大模型提供了uat环境，可能存在uat和生产环境APPID不一致情况。
    #  改造为： 追加mobile-app-id作为移动分配应用ID，根据需要其值可从POM文件中根据环境获取，也可以写为固定值。
    ai-service:
      providers:
        - id: glmwriter
          # 移动分配的应用Id
          mobile-app-id: ""
          name: 妙笔写作大模型
          scene: COMPOSE
          # 处理逻辑与九天大模型一致, 只是base-url, app-id, app-key不一样.
          spi-impl: mbwriterSpi
          base-url: http://ai-writer-nostream.oa-llm:8080/mubanwriter/v1/service
          app-id: 123
          app-key: 123
          ext-params:
            feedback-url:
            feedback-options-url:
    # 错误类型映射
    error-type:
      # 平能大模型与通用(浙江)错误码的映射
      balanceability: '{"检查常见字词错误": 1, "检查专有名词错误": 1, "检查标点符号错误": 2, "检查日期错误": 101, "检查数字单位类错误": 102, "检查语法错误": 106, "检查结构层次标注错误": 106, "检查引用格式错误": 109, "检查格式错误": 109}'
    moa:
      check:
        # moa端核稿使用的大模型id. 值需要为CheckServiceEnum枚举.
        checkUnitId: SQUARE
        # 核稿结果文件的有效期(天)
        attachmentExpire: 7
    logging:
      config: /app/config/logback.xml 
---
apiVersion:  v1
kind: ConfigMap
metadata:
  name: article-auto-compose-server-shells
  namespace: oa-llm
data:
  start.sh: |-
    #!/bin/bash
    # hegao服务启动脚本

    # 应用日志
    LOG_DIR="/app/logs"
    CONFIG_FILE="/app/config/config.properties"
    mkdir -p $LOG_DIR

    # 启动应用
    echo "$(date) - 启动 智能写作 服务..." >> $LOG_DIR/startup.log

    # 树状展示/app目录下所有的目录及文件
    echo "树状展示/app目录下所有的目录及文件：ls -R /app"
    tree /app


    echo "读取配置文件$CONFIG_FILE"
    echo "原始配置文件内容："
    echo "--------------------------------"
    cat $CONFIG_FILE
    echo "--------------------------------"
    # 判断配置文件是否存在，如果不存在，则不读取内容，也不在启动命令后添加启动参数
    PROPS_ARGS=""
    if [ ! -f "$CONFIG_FILE" ]; then
      echo "配置文件 $CONFIG_FILE 不存在,不读取配置文件内容"
      PROPS_ARGS=""
    else
      # 使用grep过滤注释行和空行，然后转换为启动参数
      while IFS= read -r line; do
        # 忽略空行和注释行
        if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# ]]; then
          PROPS_ARGS="$PROPS_ARGS --$line"
        fi
      done < <(grep -v "^#" $CONFIG_FILE | grep -v "^[[:space:]]*$")
    fi

    echo "转换后的启动参数： $PROPS_ARGS"
    echo "--------------------------------"
    STARTUP_CMD="java -jar \
    /app/app.jar \
    --server.port=8080 \
    --server.servlet.context-path=/ai-compose-poc \
    --spring.config.location=file:/app/config/ \
    --spring.profiles.active=prod"
    # 构建启动命令
    # 如果有配置参数，添加到命令中
    if [ ! -z "$PROPS_ARGS" ]; then
      STARTUP_CMD="$STARTUP_CMD $PROPS_ARGS"
    fi

    echo "启动命令： $STARTUP_CMD"

    # 执行启动命令
    eval $STARTUP_CMD
    # 获取启动结果
    RESULT=$?
    if [ $RESULT -eq 0 ]; then
      echo "$(date) - 智能写作 服务启动成功!" >> $LOG_DIR/startup.log
    else
      echo "$(date) - 智能写作 服务启动失败，退出码: $RESULT" >> $LOG_DIR/startup.log
    fi 
  test.sh: |-
    #!/bin/bash
    # 颜色设置
    GREEN='\033[0;32m'
    BLUE='\033[0;34m'
    RED='\033[0;31m'
    YELLOW='\033[1;33m'
    NC='\033[0m' # 无颜色

    # 测试文本
    test_text="请以《关于召开项目进度评审会议的通知》为题生成一篇通知类型的公文，子类型为会议通知。主送单位为：各部门负责人。内容包括会议时间：2024年5月15日上午10:00，会议地点：公司三楼会议室，参会人员：各部门负责人及项目组成员。会议议程：1.项目进度汇报；2.问题讨论；3.下一阶段计划制定。请各相关人员准时参加。字数要求1000字左右。"

    # ====================== 本服务接口测试 ======================
    echo -e "${BLUE}=== 本服务接口测试 ===${NC}"
    echo -e "${YELLOW}正在测试智能写作接口...${NC}"

    # 构建简单请求命令
    cmd="curl -v -X POST \"http://localhost:8080/ai-compose-poc/api/v1/compose\" \
      -H \"Content-Type: application/json\" \
      -d '{
        \"composeUnitId\": \"glmwriter\",
        \"text\": \"${test_text}\"
      }'"

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行简单请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}简单请求耗时: $((end_time - start_time)) 秒${NC}"

    # ====================== 带参数的请求测试 ======================
    echo -e "${BLUE}=== 带参数的请求测试 ===${NC}"
    cmd="curl -v -X POST \"http://localhost:8080/ai-compose-poc/api/v1/compose\" \
      -H \"Content-Type: application/json\" \
      -d '{
        \"composeUnitId\": \"glmwriter\",
        \"text\": \"请以《公司关于开展成本管理专项活动通知》为主题生成一篇通知类型的公文。字数要求100字以上\",
        \"extension\": {
          \"docInfo\": {
            \"sourceText\": \"公司关于开展成本管理专项活动通知\",
            \"universalType\": \"通知\",
            \"subTypeName\": \"活动通知\",
            \"mainDeliveryUnit\": \"各部门\"
          }
        }
      }'"

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行带参数的请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}带参数的请求耗时: $((end_time - start_time)) 秒${NC}"
    echo
    echo -e "${GREEN}=== 所有测试完成 ===${NC}"
---
apiVersion: v1
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: log
      paths:
        - /article-auto-compose-server/data/logs/*.log
      fields:
        envTag: "oa-llm"
        appTag: "article-auto-compose-server"
        namespace: oa-llm
      multiline:
        pattern: '^\d{4}-\d{2}-\d{2}'
        negate: true
        match: after
        max_lines: 500
    processors:
    - drop_fields:
        fields: ["agent","input","ecs"]
        ignore_missing: true
    output.elasticsearch:
      hosts: ["http://elasticsearch.oa-llm:9200"]
      index: "article-auto-compose-server-logs-%{+yyyy.MM.dd}"
    setup.template.name: "article-auto-compose-server-logs"
    setup.template.pattern: "article-auto-compose-server-logs*"
    setup.ilm.enabled: false  # 禁用 ILM，避免自动 rollover 到 filebeat-*
kind: ConfigMap
metadata:
  labels:
    cmcc-gitops-project-tag: oa-llm 
  name: article-auto-compose-server-filebeat-cm
  namespace: oa-llm