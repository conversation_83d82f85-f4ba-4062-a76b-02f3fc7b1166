-- 删除已存在的表
DROP TABLE IF EXISTS knowledge_base;
DROP TABLE IF EXISTS document;
DROP TABLE IF EXISTS document_chunk;

-- 存储知识库的基本信息，包括名称、ID、分组等
CREATE TABLE knowledge_base (
    id SERIAL PRIMARY KEY,                                   -- 自增主键，用于唯一标识知识库
    name VARCHAR(255) NOT NULL,                              -- 知识库名称，用于展示和搜索
    knowledge_base_id VARCHAR(255) NOT NULL UNIQUE,          -- 知识库唯一标识，用于关联其他表
    system_id VARCHAR(20) NOT NULL,                          -- 系统标识，用于区分不同系统
    app_id VARCHAR(20) NOT NULL,                             -- 应用ID，用于区分不同应用
    group_id VARCHAR(50) NOT NULL,                           -- 分组ID，用于分类和管理
    project_id VARCHAR(50) NOT NULL,                         -- 项目ID，用于关联项目信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,          -- 创建时间，用于记录创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP           -- 更新时间，用于记录更新时间
);

-- 存储文档的基本信息，包括文档ID、知识库ID、文件名、文件路径、文件大小、处理状态和批次ID等
CREATE TABLE document (
    id SERIAL PRIMARY KEY,                                  -- 主键ID，自增，用于唯一标识文档
    knowledge_base_id VARCHAR(255) NOT NULL,                -- 关联知识库ID，用于关联知识库信息
    document_id VARCHAR(255) NOT NULL UNIQUE,               -- 文档唯一标识，用于业务层面识别文档
    batch_id VARCHAR(255),                                  -- 批次ID，用于批量处理和管理
    file_name VARCHAR(255) NOT NULL,                        -- 上传的原始文件名，用于展示和搜索
    file_path VARCHAR(255) NOT NULL,                        -- 文件存储的实际路径，用于存储和读取
    file_size BIGINT DEFAULT 0,                             -- 文件大小，用于统计和限制
    process_state SMALLINT DEFAULT 1,                       -- 处理状态: 1-待处理 2-处理中 3-处理成功 4-处理失败 5-已删除
    file_type SMALLINT DEFAULT 0,                           -- 文档类型: 0-普通文档 1-问答对文档
    error_msg VARCHAR(512),                                 -- 记录处理失败的具体原因，便于排查和调试
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,         -- 创建时间，用于记录创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP          -- 更新时间，自动更新
);

-- 存储文档的分块信息，包括文档ID、批次ID、分块索引、分块内容等
CREATE TABLE document_chunk (
    id SERIAL PRIMARY KEY,                                    -- 主键ID，自增，用于唯一标识分块
    document_id VARCHAR(255) NOT NULL,                        -- 关联文档ID，用于关联文档信息
    chunk_index INTEGER NOT NULL,                             -- 分块索引，用于标识分块顺序
    chunk_content TEXT NOT NULL,                              -- 分块内容，用于存储和读取
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,          -- 创建时间，用于记录创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP           -- 更新时间，自动更新
);