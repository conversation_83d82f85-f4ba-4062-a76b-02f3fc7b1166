{"app_name": "rag-qa-online", "module_name": "znwd", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "v2", "image_name": "znwd/rag-qa-online:v2", "container_port": "8080", "host_port": "8085", "app_data_dir": "/home/<USER>/data", "app_logs_dir": "/home/<USER>/log", "app_config_dir_1": "/home/<USER>/config/services_test.json", "app_config_dir_2": "/home/<USER>/configs/models.yaml", "host_data_dir": "{{base_dir}}/znwd/rag-qa-online/data", "host_logs_dir": "{{base_dir}}/znwd/logs", "host_config_dir_1": "{{base_dir}}/znwd/rag-qa-online/config/services.json", "host_config_dir_2": "{{base_dir}}/znwd/rag-qa-online/config/models.yaml", "restart_script": "{{base_dir}}/znwd/rag-qa-online/restart.sh", "test_script": "{{base_dir}}/znwd/rag-qa-online/test_curl.sh", "runtime": "python", "env_vars": [{"name": "PYTHONPATH", "value": "/home/<USER>"}, {"name": "TZ", "value": "Asia/Shanghai"}], "external_dependencies": [{"type": "service", "name": "query-parser", "url": "http://query-parser:7890"}, {"type": "service", "name": "embedding-service-bge-m3", "url": "http://embedding-service-bge-m3:8080"}, {"type": "service", "name": "bge-reranker-v2-m3", "url": "http://bge-reranker-v2-m3:8080"}, {"type": "middleware", "name": "elasticsearch", "url": "elasticsearch:9200"}], "test_commands": ["curl -s http://localhost:8085/health | grep OK"]}