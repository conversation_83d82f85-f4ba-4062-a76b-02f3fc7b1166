docker run -it --rm --net=jiutianwensi-network officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14 bash

docker run -itd --name=nginx-test1 -p 8887:80 --net=jiutianwensi-network m.daocloud.io/docker.io/nginx:1.27.4 

docker run -itd --name=nginx-test2 -p 8888:80 --net=jiutianwensi-network m.daocloud.io/docker.io/nginx:1.27.4 

docker run -itd --name=nginx-test3 --net=jiutianwensi-network m.daocloud.io/docker.io/nginx:1.27.4 


curl -v -X POST "http://localhost:1025/v1/chat/completions"       -H "Content-Type: application/json"       -H "Authorization: Bearer "       -d '{
      "model": "DeepSeek-R1-Distill-Llama-70B",
      "messages": [
        {
          "role": "system",
          "content": "你是一个专业的文本纠错助手。请对输入的文本进行纠错，确保输出符合以下要求：

任务说明：
你需要对输入的文本进行纠错，主要处理以下几类错误：

1. 词语错别字
    - 修正词语中的错别字
    - 示例：[我觉得很星福。] → [我觉得很幸福。]

2. 拼写错误
    - 将拼音转换为正确的汉字
    - 示例：[你hao！] → [你好！]
    - 示例：[我们需要yuan助。] → [我们需要援助。]

3. 多余字
    - 删除文本中多余的字
    - 示例：[今天真开的心。] → [今天真开心。]

4. 缺失字
    - 补充文本中缺失的字
    - 示例：[请各单组织学习青年大学习] → [请各单位组织学习青年大学习]

5. 标点符号错误
    - 修正错误的标点符号
    - 示例：[今天天气真好.] → [今天天气真好。]

6. 语法错误
    - 搭配不当：修正动词宾语、主语宾语、修饰语、主语谓语搭配错误
    - 语序问题：修正动作逻辑顺序、时间状语位置、修饰语位置、关联词位置错误
    - 语义矛盾：修正语义不一致的内容
    - 副词冗余：删除多余的副词
    - 成分缺失：补充缺失的句子成分

输出要求：
1. 如果文本存在错误，请进行纠错并以JSON格式输出：
    {\"output\": \"纠错后的文本\"}

2. 如果文本没有错误，请直接以JSON格式输出原文：
    {\"output\": \"原文\"}

3. 只输出JSON格式的结果，不要添加任何其他信息。"
        },
        {
          "role": "user",
          "content": ""
        }
      ]
    }'

docker-compose -f docker-compose.yml up -d poc-intelligence-view


docker run -itd --name=poc-intelligence-view-write -p 20002:20002 \
-v /data/jtws-host/poc-intelligence-view-write/config/nginx.conf:/etc/nginx/nginx.conf \
 --net=jiutianwensi-network officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
