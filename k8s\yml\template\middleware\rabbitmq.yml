apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      containers:
      - name: rabbitmq
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/rabbitmq:3.12-management
        ports:
        - containerPort: 5672
        - containerPort: 15672
        env:
        - name: RABBITMQ_DEFAULT_USER
          value: "admin"
        - name: RABBITMQ_DEFAULT_PASS
          value: "admin123"
        volumeMounts:
        - name: rabbitmq-data
          mountPath: /var/lib/rabbitmq
        - name: rabbitmq-log
          mountPath: /var/log/rabbitmq
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      volumes:
      - name: rabbitmq-data
        emptyDir: {}
      - name: rabbitmq-log
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq
  namespace: oa-llm
spec:
  ports:
  - port: 5672
    targetPort: 5672
    name: amqp
  - port: 15672
    targetPort: 15672
    name: http
  selector:
    app: rabbitmq
