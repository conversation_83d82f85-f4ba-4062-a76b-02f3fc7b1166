#!/bin/bash
IMAGE_REPO="officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn"
# 在本地构建镜像，并将镜像推送到私有仓库
#IMAGE_REPO="cis-hub-beijing-zs1.cmecloud.cn"
#IMAGE_REPO="10.1.4.121"
#NAMESPACE="jiutianwensi"
NAMESPACE="jtws"
#HARBOR_USERNAME="admin"
#HARBOR_PASSWORD="Harbor12345"
HARBOR_USERNAME="zglyjt"
HARBOR_PASSWORD="Znwd0415@"
# APP_NAME 是当前目录的名称
APP_NAME=$1
MODUL_NAME=$2
BUILD_NUMBER=$3
 # docker tag
echo "docker tag ${MODUL_NAME}/${APP_NAME}:${BUILD_NUMBER} ${IMAGE_REPO}/${NAMESPACE}/${APP_NAME}:${BUILD_NUMBER}"
docker tag ${MODUL_NAME}/${APP_NAME}:${BUILD_NUMBER} ${IMAGE_REPO}/${NAMESPACE}/${APP_NAME}:${BUILD_NUMBER}

echo "登录镜像仓库:docker login --username=$HARBOR_USERNAME -p$HARBOR_PASSWORD $IMAGE_REPO"
docker login --username=$HARBOR_USERNAME -p$HARBOR_PASSWORD $IMAGE_REPO
   
# docker push
echo "docker push ${IMAGE_REPO}/${NAMESPACE}/${APP_NAME}:${BUILD_NUMBER}"
docker push ${IMAGE_REPO}/${NAMESPACE}/${APP_NAME}:${BUILD_NUMBER}
