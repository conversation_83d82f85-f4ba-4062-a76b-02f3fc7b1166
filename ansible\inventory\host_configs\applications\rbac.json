{"app_name": "rbac", "module_name": "znwd", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "1.0-SNAPSHOT", "image_name": "znwd/rbac:1.0-SNAPSHOT", "container_port": "8080", "host_port": "8081", "app_data_dir": "/app/files", "app_logs_dir": "/app/logs", "app_config_dir": "/app/config", "host_data_dir": "{{base_dir}}/znwd/rbac/data", "host_logs_dir": "{{base_dir}}/znwd/rbac/log", "host_config_dir": "{{base_dir}}/znwd/rbac/config", "restart_script": "{{base_dir}}/znwd/rbac/restart.sh", "test_script": "{{base_dir}}/znwd/rbac/test_curl.sh", "runtime": "java8", "env_vars": [{"name": "JAVA_OPTS", "value": "-Xms512m -Xmx1g"}], "external_dependencies": [{"type": "middleware", "name": "redis", "url": "redis:6379"}, {"type": "middleware", "name": "postgresql", "url": "postgresql:5432"}], "test_commands": ["curl -s http://localhost:8081/actuator/health | grep UP"]}