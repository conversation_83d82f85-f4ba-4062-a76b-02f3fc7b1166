#!/bin/bash

# 当前目录
CURRENT_DIR=$(cd $(dirname $0); pwd)



# 停止并删除已存在的 filebeat 容器
docker stop filebeat-deepseek70b 2>/dev/null
docker rm filebeat-deepseek70b 2>/dev/null

# 启动 filebeat 容器
docker run -d \
  --name filebeat-deepseek70b \
  --restart unless-stopped \
  --network jiutianwensi-network \
  --user root \
  -v "$CURRENT_DIR/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro" \
  -v "./deepseek70b/init-logs:/data/deepseek70b/init-logs" \
  officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14 \
  filebeat -e -strict.perms=false

# 检查容器是否成功启动
if [ $? -eq 0 ]; then
    echo "Filebeat 容器启动成功"
    echo "容器名称: filebeat-deepseek70b"
    echo "日志目录: /data/deepseek70b/init-logs"
    echo "配置文件: $CURRENT_DIR/filebeat.yml"
else
    echo "Filebeat 容器启动失败"
    exit 1
fi 