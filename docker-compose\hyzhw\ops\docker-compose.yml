version: '3.5'

services:
  elasticsearch:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=elastic@123
      - bootstrap.memory_lock=true
    volumes:
      - ./elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - ./elasticsearch/data:/usr/share/elasticsearch/data
      - ./elasticsearch/logs:/usr/share/elasticsearch/logs
    ports:
      - 9200:9200
      - 9300:9300
    networks:
      - jiutianwensi-network
    restart: always
  kibana:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14
    container_name: kibana
    ports:
      - "20001:5601"
    environment:
      - TZ=Asia/Shanghai
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD=elastic@123
    volumes:
      - ./kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml
      - ./kibana/data:/usr/share/kibana/data
    networks:
      - jiutianwensi-network
    restart: always
    depends_on:
      - elasticsearch
  nginx-common:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4
    container_name: nginx-common
    ports:
      - "20004:80"
    volumes:
      - ./nginx-common/config/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx-common/shells/test.txt:/test.txt
      - ./nginx-common/shells/test.sh:/test.sh
      - ./nginx-common/logs:/var/log/nginx
      - ./nginx-common/html:/usr/share/nginx/html
    environment:
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always      
networks:
  jiutianwensi-network:
    name: jiutianwensi-network