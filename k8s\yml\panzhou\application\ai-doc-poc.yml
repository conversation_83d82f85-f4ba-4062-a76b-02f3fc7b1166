apiVersion: v1
kind: Service
metadata:
  labels:
    cmcc-gitops-file-name: ai-doc-poc.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-doc-poc
  namespace: oa-llm
spec:
  ports:
  - port: 8080
    targetPort: 8080
  selector:
    app: ai-doc-poc
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    cmcc-gitops-file-name: ai-doc-poc.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-doc-poc
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-doc-poc
  serviceName: ai-doc-poc
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: ai-doc-poc
    spec:
      containers:
      - env:
        - name: spring.profiles.active
          value: prod
        - name: spring.config.location
          value: /app/config/
        image: artifactory.dep.devops.cmit.cloud:20101/native_common/ai-doc-poc:x86
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        name: ai-doc-poc
        ports:
        - containerPort: 8080
          protocol: TCP
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 20
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 10
        resources: {}
        volumeMounts:
        - mountPath: /app/config/application-prod.yml
          name: ai-doc-poc-config
          subPath: application-prod.yml
        - mountPath: /app/shells
          name: ai-doc-poc-shells
        - mountPath: /app/logs
          name: short-term-logs
      - args:
        - -c
        - /opt/filebeat/filebeat.yml
        - -e
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/filebeat:7.17.14
        imagePullPolicy: Always
        name: filebeat
        resources: {}
        terminationMessagePath: /var/log/err.log
        volumeMounts:
        - mountPath: /opt/filebeat/filebeat.yml
          name: ai-doc-poc-filebeat-cm
          subPath: filebeat.yml
        - mountPath: /ai-doc-poc/data/logs 
          name: short-term-logs
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: short-term-logs
      - configMap:
          defaultMode: 420
          name: ai-doc-poc-config
        name: ai-doc-poc-config
      - configMap:
          defaultMode: 420
          name: ai-doc-poc-filebeat-cm
        name: ai-doc-poc-filebeat-cm
      - configMap:
          defaultMode: 420
          name: ai-doc-poc-shells
        name: ai-doc-poc-shells
  updateStrategy: {}
status:
  replicas: 0
---
apiVersion: v1
data:
  application-prod.yml: |-
    server:
      port: 8080
      servlet:
        context-path: /ai-doc-poc
      shutdown: graceful
    spring:
      application:
        name: ai-doc
      web:
        resources:
          static-locations: classpath:/
      servlet:
        multipart:
          location: /app/files
          max-request-size: 100MB #总文件大小
          max-file-size: 100MB #单个文件大小
      jackson:
        date-format: yyyy-MM-dd HH:mm:ss.SSS
        time-zone: GMT+8
        defaultPropertyInclusion: non_null # always #非空属性才序列化
        deserialization:
          fail_on_unknown_properties: false #未定义的key不序列化
    #配置文件加密
    jasypt:
      encryptor:
        algorithm: PBEWithMD5AndDES
        ivGeneratorClassname: org.jasypt.iv.NoIvGenerator
        password: EbfYkitulv73I2p0mXI50JMXoaxZTKJ7
    # 变更说明：原ai-service.providers.id 指移动分配应用ID。 现在大模型提供了uat环境，可能存在uat和生产环境APPID不一致情况。
    #  改造为： 追加mobile-app-id作为移动分配应用ID，根据需要其值可从POM文件中根据环境获取，也可以写为固定值。
    ai-service:
      providers:
        - id: square # 功能需要见CheckServiceEnum
          # 移动分配的应用Id
          mobile-app-id: square
          name: 浙江核稿大模型
          scene: REVIEW
          spi-impl: squareSpi
          base-url: http://ai-redactor.oa-llm:8080/spellcheck/oa
          app-id: ENC(qLwxCd+kGLASvxFMi2EonA==)
          app-key: ENC(+svIC/lQsgLR+UqbodRkzBpRUlF9/FLw1MK2rRrlRwnEdTc2W7f1OFvYK+Jg//gMwr8gRey2fvcVaLjA5snmIJtW0S54AN7kd4+AxQbo8ce0YcMaa7AIoflf+WUIBTrr176pCfsKSiaMi3lNCw2D5zJF2zcfOMosqS/SKDEpctlb0x5d5du2w9faAyIMYfp79NdU14fzryakfNuz6XmWTyKq/to2yc+Fk5aMSnzV8g5HfdsiZBPjqn71FSJNUWzxcc6ejch4Vm2NhYoQmkEJCgojE0+1fGk20gmZPGpEwQp04oQnX5blc3J+yFhgajxeHB9ejjw5DomiGN9cIgaAsGHlSNe1IIDBbnpAeOYKmHnp1PoNCP+LBe5xXQ7RMmCyJklx04d6SvadYWIj/1CqSsI1lTriiIFgY5rv4r75IFeYpgMU8xKUfM7xenPDFUzypLywIFb/EGey/uKGKuMZayJphrUav906+Hkb75Cty4p144PsUI6fV21/yCNXp7WV4KN9dLG/SilNxL4wG94Pk8YiG+hHKV8hpmuvW6Im6hBi+vM8I9abeJLxvb9jGDTVkFhPIyKv1lI7LbnUYksxvvmgr3zlBqgFnYTax8hNiVU1DyX9h1vwKeDUeT9bPqyWP0H28l6EUjikz72aqJx158KMggL97U016t+W1l/45chsJIhi+d4OmXCrnm2YFG6SekVE6N2AkF1ddIqclePzqfd1xSOBxPdzXH223+fCtO/9GX/WAq3pxRH0uKgwXQXlU+HCvf4uVAN9gE5oOn0tWtCwY9K7XTD4ZUpAYK7Y6/kl0ibyGAN8+GqBAgOLr2JQnetPvufCFJNQF/zW7A204L0ssLptEwkrywqLlkueykpCct/MGxMzXA91BYj7InyVe/x+3nj+Gfmg45IgF2/80//igN3oWkeA3wqjbyKxc0q1xw8ODtbryoa0bPMSvE9DDMCcJH2o90m30tbdyqKcROX5I/itXqLI)
          ext-params:
            record-distribute-url: ENC(F8/RkAwu8xjc/ScQ1cmFB/cJKuNaQ4MSHY2CtoqgYI5rUCYiI690xhmb2DflNBwA)
            acquire-token-sys-code: ENC(PQt66HNANhdsyKa8snj3Vg==)
            acquire-token-url: ENC(hRUBOcjLSMUDjdZ1nOgu3vTdhATbjx5PiWnqUdzZd4FTpwrx6K0hxguuJ8rypgZb4fXmW6LZzPC9CdoivtYjJ24eqU3aNfT4xJ1W/0y5jB/QWwcYoJd7/qrcmH4V9/Mh)
        - id: pncheck #功能需要自定义了该值 见CheckServiceEnum
          # 平能大模型无移动分配ID
          mobile-app-id: ""
          name: 平能核稿大模型
          scene: REVIEW
          spi-impl: balanceAbilitySpi
          base-url: http://ai-redactor.oa-llm:8080/spellcheck/oa
          app-id: llmz-zhl
          app-key: 551a68b15b753d4bd0789f1527cc64b7
          ext-params:
            record-distribute-url: ""
    # 错误类型映射
    error-type:
      # 平能大模型与通用(浙江)错误码的映射
      balanceability: '{"检查常见字词错误": 1, "检查专有名词错误": 1, "检查标点符号错误": 2, "检查日期错误": 101, "检查数字单位类错误": 102, "检查语法错误": 106, "检查结构层次标注错误": 106, "检查引用格式错误": 109, "检查格式错误": 109}'
    moa:
      check:
        # moa端核稿使用的大模型id. 值需要为CheckServiceEnum枚举.
        checkUnitId: SQUARE
    logging:
      config: /app/config/logback.xml
kind: ConfigMap
metadata:
  name: ai-doc-poc-config
  namespace: oa-llm
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-doc-poc-shells
  namespace: oa-llm
data:
  start.sh: |-
    #!/bin/bash
    # ai-doc-poc服务启动脚本

    # 应用日志
    LOG_DIR="/app/logs"
    CONFIG_FILE="/app/config/config.properties"
    mkdir -p $LOG_DIR

    # 启动应用
    echo "$(date) - 启动 hegao 服务..." >> $LOG_DIR/startup.log

    # 树状展示/app目录下所有的目录及文件
    echo "树状展示/app目录下所有的目录及文件：ls -R /app"
    ls -R /app


    echo "读取配置文件$CONFIG_FILE"
    echo "原始配置文件内容："
    echo "--------------------------------"
    cat $CONFIG_FILE
    echo "--------------------------------"
    # 判断配置文件是否存在，如果不存在，则不读取内容，也不在启动命令后添加启动参数
    PROPS_ARGS=""
    if [ ! -f "$CONFIG_FILE" ]; then
      echo "配置文件 $CONFIG_FILE 不存在,不读取配置文件内容"
      PROPS_ARGS=""
    else
      # 使用grep过滤注释行和空行，然后转换为启动参数
      while IFS= read -r line; do
        # 忽略空行和注释行
        if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# ]]; then
          PROPS_ARGS="$PROPS_ARGS --$line"
        fi
      done < <(grep -v "^#" $CONFIG_FILE | grep -v "^[[:space:]]*$")
    fi

    echo "转换后的启动参数： $PROPS_ARGS"
    echo "--------------------------------"
    STARTUP_CMD="java -jar \
    /app/app.jar \
    --server.port=8080 \
    --server.servlet.context-path=/ai-doc-poc \
    --spring.config.location=file:/app/config/ \
    --spring.profiles.active=prod"
    # 构建启动命令
    # 如果有配置参数，添加到命令中
    if [ ! -z "$PROPS_ARGS" ]; then
      STARTUP_CMD="$STARTUP_CMD $PROPS_ARGS"
    fi

    echo "启动命令： $STARTUP_CMD"

    # 执行启动命令
    eval $STARTUP_CMD

    # 获取启动结果
    RESULT=$?
    if [ $RESULT -eq 0 ]; then
      echo "$(date) - hegao 服务启动成功!" >> $LOG_DIR/startup.log
    else
      echo "$(date) - hegao 服务启动失败，退出码: $RESULT" >> $LOG_DIR/startup.log
    fi 
     echo "进行接口文档测试" 
    sh /app/shells/test.sh
  test.sh: |-
    # ====================== 文档检查API测试 ======================
    echo -e "${BLUE}=== 文档检查API测试 ===${NC}"
    echo -e "${YELLOW}正在测试文档检查接口...${NC}"

    # 显示将要执行的命令
    echo 'curl -X POST \'
    echo '  -F "file=@/app/shells/test.txt;type=text/plain" \'
    echo '  -F "accessMode=STANDALONE" \'
    echo '  -F "checkUnitId=BALANCEABILITY" \'
    echo '  -F "fileType=HTML" \'
    echo '  -F "extension={\"title\":\"请在此输入标题\",\"mainSubmit\":[],\"copySubmit\":[]}" \'
    echo '  http://localhost:8080/ai-doc-poc/api/v1/check'
    echo

    # 测试文档为test.txt，请确保该文件存在
    curl -X POST \
      -F "file=@/app/shells/test.txt;type=text/plain" \
      -F "accessMode=STANDALONE" \
      -F "checkUnitId=BALANCEABILITY" \
      -F "fileType=HTML" \
      -F "extension={\"title\":\"请在此输入标题\",\"mainSubmit\":[],\"copySubmit\":[]}" \
      http://localhost:8080/ai-doc-poc/api/v1/check

    echo
    echo -e "${GREEN}=== 文档检查API测试结束 ===${NC}"
  test.txt: |-
    请输入你的测试文本
---
apiVersion: v1
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: log
      paths:
        - /ai-doc-poc/data/logs/*.log
      fields:
        envTag: "oa-llm"
        appTag: "ai-doc-poc"
        namespace: oa-llm
      multiline:
        pattern: '^\d{4}-\d{2}-\d{2}'
        negate: true
        match: after
        max_lines: 500
    processors:
    - drop_fields:
        fields: ["agent","input","ecs"]
        ignore_missing: true
    output.elasticsearch:
      hosts: ["http://elasticsearch.oa-llm:9200"]
      index: "ai-doc-poc-logs-%{+yyyy.MM.dd}"
    setup.template.name: "ai-doc-poc-logs"
    setup.template.pattern: "ai-doc-poc-logs*"
    setup.ilm.enabled: false  # 禁用 ILM，避免自动 rollover 到 filebeat-*
kind: ConfigMap
metadata:
  labels:
    cmcc-gitops-project-tag: oa-llm 
  name: ai-doc-poc-filebeat-cm
  namespace: oa-llm