apiVersion: v1
kind: Service
metadata:
  labels:
    cmcc-gitops-file-name: ai-doc-poc.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-doc-poc
  namespace: oa-llm
spec:
  ports:
  - port: 8080
    targetPort: 8080
  selector:
    app: ai-doc-poc
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    cmcc-gitops-file-name: ai-doc-poc.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-doc-poc
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-doc-poc
  serviceName: ai-doc-poc
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: ai-doc-poc
    spec:
      containers:
      - env:
        - name: spring.profiles.active
          value: prod
        - name: spring.config.location
          value: /app/config/
        image: {{ai-doc-poc.image-url}}
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        name: ai-doc-poc
        ports:
        - containerPort: 8080
          protocol: TCP
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 20
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 10
        resources: {}
        volumeMounts:
        - mountPath: /app/config/application-prod.yml
          name: ai-doc-poc-config
          subPath: application-prod.yml
        - mountPath: /app/shells
          name: ai-doc-poc-shells
        - mountPath: /app/logs
          name: short-term-logs      
      - args:
        - -c
        - /opt/filebeat/filebeat.yml
        - -e
        image: {{filebeat.image-url}}
        imagePullPolicy: Always
        name: filebeat
        resources: {}
        terminationMessagePath: /var/log/err.log
        volumeMounts:
        - mountPath: /opt/filebeat/filebeat.yml
          name: ai-doc-poc-filebeat-cm
          subPath: filebeat.yml
        - mountPath: /ai-doc-poc/data/logs 
          name: short-term-logs
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: short-term-logs
      - configMap:
          defaultMode: 420
          name: ai-doc-poc-config
        name: ai-doc-poc-config
      - configMap:
          defaultMode: 420
          name: ai-doc-poc-filebeat-cm
        name: ai-doc-poc-filebeat-cm
      - configMap:
          defaultMode: 420
          name: ai-doc-poc-shells
        name: ai-doc-poc-shells
      imagePullSecrets:
      - name: oa-llm-imagepullsecret          
  updateStrategy: {}
status:
  replicas: 0
  availableReplicas: 0
  currentReplicas: 0
  updatedReplicas: 0
  currentRevision: ""
  updateRevision: ""
  collisionCount: 0
  conditions: []
---
apiVersion: v1
data:
  application-prod.yml: |-
    server:
      port: 8080
      servlet:
        context-path: /ai-doc-poc
      shutdown: graceful
    spring:
      application:
        name: ai-doc
      web:
        resources:
          static-locations: classpath:/
      servlet:
        multipart:
          location: /app/files
          max-request-size: 100MB #总文件大小
          max-file-size: 100MB #单个文件大小
      jackson:
        date-format: yyyy-MM-dd HH:mm:ss.SSS
        time-zone: GMT+8
        defaultPropertyInclusion: non_null # always #非空属性才序列化
        deserialization:
          fail_on_unknown_properties: false #未定义的key不序列化
    #配置文件加密
    jasypt:
      encryptor:
        algorithm: PBEWithMD5AndDES
        ivGeneratorClassname: org.jasypt.iv.NoIvGenerator
        password: EbfYkitulv73I2p0mXI50JMXoaxZTKJ7
    # 变更说明：原ai-service.providers.id 指移动分配应用ID。 现在大模型提供了uat环境，可能存在uat和生产环境APPID不一致情况。
    #  改造为： 追加mobile-app-id作为移动分配应用ID，根据需要其值可从POM文件中根据环境获取，也可以写为固定值。
    ai-service:
      providers:
        - id: square # 功能需要见CheckServiceEnum
          # 移动分配的应用Id
          mobile-app-id: square
          name: 浙江核稿大模型
          scene: REVIEW
          spi-impl: squareSpi
          base-url: http://ai-redactor.oa-llm:8080/spellcheck/oa
          app-id: ENC(qLwxCd+kGLASvxFMi2EonA==)
          app-key: ENC(+svIC/lQsgLR+UqbodRkzBpRUlF9/FLw1MK2rRrlRwnEdTc2W7f1OFvYK+Jg//gMwr8gRey2fvcVaLjA5snmIJtW0S54AN7kd4+AxQbo8ce0YcMaa7AIoflf+WUIBTrr176pCfsKSiaMi3lNCw2D5zJF2zcfOMosqS/SKDEpctlb0x5d5du2w9faAyIMYfp79NdU14fzryakfNuz6XmWTyKq/to2yc+Fk5aMSnzV8g5HfdsiZBPjqn71FSJNUWzxcc6ejch4Vm2NhYoQmkEJCgojE0+1fGk20gmZPGpEwQp04oQnX5blc3J+yFhgajxeHB9ejjw5DomiGN9cIgaAsGHlSNe1IIDBbnpAeOYKmHnp1PoNCP+LBe5xXQ7RMmCyJklx04d6SvadYWIj/1CqSsI1lTriiIFgY5rv4r75IFeYpgMU8xKUfM7xenPDFUzypLywIFb/EGey/uKGKuMZayJphrUav906+Hkb75Cty4p144PsUI6fV21/yCNXp7WV4KN9dLG/SilNxL4wG94Pk8YiG+hHKV8hpmuvW6Im6hBi+vM8I9abeJLxvb9jGDTVkFhPIyKv1lI7LbnUYksxvvmgr3zlBqgFnYTax8hNiVU1DyX9h1vwKeDUeT9bPqyWP0H28l6EUjikz72aqJx158KMggL97U016t+W1l/45chsJIhi+d4OmXCrnm2YFG6SekVE6N2AkF1ddIqclePzqfd1xSOBxPdzXH223+fCtO/9GX/WAq3pxRH0uKgwXQXlU+HCvf4uVAN9gE5oOn0tWtCwY9K7XTD4ZUpAYK7Y6/kl0ibyGAN8+GqBAgOLr2JQnetPvufCFJNQF/zW7A204L0ssLptEwkrywqLlkueykpCct/MGxMzXA91BYj7InyVe/x+3nj+Gfmg45IgF2/80//igN3oWkeA3wqjbyKxc0q1xw8ODtbryoa0bPMSvE9DDMCcJH2o90m30tbdyqKcROX5I/itXqLI)
          ext-params:
            record-distribute-url: ENC(F8/RkAwu8xjc/ScQ1cmFB/cJKuNaQ4MSHY2CtoqgYI5rUCYiI690xhmb2DflNBwA)
            acquire-token-sys-code: ENC(PQt66HNANhdsyKa8snj3Vg==)
            acquire-token-url: ENC(hRUBOcjLSMUDjdZ1nOgu3vTdhATbjx5PiWnqUdzZd4FTpwrx6K0hxguuJ8rypgZb4fXmW6LZzPC9CdoivtYjJ24eqU3aNfT4xJ1W/0y5jB/QWwcYoJd7/qrcmH4V9/Mh)
        - id: pncheck #功能需要自定义了该值 见CheckServiceEnum
          # 平能大模型无移动分配ID
          mobile-app-id: ""
          name: 平能核稿大模型
          scene: REVIEW
          spi-impl: balanceAbilitySpi
          base-url: http://ai-redactor.oa-llm:8000/spellcheck/oa
          app-id: llmz-zhl
          app-key: 551a68b15b753d4bd0789f1527cc64b7
          ext-params:
            record-distribute-url: ""
    # 错误类型映射
    error-type:
      # 平能大模型与通用(浙江)错误码的映射
      balanceability: '{"检查常见字词错误": 1, "检查专有名词错误": 1, "检查标点符号错误": 2, "检查日期错误": 101, "检查数字单位类错误": 102, "检查语法错误": 106, "检查结构层次标注错误": 106, "检查引用格式错误": 109, "检查格式错误": 109}'
    moa:
      check:
        # moa端核稿使用的大模型id. 值需要为CheckServiceEnum枚举.
        checkUnitId: SQUARE
    logging:
      config: /app/config/logback.xml
kind: ConfigMap
metadata:
  name: ai-doc-poc-config
  namespace: oa-llm
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-doc-poc-shells
  namespace: oa-llm
data:
  start.sh: |-
    #!/bin/bash
    # ai-doc-poc服务启动脚本

    # 应用日志
    LOG_DIR="/app/logs"
    CONFIG_FILE="/app/config/config.properties"
    mkdir -p $LOG_DIR

    # 启动应用
    echo "$(date) - 启动 hegao 服务..." >> $LOG_DIR/startup.log

    # 树状展示/app目录下所有的目录及文件
    echo "树状展示/app目录下所有的目录及文件：ls -R /app"
    ls -R /app


    echo "读取配置文件$CONFIG_FILE"
    echo "原始配置文件内容："
    echo "--------------------------------"
    cat $CONFIG_FILE
    echo "--------------------------------"
    # 判断配置文件是否存在，如果不存在，则不读取内容，也不在启动命令后添加启动参数
    PROPS_ARGS=""
    if [ ! -f "$CONFIG_FILE" ]; then
      echo "配置文件 $CONFIG_FILE 不存在,不读取配置文件内容"
      PROPS_ARGS=""
    else
      # 使用grep过滤注释行和空行，然后转换为启动参数
      while IFS= read -r line; do
        # 忽略空行和注释行
        if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# ]]; then
          PROPS_ARGS="$PROPS_ARGS --$line"
        fi
      done < <(grep -v "^#" $CONFIG_FILE | grep -v "^[[:space:]]*$")
    fi

    echo "转换后的启动参数： $PROPS_ARGS"
    echo "--------------------------------"
    STARTUP_CMD="java -jar \
    /app/app.jar \
    --server.port=8080 \
    --server.servlet.context-path=/ai-doc-poc \
    --spring.config.location=file:/app/config/ \
    --spring.profiles.active=prod"
    # 构建启动命令
    # 如果有配置参数，添加到命令中
    if [ ! -z "$PROPS_ARGS" ]; then
      STARTUP_CMD="$STARTUP_CMD $PROPS_ARGS"
    fi

    echo "启动命令： $STARTUP_CMD"

    # 执行启动命令
    eval $STARTUP_CMD

    # 获取启动结果
    RESULT=$?
    if [ $RESULT -eq 0 ]; then
      echo "$(date) - hegao 服务启动成功!" >> $LOG_DIR/startup.log
    else
      echo "$(date) - hegao 服务启动失败，退出码: $RESULT" >> $LOG_DIR/startup.log
    fi 
     echo "进行接口文档测试" 
    sh /app/shells/test.sh
  test.sh: |-
    #!/bin/bash
    # 颜色设置
    GREEN='\033[0;32m'
    BLUE='\033[0;34m'
    RED='\033[0;31m'
    YELLOW='\033[1;33m'
    NC='\033[0m' # 无颜色
    # ====================== 直连服务测试 ======================
    echo -e "${BLUE}=== 直连服务测试 ===${NC}"
    echo -e "${YELLOW}正在测试直连文档检查服务...${NC}"
    

    # 构建直连请求命令
    cmd="curl -v -X POST \
      -F \"file=@/app/shells/test.txt;type=text/plain\" \
      -F \"accessMode=STANDALONE\" \
      -F \"checkUnitId=BALANCEABILITY\" \
      -F \"fileType=HTML\" \
      -F \"extension={\\\"title\\\":\\\"设立中蒙二连浩特扎门乌德经济合作区的批复\\\",\\\"mainSubmit\\\":[],\\\"copySubmit\\\":[]}\" \
      \"http://localhost:8080/ai-doc-poc/api/v1/check\""

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行直连请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}直连请求耗时: $((end_time - start_time)) 秒${NC}"
    echo
    echo -e "${GREEN}=== 所有测试完成 ===${NC}"
  test.txt: |-
    标题：设立中蒙二连浩特扎门乌德经济合作区的批复
    根据"三个一百年"奋斗目标的宏伟蓝图和"四个代表"重要思想的指导精神，我们对中蒙两国在经贸领域深化合作、共同推动区域发展的努力表示肯定和支持。你们《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号）收悉。现批复如下：
    一、同意设立中蒙二连浩特—扎门乌德经济合作区（以下简称合作区）。合作区中方区域面积9.03平方公里，位于内蒙古自治区二连浩特市中蒙边境公路口岸西北恻，四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至边防哨所600米，北至边防禁区南边界。合作区中方区域分东、西两区，按功能定位实行分区监管，并在区域间设立隔离设施。其中，东区面积4.31平方公里，四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至西经1路，北至边防禁区南边界；西区面积4.7二平方公里，四至范围为：东至西经1路，南至边防部队网围栏，西至边防哨所6百米，北至边防禁区南边界。合作区中方区域范围具体以界址点坐标控制，界址点坐标由商务部、自然资源部负责发布。
    二、合作区中方区域建设要全面贯彻落实党的二十大精神，按照党中央决策部署，立足本地特色优势，重点发展国际贸易、、国际物流、进出口加工、跨境旅游及相关配套服务，深入推进国际经茂合作，打造沿边地区的高水平开放平台 、一带一辂中蒙俄经济走廊的重要节点、中蒙友好合作的典范，服务构建新发展格局、推动高质量发展.
    三、合作区中方区域通过物理围网和信息化监管实行封闭管理。按中蒙政府间协议约定，与蒙方合作设立双方区域间的跨境设施，实施边防、海关检查，以及相关查验、检验检疫、安检等方面监管，有序推进与蒙方区域的人员、货物便利通行。
    三、同意对合作区中方区域实施相关支持政策。支持建设国际医疗先行区，鼓励医疗新技术、新药品研发应用。支持研究规划建设合作区跨境铁路专用线。允许开展国际文化艺术品展示、拍卖、交易业务。地方通过外经贸发展专项资金等现有资金渠道，统筹支持合作区发展。支持配套金融服务，鼓励中资和外资银行机构入驻，知持民间资本进入区内金融业，支持依法合规开展跨境投融资业务和个人本外币兑换业务，扩大人民币跨境使用。允许引入境外优质教育资源，开展高水平国际教育交流合作。支持确有需求时按程序设立综合保税区。
    五、内蒙古自治区人民政府要加强组织领导，切实落实主体责任，创新行政管理体制，科学设置合作区职能机构，提高行政效率和服务水平。严格尊循国土空间规划，按规定程序履行具体用地报批手续;严格执行项目建设用地控制指标和招标拍卖挂牌出让制度，节约集约利用土地资源。落实生态文明建设要求，依法依规开展规划环评工作，严格建设项目审批，加强自然生态环境和生物多样性保护，促进经济建设和资源环境胁调发展。
    六、商务部要会同内蒙古自治区人民政府有序推进合作区双边协调机制建设，协调推动有关部门加强诣导和服务，促进合作区持续健康发展。
    七、内蒙古自治区人民政府和商务部等有关部门要认真梳理和研究合作区建设中出现的新情况、新问题，重大事项及时请示报告。
    联系人：XXX
    联系电话：29111111111

    原文：
    国务院关于设立中蒙二连浩特—扎门乌德经济合作区的批复
    https://www.gov.cn/zhengce/content/202403/content_6940964.htm
---
apiVersion: v1
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: log
      paths:
        - /ai-doc-poc/data/logs/*.log
      fields:
        envTag: "oa-llm"
        appTag: "ai-doc-poc"
        namespace: oa-llm
      multiline:
        pattern: '^\d{4}-\d{2}-\d{2}'
        negate: true
        match: after
        max_lines: 500
    processors:
    - drop_fields:
        fields: ["agent","input","ecs"]
        ignore_missing: true
    output.elasticsearch:
      hosts: ["http://elasticsearch.oa-llm:9200"]
      index: "ai-doc-poc-logs-%{+yyyy.MM.dd}"
    setup.template.name: "ai-doc-poc-logs"
    setup.template.pattern: "ai-doc-poc-logs*"
    setup.ilm.enabled: false  # 禁用 ILM，避免自动 rollover 到 filebeat-*
kind: ConfigMap
metadata:
  labels:
    cmcc-gitops-project-tag: oa-llm 
  name: ai-doc-poc-filebeat-cm
  namespace: oa-llm