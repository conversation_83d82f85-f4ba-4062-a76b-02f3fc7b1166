spring:
  datasource:
    url: jdbc:postgresql://{{postgresql.ip}}:5432/aidb?currentSchema=qa&useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&useSSL=false
    username: admin
    password: PgSQL@2025

  data:
    redis:
      host: {{redis.ip}}
      port: 6379
      password: admin123
      database: 2

ai-chat:
  url: http://aiknowledge-controller:8080
  appId: afsoprmv
logging:
  file:
    path: /app/logs
  pattern:
    console:
      console-pattern: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
      file-pattern: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"