server:
  port: 8028
  tomcat:
    accept-count: 200
    min-spare-threads: 30
    max-threads: 200
    max-connections: 10000

spring:
  application:
    name: ai-knowledge-controller
  # db数据源
  datasource:
    driverClassName: org.postgresql.Driver
    url: *************************************
    username: admin
    password: PgSQL@2025
  data:
    redis:
      database: 1
      host: ********
      port: 6379
      password: admin123
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 150
          max-wait: -1ms
          max-idle: 16
          min-idle: 16
          time-between-eviction-runs: 120s
  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      maxFileSize: 200MB
      maxRequestSize: 200MB

logging:
  level:
    com.chinamobile.cmit.ai: debug
    org.springframework: info

capabilityapi:
  # 多模型配置
  models:
    deepseek32b:
      docChatUrl: http://*************:9050/CaiwuDS/Rag/QAOnlineSSE        # 对话地址 宁波国产化
      docChatAppid: ragcw-ds                                               # 对话服务应用ID
      docChatAppkey: 232a0ab2fde842493b187115605f1846                      # 对话服务密钥
    deepseek671b:
      docChatUrl: http://*************:9050//CaiwuDS671B/Rag/QAOnlineSSE   # 对话地址 宁波国产化
      docChatAppid: ragcw-ds                                               # 对话服务应用ID
      docChatAppkey: 232a0ab2fde842493b187115605f1846                      # 对话服务密钥
    jiutian57b:
      docChatUrl: http://*************:9050/test/Rag/QAOnlineSSE           # 对话地址 宁波国产化
      docChatAppid: ragonlin                                               # 对话服务应用ID
      docChatAppkey: fe4a7c95f91bdd452e99699bc981bbbd                      # 对话服务密钥
    qwen32b:
      docChatUrl: http://*************:9050/Caiwu/Rag/QAOnlineSSE          # 对话地址 宁波国产化
      docChatAppid: ragcaiwu                                               # 对话服务应用ID
      docChatAppkey: bb1ae28bc9f2a04fb77818e0bead42f7                      # 对话服务密钥
  # 文档解析参数 贵阳国产化集群
  docParserChunkUrl: http://*************:9050/Rag/DocPaserChunk/test-gy   # 文档解析服务地址
  docParserAppid: ragonlin                                                 # 文档解析服务应用ID
  docParserAppkey: fe4a7c95f91bdd452e99699bc981bbbd                        # 文档解析服务密钥
  # 向量库参数 宁波国产化集群
  knowledgeBaseApiUrl: http://*************:9050/prod/kg/aud               # 向量库基础API地址，新增，删除，修改知识库
  qaChangeApiUrl: http://*************:9050/prod/qa/aud                    # 问答对批量删除接口地址
  qaBatchAddApiUrl: http://*************:9050/prod/qa/add/batch            # 问答对批量添加接口地址
  knowledgeBaseAppKey: b8d8a50b547e204e3479c39d7eeb71be                    # 向量库访问密钥
  knowledgeBaseAppId: ragkgdat                                             # 向量库应用ID
  # 统一用户能力平台接口参数
  tyyhBaseUrl: http://172.16.243.8:8080/bap/gw/oppf/                       # 统一用户能力平台接口地址
  tywdBaseUrl: http://172.16.243.8:8080/bap/gw/updown/iuMZZr/              # 统一文档能力平台接口地址
  uploadPath: C:/ai-knowledge/uploads                                      # 文件上传路径
  gatewayTokenUrl: http://172.16.243.8:8080/bap/gw/aopoauth/oauth/token    # 网关token地址
  tyyhBaseAppId: 10640                                                     # 统一用户能力平台应用ID
  tyyhBaseAppKey: d8a9cc2386c4df957e888e8d50497084                         # 统一用户能力平台应用密钥
  clientId: uni_621_webaia                                                 # 客户端ID
  clientSecret: U2FsdGVkX1/Q4y6uVX5pdoLDBv/ZmvFInLEvgjVSRc=                # 客户端密钥
  smsApiBaseUrl: http://*************:7010/xxzx/sms/v1/submit              # 短信API地址
  smsAppKey: 10640                                                         # 短信API应用ID
  smsAppSecret: d8a9cc2386c4df957e888e8d50497084                           # 短信API应用密钥
  minio:
    endpoint: http://*********:9000
    access-key-id: admin
    access-key-secret: MiniO@2025
    bucket-name: wensi

opensearch:
  hostname: *********
  port: 9200
  scheme: http
  username: admin
  password: admin
  appid: qasystem
  appkey: 425be7ea62a7684db018995f04030ca2
  segment-url: http://*************:9050/test/Rag/Segment
  embedding-url: http://*************:9050/test/Rag/Embedding/bge_m3

api:
  auth:
    systems:
      ids: xira4yr8,bgdmoapi,afsoprmv  # 系统ID列表，逗号分隔
      xira4yr8:  # 财务家园的认证信息
        appId: xira4yr8
        appKey: 4a7b52f63e7a2c7f89fcb93f140bc2d3
        callbackUrl: http://************:13201/core/api/v1/knowledge/document/callback
        capabilityName: FinancialHomeAI
      bgdmoapi:  # 供应链的认证信息
        appId: bgdmoapi
        appKey: 52c76073075b8d4f02eb596688b94679
        capabilityName: officeLM
      afsoprmv:  # 系统3的认证信息
        appId: afsoprmv
        appKey: 52c76073075b8d4f02eb596688b946d3
        callbackUrl: http://***************:8091/knowledge/doc/callback
        capabilityName: system3

management:
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: "*"

