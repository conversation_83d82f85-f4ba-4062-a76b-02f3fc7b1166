#!/bin/bash
# 重启hegao应用的脚本

# 设置应用参数
APP_NAME=article-auto-compose-server
MODUL_NAME=hegao
HOST_PORT=8094
IMAGE_TAG="0.0.1-SNAPSHOT"
# 调用通用重启脚本
sh /data/build/restart_app.sh -a ${APP_NAME} -m ${MODUL_NAME} -p ${HOST_PORT} -t ${IMAGE_TAG}

# 接口验证
echo "接口验证: curl -X POST http://localhost:8094/ai-compose-poc/api/v1/compose"
curl -X POST http://localhost:8094/ai-compose-poc/api/v1/compose

curl -v -X POST http://127.0.0.1:8094/ai-compose-poc/api/v1/compose \
-H 'Content-Type: application/json' \
-d '{
    "composeUnitId": "glmwriter",
    "text": "请以《中国移动公司关于开展成本管理专项活动通知》为主题生成一篇通知类型的公文。字数要求100字以上"
}'