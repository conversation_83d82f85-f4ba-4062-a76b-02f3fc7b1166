#!/bin/bash
echo "===== 配置Filebeat连接到Elasticsearch ====="

# Elasticsearch认证信息
ES_USERNAME="elastic"
ES_PASSWORD="elastic123"

# 确保Elasticsearch已经部署
echo "1. 检查Elasticsearch是否已部署"
if ! curl -s -u ${ES_USERNAME}:${ES_PASSWORD} http://localhost:9200 | grep -q "version"; then
  echo "错误: Elasticsearch未部署或无法访问。请先运行 deploy/elasticsearch_kibana/deploy.sh"
  exit 1
fi

# 创建Filebeat配置
echo "2. 创建Filebeat配置文件"
mkdir -p /data/filebeat/{config,data,logs}
chmod -R 755 /data/filebeat

cat > /data/filebeat/config/filebeat.yml <<EOF
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /data/znwd/rbac/logs/*.log
  fields:
    type: rbac
    index: rbac-logs
  multiline:
    pattern: '^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  close_inactive: 5m
  scan_frequency: 10s

- type: log
  enabled: true
  paths:
    - /data/znwd/qa/logs/*.log
  fields:
    type: qa
    index: qa-logs
  multiline:
    pattern: '^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  close_inactive: 5m
  scan_frequency: 10s

- type: log
  enabled: true
  paths:
    - /data/znwd/aiknowledge-controller/logs/*.log
  fields:
    type: aiknowledge
    index: aiknowledge-logs
  multiline:
    pattern: '^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  close_inactive: 5m
  scan_frequency: 10s

processors:
  - add_host_metadata: ~
  - add_docker_metadata: ~
  - decode_json_fields:
      fields: ["message"]
      process_array: false
      max_depth: 1
      target: ""
      overwrite_keys: true
      when:
        contains:
          message: "{"

output.elasticsearch:
  hosts: ["http://localhost:9200"]
  username: "${ES_USERNAME}"
  password: "${ES_PASSWORD}"
  indices:
    - index: "rbac-%{+yyyy.MM.dd}"
      when.contains:
        fields.type: "rbac"
    - index: "qa-%{+yyyy.MM.dd}"
      when.contains:
        fields.type: "qa"
    - index: "aiknowledge-%{+yyyy.MM.dd}"
      when.contains:
        fields.type: "aiknowledge"

setup.ilm.enabled: false
setup.template.enabled: false
setup.kibana:
  host: "http://localhost:5601"
  username: "${ES_USERNAME}"
  password: "${ES_PASSWORD}"

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
EOF

# 检查日志目录和文件权限
echo "3. 检查日志目录和文件权限"
for logdir in "/data/znwd/rbac/logs" "/data/znwd/qa/logs" "/data/znwd/aiknowledge-controller/logs"; do
  if [ ! -d "$logdir" ]; then
    echo "创建目录 $logdir"
    mkdir -p $logdir
    chmod -R 755 $logdir
  fi
done

# 生成测试日志
echo "4. 生成测试日志"
current_time=$(date '+%Y-%m-%d %H:%M:%S')
echo "$current_time [INFO] 这是一条测试日志 - $(date +%s)" > /data/znwd/rbac/logs/rbac.log
echo "$current_time [INFO] 这是一条测试日志 - $(date +%s)" > /data/znwd/qa/logs/spring.log
echo "$current_time [INFO] 这是一条测试日志 - $(date +%s)" > /data/znwd/aiknowledge-controller/logs/application.log

# 设置文件权限
echo "5. 设置文件权限"
chmod -R o+r /data/znwd/rbac/logs/ /data/znwd/qa/logs/ /data/znwd/aiknowledge-controller/logs/

# 停止并删除旧容器
echo "6. 停止并删除旧容器"
docker rm -f filebeat 2>/dev/null || true

# 拉取Filebeat镜像
echo "7. 拉取Filebeat镜像"
docker pull docker.elastic.co/beats/filebeat:8.12.0

# 启动Filebeat容器
echo "8. 启动Filebeat容器"
docker run -d \
  --name=filebeat \
  --user=root \
  --network=host \
  --volume="/data/filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro" \
  --volume="/data/filebeat/data:/usr/share/filebeat/data:rw" \
  --volume="/data/filebeat/logs:/var/log/filebeat:rw" \
  --volume="/data/znwd/rbac/logs:/data/znwd/rbac/logs:ro" \
  --volume="/data/znwd/qa/logs:/data/znwd/qa/logs:ro" \
  --volume="/data/znwd/aiknowledge-controller/logs:/data/znwd/aiknowledge-controller/logs:ro" \
  docker.elastic.co/beats/filebeat:8.12.0 \
  filebeat -e --strict.perms=false

# 检查是否成功启动
echo "9. 检查Filebeat是否成功启动"
if docker ps | grep -q filebeat; then
  echo "✅ Filebeat成功启动"
else
  echo "❌ Filebeat启动失败"
  docker logs filebeat
  exit 1
fi

# 等待日志处理
echo "10. 等待10秒，让Filebeat处理日志..."
sleep 10

# 检查日志
echo "11. Filebeat日志："
docker logs filebeat | tail -n 20

# 检查索引是否创建
echo "12. 检查索引是否创建："
today=$(date +"%Y.%m.%d")
curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://localhost:9200/_cat/indices/rbac-$today?v" || echo "rbac索引未创建"
curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://localhost:9200/_cat/indices/qa-$today?v" || echo "qa索引未创建"
curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://localhost:9200/_cat/indices/aiknowledge-$today?v" || echo "aiknowledge索引未创建"

# 显示Kibana链接
echo "===== Filebeat配置完成 ====="
echo "访问Kibana查看日志数据: http://服务器IP:5601"
echo "Elasticsearch用户名: ${ES_USERNAME}"
echo "Elasticsearch密码: ${ES_PASSWORD}"
echo "在Kibana中创建索引模式可以使用: rbac-*, qa-*, aiknowledge-*" 