#!/bin/bash
echo "检查OpenSearch索引和数据"

# 检查所有索引
echo "所有索引列表："
curl -k -u admin:admin -X GET "https://10.1.4.16:9200/_cat/indices?v"

# 检查特定索引
for index in "rbac-2025" "qa-2025" "aiknowledge-controller-2025"; do
  echo -e "\n检查索引 $index："
  curl -k -u admin:admin -X GET "https://10.1.4.16:9200/$index/_count" -H 'Content-Type: application/json'
  
  echo -e "\n最新文档示例："
  curl -k -u admin:admin -X GET "https://10.1.4.16:9200/$index/_search" -H 'Content-Type: application/json' -d'
  {
    "size": 1,
    "sort": [
      {
        "@timestamp": {
          "order": "desc"
        }
      }
    ]
  }
  '
done 