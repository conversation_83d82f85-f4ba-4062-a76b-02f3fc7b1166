#!/bin/bash
# 通用应用重启脚本

# 显示帮助信息
show_help() {
    echo "用法: $0 -a APP_NAME -m MODULE_NAME [-p HOST_PORT] [-t IMAGE_TAG]"
    echo
    echo "参数:"
    echo "  -a APP_NAME      应用名称(必需)"
    echo "  -m MODULE_NAME   模块名称(必需)"
    echo "  -p HOST_PORT     主机端口号(默认:8080)"
    echo "  -t IMAGE_TAG     镜像标签(默认:1.0-SNAPSHOT)"
    echo "  -h               显示帮助信息"
    echo
    exit 1
}

# 设置默认值
HOST_PORT=8080
IMAGE_TAG="1.0-SNAPSHOT"

# 解析命令行参数
while getopts "a:m:p:t:h" opt; do
  case $opt in
    a) APP_NAME="$OPTARG" ;;
    m) MODULE_NAME="$OPTARG" ;;
    p) HOST_PORT="$OPTARG" ;;
    t) IMAGE_TAG="$OPTARG" ;;
    h) show_help ;;
    \?) echo "无效的选项: -$OPTARG" >&2; show_help ;;
  esac
done

# 检查必需参数
if [ -z "$APP_NAME" ] || [ -z "$MODULE_NAME" ]; then
    echo "错误: APP_NAME和MODULE_NAME是必需的参数!"
    show_help
fi

# 设置应用目录
APP_DATA_DIR=/data/${MODULE_NAME}/${APP_NAME}
DEPLOY_DIR=/data/build/${APP_NAME}

echo "===== 重启 ${APP_NAME} 应用 ====="
echo "模块名称: ${MODULE_NAME}"
echo "主机端口: ${HOST_PORT}"
echo "应用目录: ${APP_DATA_DIR}"
echo "镜像标签: ${IMAGE_TAG}"
echo "部署目录: ${DEPLOY_DIR}"
echo "镜像名称: ${MODULE_NAME}/${APP_NAME}:${IMAGE_TAG}"
# 检查镜像是否存在
if ! docker images ${MODULE_NAME}/${APP_NAME}:${IMAGE_TAG} | grep -q ${IMAGE_TAG}; then
    echo "错误: 镜像 ${MODULE_NAME}/${APP_NAME}:${IMAGE_TAG} 不存在!"
    echo "请先运行部署脚本来创建镜像。"
    exit 1
fi

# 1. 停止并删除已存在的容器
echo "1. 停止并删除容器 ${APP_NAME}..."
docker stop ${APP_NAME} || true
docker rm -f ${APP_NAME} || true
echo "容器已停止并删除"

# 将APP_DATA_DIR 目录下的文件拷贝到/data/build/${APP_NAME} 目录下
echo "将APP_DATA_DIR 目录下的文件拷贝到${DEPLOY_DIR} 目录下"
# 先确保目录存在
mkdir -p ${DEPLOY_DIR}
# 先清空/data/build/${APP_NAME} 目录下的文件
rm -rf ${DEPLOY_DIR}/*
# 将APP_DATA_DIR 目录下的文件拷贝到/data/build/${APP_NAME} 目录下 
if [ -d "${APP_DATA_DIR}/config" ]; then
    echo "执行命令: cp -r ${APP_DATA_DIR}/config/* ${DEPLOY_DIR}/"
    cp -r ${APP_DATA_DIR}/config/* ${DEPLOY_DIR}/ || true
fi

if [ -d "${APP_DATA_DIR}/shells" ]; then
    echo "执行命令: cp -r ${APP_DATA_DIR}/shells/* ${DEPLOY_DIR}/"
    cp -r ${APP_DATA_DIR}/shells/* ${DEPLOY_DIR}/ || true
fi

if [ -f "${APP_DATA_DIR}/app.jar" ]; then
    echo "执行命令: cp -r ${APP_DATA_DIR}/app.jar ${DEPLOY_DIR}/"
    cp -r ${APP_DATA_DIR}/app.jar ${DEPLOY_DIR}/ || true
fi

if [ -f "${APP_DATA_DIR}/restart.sh" ]; then
    echo "执行命令: cp -r ${APP_DATA_DIR}/restart.sh ${DEPLOY_DIR}/"
    cp -r ${APP_DATA_DIR}/restart.sh ${DEPLOY_DIR}/ || true
fi
echo "设置logs目录的权限"
echo "设置logs目录的权限 执行命令: sudo chmod -R 777 ${APP_DATA_DIR}/logs"
sudo chmod -R 777 ${APP_DATA_DIR}/logs

echo "设置files目录的权限"
echo "设置files目录的权限 执行命令: sudo chmod -R 777 ${APP_DATA_DIR}/files"
sudo chmod -R 777 ${APP_DATA_DIR}/files

echo "执行命令: ls -l ${DEPLOY_DIR}/"
ls -l ${DEPLOY_DIR}/

# 2. 启动新容器
echo "2. 启动容器 ${APP_NAME}..."
echo "执行命令: docker run -itd --name ${APP_NAME} --network jiutianwensi --restart always -v ${APP_DATA_DIR}/app.jar:/app/app.jar -v ${APP_DATA_DIR}/config:/app/config -v ${APP_DATA_DIR}/logs:/app/logs -v ${APP_DATA_DIR}/files:/app/files -v ${APP_DATA_DIR}/shells:/app/shells -p ${HOST_PORT}:8080 ${MODULE_NAME}/${APP_NAME}:${IMAGE_TAG}"

docker run -itd \
    --name ${APP_NAME} \
    --network jiutianwensi \
    --restart always \
    -v ${APP_DATA_DIR}/app.jar:/app/app.jar \
    -v ${APP_DATA_DIR}/config:/app/config \
    -v ${APP_DATA_DIR}/logs:/app/logs \
    -v ${APP_DATA_DIR}/files:/app/files \
    -v ${APP_DATA_DIR}/shells:/app/shells \
    -p ${HOST_PORT}:8080 \
    ${MODULE_NAME}/${APP_NAME}:${IMAGE_TAG}

# 3. 检查容器状态
echo "3. 检查容器状态..."
if docker ps | grep -q ${APP_NAME}; then
    echo "容器 ${APP_NAME} 已成功启动!"
    echo "容器详情:"
    docker ps | grep ${APP_NAME}
    
    echo "容器日志:"
    docker logs ${APP_NAME}
else
    echo "错误: 容器 ${APP_NAME} 启动失败!"
    docker logs ${APP_NAME} || true
    exit 1
fi

echo "===== ${APP_NAME} 重启完成 =====" 

# 重启一下filebeat
echo "重启一下filebeat"
if sudo docker ps | grep -q filebeat; then
    sudo docker restart filebeat
else
    echo "filebeat不存在"
fi
