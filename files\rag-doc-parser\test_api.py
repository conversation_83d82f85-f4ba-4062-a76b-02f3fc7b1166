# -*- coding: utf-8 -*-
import requests
import json
import uuid
import time
import base64
import hashlib

from utils import *

url = "http://127.0.0.1:8005/doc_generate_qa/v1/service"


# 要上传的文件路径
# file_path = "test.pdf"
file_path = "test1.docx"
with open(file_path, "rb") as f:
    # 定义文件参数
    # files = {"file": (file_path, f, "application/pdf")}
    files = {"file": (file_path, f, "application/vnd.openxmlformats-officedocument.wordprocessingml.document")}
    # 定义额外的请求头和数据
    # headers = {"Authorization": "Bearer your_token"}
    data = {"description": "This is a test pdf file"}
    # 发送 POST 请求
    res= requests.post(url, files=files, headers={}, data=data)
    print("res == "+str(res))
    response_headers = res.headers
    if res.ok:
        print(res.json())
