# bin/bash
# 获取当前目录
CURRENT_DIR=$(pwd)
# 获取父目录
PARENT_DIR=$(dirname ${CURRENT_DIR})
# 读取config.json文件
CONFIG_FILE="${CURRENT_DIR}/config.json"

# 创建logs目录
mkdir -p ${CURRENT_DIR}/logs

# 检查文件是否存在
if [ ! -f ${CONFIG_FILE} ]; then
    echo "错误: config.json文件不存在"
    exit 1
fi

# 检查jq是否安装
if ! command -v jq &> /dev/null; then
    echo "错误: jq 未安装，请先安装 jq"
    exit 1
fi

# 检查JSON文件格式
if ! jq '.' ${CONFIG_FILE} > /dev/null 2>&1; then
    echo "错误: config.json 文件格式不正确"
    exit 1
fi

# 读取config.json文件中的base-config字段
SOURCE_IMAGE_REPO_URL=$(jq -r '.["base-config"]["source-image-repo-url"]' ${CONFIG_FILE})
SOURCE_IMAGE_REPO_USER=$(jq -r '.["base-config"]["source-image-repo-user"]' ${CONFIG_FILE})
SOURCE_IMAGE_REPO_PASSWORD=$(jq -r '.["base-config"]["source-image-repo-password"]' ${CONFIG_FILE})

# 检查是否成功读取配置
if [ -z "$SOURCE_IMAGE_REPO_URL" ] || [ -z "$SOURCE_IMAGE_REPO_USER" ] || [ -z "$SOURCE_IMAGE_REPO_PASSWORD" ]; then
    echo "错误: 读取配置文件失败"
    exit 1
fi

# 登录源镜像仓库
echo "登录源镜像仓库"
echo "docker login ${SOURCE_IMAGE_REPO_URL} -u ${SOURCE_IMAGE_REPO_USER} -p ${SOURCE_IMAGE_REPO_PASSWORD}" >> ${CURRENT_DIR}/logs/cmd.log
docker login ${SOURCE_IMAGE_REPO_URL} -u ${SOURCE_IMAGE_REPO_USER} -p ${SOURCE_IMAGE_REPO_PASSWORD}

# 遍历replace-config中的每一个节点
REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})
for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
    # 获取source-images-url 
    SOURCE_IMAGES_URL=$(jq -r ".[\"replace-config\"][$i][\"source-images-url\"]" ${CONFIG_FILE})
    # 获取target-images-url
    TARGET_IMAGES_URL=$(jq -r ".[\"replace-config\"][$i][\"target-images-url\"]" ${CONFIG_FILE})
    # 获取description
    DESCRIPTION=$(jq -r ".[\"replace-config\"][$i][\"description\"]" ${CONFIG_FILE})
    # 获取type
    TYPE=$(jq -r ".[\"replace-config\"][$i][\"type\"]" ${CONFIG_FILE})
    
    # 下载镜像
    echo "下载[$TYPE:$DESCRIPTION]镜像: ${SOURCE_IMAGES_URL} -> ${TARGET_IMAGES_URL}"
    # 下载镜像
    echo "docker pull ${SOURCE_IMAGES_URL}" >> ${CURRENT_DIR}/logs/cmd.log
    docker pull ${SOURCE_IMAGES_URL}
    # 重新打标签
    echo "docker tag ${SOURCE_IMAGES_URL} ${TARGET_IMAGES_URL}" >> ${CURRENT_DIR}/logs/cmd.log
    docker tag ${SOURCE_IMAGES_URL} ${TARGET_IMAGES_URL}
done
# 下载filebeat镜像
echo "下载filebeat镜像"
echo "docker pull ${FILEBEAT_SOURCE_IMAGES_URL}" >> ${CURRENT_DIR}/logs/cmd.log
docker pull ${FILEBEAT_SOURCE_IMAGES_URL}
# 重新打标签
echo "docker tag ${FILEBEAT_SOURCE_IMAGES_URL} ${FILEBEAT_TARGET_IMAGES_URL}" >> ${CURRENT_DIR}/logs/cmd.log
docker tag ${FILEBEAT_SOURCE_IMAGES_URL} ${FILEBEAT_TARGET_IMAGES_URL}








