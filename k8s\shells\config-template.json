{"description": {"description": "字段说明", "source-image-repo-url": "源镜像仓库地址", "source-image-repo-user": "源镜像仓库用户名", "source-image-repo-password": "源镜像仓库密码", "source-image-namespace": "源镜像仓库命名空间", "target-image-repo-url": "目标镜像仓库地址", "target-image-repo-user": "目标镜像仓库用户名", "target-image-repo-password": "目标镜像仓库密码", "target-image-namespace": "目标镜像仓库命名空间", "cpu-arch": "cpu架构", "yml-file-name": "yml文件名", "type": "类型:application/middleware", "storage-class": "目标集群的存储类名称", "proxy-enable": "是否启用代理", "proxy-model": "代理模式:nginx/forward", "proxy-port": "每个服务的代理端口，在nginx模式的port模式或者forward模式下有效", "proxy-config.nginx-config.mode": "NGINX代理模式:port/server-name", "proxy-config.nginx-config.service-name-config": "服务名称配置，在nginx模式的server-name模式下有效", "proxy-config.nginx-config.image-url": "NGINX镜像地址"}, "base-config": {"cpu-arch": "arm64", "target-image-repo-url": "10.7.202.240:8080", "target-image-repo-user": "admin", "target-image-repo-password": "Admin123", "target-image-namespace": "native_common", "source-image-repo-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn", "source-image-repo-user": "zglyjt", "source-image-repo-password": "Znwd0415@", "source-image-namespace": "jtws"}, "storage-config": {"storage-class": "standard", "server": "10.7.202.243", "path": "/nfs/data", "enabled": "true"}, "filebeat-config": {"source-images-url": "{{source-image-repo-url}}/{{source-image-namespace}}/filebeat:{{cpu-arch}}-7.17.14", "target-images-url": "{{target-image-repo-url}}/{{target-image-namespace}}/filebeat:{{cpu-arch}}-7.17.14"}, "model-config": {"url": "http://10.7.202.253:8000/v1/chat/completions", "api-key": "", "model-name": "/opt/vllm_models/Qwen/Qwen2.5-72B-Instruct"}, "proxy-config": {"proxy-model": "nginx", "nginx-config": {"mode": "port", "service-name-config": {"port": "31001", "service-name-suffix": "oa-llm.jtws.com"}, "image-url": "{{target-image-repo-url}}/{{target-image-namespace}}/nginx:{{cpu-arch}}-1.27.4"}}, "replace-config": [{"source-images-url": "{{source-image-repo-url}}/{{source-image-namespace}}/elasticsearch:{{cpu-arch}}-7.17.14", "target-images-url": "{{target-image-repo-url}}/{{target-image-namespace}}/elasticsearch:{{cpu-arch}}-7.17.14", "yml-file-name": "elasticsearch-nostorage.yml", "description": "elasticsearch", "service-name": "elasticsearch", "type": "middleware", "proxy-port": "32000"}, {"source-images-url": "{{source-image-repo-url}}/{{source-image-namespace}}/kibana:{{cpu-arch}}-7.17.14", "target-images-url": "{{target-image-repo-url}}/{{target-image-namespace}}/kibana:{{cpu-arch}}-7.17.14", "yml-file-name": "kibana.yml", "description": "kibana", "service-name": "kibana", "type": "middleware", "proxy-port": "32001"}, {"source-images-url": "{{source-image-repo-url}}/{{source-image-namespace}}/poc-intelligence-view-write:{{cpu-arch}}-1.0.0", "target-images-url": "{{target-image-repo-url}}/{{target-image-namespace}}/poc-intelligence-view-write:{{cpu-arch}}-1.0.0", "yml-file-name": "poc-intelligence-view-write.yml", "description": "拟稿前端", "service-name": "poc-intelligence-view-write", "type": "application", "proxy-port": "32002"}, {"source-images-url": "{{source-image-repo-url}}/{{source-image-namespace}}/poc-intelligence-view:{{cpu-arch}}-1.0.0", "target-images-url": "{{target-image-repo-url}}/{{target-image-namespace}}/poc-intelligence-view:{{cpu-arch}}-1.0.0", "yml-file-name": "poc-intelligence-view.yml", "description": "核稿前端", "service-name": "poc-intelligence-view", "type": "application", "proxy-port": "32003"}, {"source-images-url": "{{source-image-repo-url}}/{{source-image-namespace}}/ai-doc-poc:{{cpu-arch}}-0.0.1-SNAPSHOT", "target-images-url": "{{target-image-repo-url}}/{{target-image-namespace}}/ai-doc-poc:{{cpu-arch}}-0.0.1-SNAPSHOT", "yml-file-name": "ai-doc-poc.yml", "description": "核稿java端", "service-name": "ai-doc-poc", "type": "application", "proxy-port": "32004"}, {"source-images-url": "{{source-image-repo-url}}/{{source-image-namespace}}/ai-redactor:{{cpu-arch}}-v2025.05.18", "target-images-url": "{{target-image-repo-url}}/{{target-image-namespace}}/ai-redactor:{{cpu-arch}}-v2025.05.18", "yml-file-name": "ai-redactor.yml", "description": "核稿python端", "service-name": "ai-redactor", "type": "application", "proxy-port": "32005"}, {"source-images-url": "{{source-image-repo-url}}/{{source-image-namespace}}/article-auto-compose-server:{{cpu-arch}}-0.0.1-SNAPSHOT", "target-images-url": "{{target-image-repo-url}}/{{target-image-namespace}}/article-auto-compose-server:{{cpu-arch}}-0.0.1-SNAPSHOT", "yml-file-name": "article-auto-compose-server.yml", "description": "拟稿java端", "service-name": "article-auto-compose-server", "type": "application", "proxy-port": "32006"}, {"source-images-url": "{{source-image-repo-url}}/{{source-image-namespace}}/ai-writer-nostream-python-arm:v10.0", "target-images-url": "{{target-image-repo-url}}/{{target-image-namespace}}/ai-writer-nostream-python-arm:v10.0", "yml-file-name": "ai-writer-nostream.yml", "description": "拟稿python端", "service-name": "ai-writer-nostream", "type": "application", "proxy-port": "32007"}, {"source-images-url": "{{source-image-repo-url}}/{{source-image-namespace}}/nginx:{{cpu-arch}}-1.27.4", "target-images-url": "{{target-image-repo-url}}/{{target-image-namespace}}/nginx:{{cpu-arch}}-1.27.4", "yml-file-name": "jtws.yml", "description": "系统入口", "service-name": "jtws", "type": "application", "proxy-enable": "false"}]}