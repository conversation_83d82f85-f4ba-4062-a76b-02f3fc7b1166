mkdir -p /opt/nginx/{conf,log,html}
chmod -R 755 /opt/nginx‌
cat << EOF > /opt/nginx/conf/nginx.conf
# Nginx 配置文件

worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    # 日志格式
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  logs/access.log  main;

    sendfile        on;
    keepalive_timeout  65;

    # DeepSeek-R1-Distill-Qwen-32B 模型服务
    server {
        listen 10080;
        server_name DeepSeek-R1-Distill-Qwen-32B.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_1_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Qwen2.5-VL-7B-Instruct 模型服务
    server {
        listen 10080;
        server_name Qwen2.5-VL-7B-Instruct.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_2_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # bge-embedding-m3 模型服务
    server {
        listen 10080;
        server_name bge-embedding-m3.znwd.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_3_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # bce-embedding-v1 模型服务
    server {
        listen 10080;
        server_name bce-embedding-v1.znwd.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_4_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # bge-reranker-m3-v2 模型服务
    server {
        listen 10080;
        server_name bge-reranker-m3-v2.znwd.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_5_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # qwen-32b-instruct 模型服务
    server {
        listen 10080;
        server_name qwen-32b-instruct.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_6_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # qwen-32b-coder 模型服务
    server {
        listen 10080;
        server_name qwen-32b-coder.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_7_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # deepseek-R1 模型服务
    server {
        listen 10080;
        server_name deepseek-R1.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_8_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # ocr.common 模型服务
    server {
        listen 10080;
        server_name ocr.common.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_9_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # jiqifanyi.aikuaiyi 模型服务
    server {
        listen 10080;
        server_name jiqifanyi.aikuaiyi.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_10_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # ocr.aikuaiyi 模型服务
    server {
        listen 10080;
        server_name ocr.aikuaiyi.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_11_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # hegao 模型服务
    server {
        listen 10080;
        server_name hegao.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_12_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # asr.nswj.aism 模型服务
    server {
        listen 10080;
        server_name asr.nswj.aism.model.jiutian.wensi.com;

        location / {
            proxy_pass http://backend_service_13_address:port/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF


docker rm -f nginx
docker run -d -p 10080:10080  --name nginx -v /opt/nginx/conf/nginx.conf:/etc/nginx/nginx.conf  -v /opt/nginx/html:/usr/share/nginx/html -v /opt/nginx/log:/var/log/nginx  m.daocloud.io/docker.io/nginx:1.27.4