server:
  port: 8080
  servlet:
    context-path: /ai-compose-poc
  shutdown: graceful

spring:
  application:
    name: ai-compose
  web:
    resources:
      static-locations: classpath:/
  servlet:
    multipart:
      location: /app/files
      max-request-size: 100MB #总文件大小
      max-file-size: 100MB #单个文件大小
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss.SSS
    time-zone: GMT+8
    defaultPropertyInclusion: non_null # always #非空属性才序列化
    deserialization:
      fail_on_unknown_properties: false #未定义的key不序列化

#配置文件加密
jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    ivGeneratorClassname: org.jasypt.iv.NoIvGenerator
    password: EbfYkitulv73I2p0mXI50JMXoaxZTKJ7

# 变更说明：原ai-service.providers.id 指移动分配应用ID。 现在大模型提供了uat环境，可能存在uat和生产环境APPID不一致情况。
#  改造为： 追加mobile-app-id作为移动分配应用ID，根据需要其值可从POM文件中根据环境获取，也可以写为固定值。
ai-service:
  providers:
    - id: glmwriter
      # 移动分配的应用Id
      mobile-app-id: ""
      name: 妙笔写作大模型
      scene: COMPOSE
      # 处理逻辑与九天大模型一致, 只是base-url, app-id, app-key不一样.
      spi-impl: mbwriterSpi
      base-url: http://ai-writer-nostream:8080/mubanwriter/v1/service
      app-id: 123
      app-key: 123
      ext-params:
        feedback-url:
        feedback-options-url:

# 错误类型映射
error-type:
  # 平能大模型与通用(浙江)错误码的映射
  balanceability: '{"检查常见字词错误": 1, "检查专有名词错误": 1, "检查标点符号错误": 2, "检查日期错误": 101, "检查数字单位类错误": 102, "检查语法错误": 106, "检查结构层次标注错误": 106, "检查引用格式错误": 109, "检查格式错误": 109}'

moa:
  check:
    # moa端核稿使用的大模型id. 值需要为CheckServiceEnum枚举.
    checkUnitId: SQUARE
    # 核稿结果文件的有效期(天)
    attachmentExpire: 7
logging:
  config: /app/config/logback.xml
