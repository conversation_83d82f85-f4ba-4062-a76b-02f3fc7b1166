#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# 读取文件内容并处理特殊字符
TEXT=$(cat test.txt | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g')

# 设置IP和端口
IP_PORT="************:20007"
CONTAINER_PORT="8080"

# 构建容器内命令
cmd_in="curl -v -X POST 'http://localhost:${CONTAINER_PORT}/ai-compose-poc/api/v1/compose' \
  -H 'Content-Type: application/json' \
  -d '{
    \"composeUnitId\": \"glmwriter\",
    \"text\": \"${TEXT}\"
  }'"

# 构建容器外命令
cmd_out="curl -v -X POST 'http://${IP_PORT}/ai-compose-poc/api/v1/compose' \
  -H 'Content-Type: application/json' \
  -d '{
    \"composeUnitId\": \"glmwriter\",
    \"text\": \"${TEXT}\"
  }'"

# 打印命令内容（不显示完整内容，避免输出过长）
echo -e "${BLUE}容器内命令:${NC}"
echo "$cmd_in" | head -c 100
echo "..."
echo -e "${BLUE}容器外命令:${NC}"
echo "$cmd_out" | head -c 100
echo "..."

# 记录开始时间
START_TIME=$(date +%s)
# 进入article-auto-compose-server容器执行curl命令
echo -e "${YELLOW}进入article-auto-compose-server容器执行curl命令:${NC}"
docker exec -it article-auto-compose-server /bin/bash -c "${cmd_in}"
END_TIME=$(date +%s)
# 计算执行时间
EXEC_TIME=$((END_TIME - START_TIME))
# 打印执行时间
echo -e "${GREEN}容器内执行时间: ${EXEC_TIME} 秒${NC}"

# 记录开始时间
START_TIME=$(date +%s)
# 执行cmd_out命令
echo -e "${YELLOW}执行容器外命令:${NC}"
eval "${cmd_out}"
END_TIME=$(date +%s)
# 计算执行时间
EXEC_TIME=$((END_TIME - START_TIME))
# 打印执行时间
echo -e "${GREEN}容器外执行时间: ${EXEC_TIME} 秒${NC}" 