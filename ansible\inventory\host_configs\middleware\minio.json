{"middleware_name": "minio", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "m.daocloud.io/docker.io/minio/minio:RELEASE.2023-04-28T18-11-17Z", "host_api_port": "9000", "container_api_port": "9000", "host_console_port": "9001", "container_console_port": "9001", "username": "admin", "password": "MiniO@2025", "host_data_dir": "{{base_dir}}/MinIO/data", "host_logs_dir": "{{base_dir}}/MinIO/log", "container_data_dir": "/data", "container_logs_dir": "/var/log/minio", "restart_script": "{{base_dir}}/MinIO/restart.sh", "test_script": "{{base_dir}}/MinIO/test_curl.sh", "env_vars": [{"name": "MINIO_ROOT_USER", "value": "admin"}, {"name": "MINIO_ROOT_PASSWORD", "value": "MiniO@2025"}], "start_command": "server /data --console-address \":9001\"", "test_commands": ["curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready"]}