#!/bin/bash
# qa服务启动脚本

# 应用日志
LOG_DIR="/app/logs"
mkdir -p $LOG_DIR

# 启动应用
echo "$(date) - 启动 QA 服务..." >> $LOG_DIR/startup.log

# 读取配置文件/app/config/config.properties，并设置环境变量
# 读取配置文件
CONFIG_FILE="/app/config/config.properties"
echo "读取配置文件$CONFIG_FILE"
echo "原始配置文件内容："
echo "--------------------------------"
cat $CONFIG_FILE
echo "--------------------------------"
# 判断配置文件是否存在，如果不存在，则不读取内容，也不在启动命令后添加启动参数
PROPS_ARGS=""
if [ ! -f "$CONFIG_FILE" ]; then
  echo "配置文件 $CONFIG_FILE 不存在,不读取配置文件内容"
  PROPS_ARGS=""
else
  # 使用grep过滤注释行和空行，然后转换为启动参数
  while IFS= read -r line; do
    # 忽略空行和注释行
    if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# ]]; then
      PROPS_ARGS="$PROPS_ARGS --$line"
    fi
  done < <(grep -v "^#" $CONFIG_FILE | grep -v "^[[:space:]]*$")
fi

echo "转换后的启动参数： $PROPS_ARGS"
echo "--------------------------------"

# 构建完整的启动命令
STARTUP_CMD="java -jar /app/app.jar \
  --server.port=8080 \
  --server.servlet.context-path=/ \
  --logging.file.path=/app/logs \
  --spring.profiles.active=prod \
  --spring.data.redis.host=******** \
  --ai-chat.url=http://aiknowledge-controller:8080"

# 如果有配置参数，添加到命令中
if [ ! -z "$PROPS_ARGS" ]; then
  STARTUP_CMD="$STARTUP_CMD $PROPS_ARGS"
fi

echo "启动命令： $STARTUP_CMD"

# 执行启动命令
eval $STARTUP_CMD

# 获取启动结果
RESULT=$?
if [ $RESULT -eq 0 ]; then
  echo "$(date) - QA 服务启动成功!" >> $LOG_DIR/startup.log
else
  echo "$(date) - QA 服务启动失败，退出码: $RESULT" >> $LOG_DIR/startup.log
fi 