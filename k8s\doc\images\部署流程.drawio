<mxfile host="Electron" modified="2025-05-27T09:21:31.617Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/22.1.16 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="MawKR28Iw8g4ABEe8fam" version="22.1.16" type="device">
  <diagram id="9mGGzdkMXjuFJG4Fv9BF" name="部署流程">
    <mxGraphModel dx="1036" dy="606" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="B63HIUDo3AI9SSUsLzak-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-1" target="B63HIUDo3AI9SSUsLzak-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-1" value="读取配置" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="30" y="310" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-2" target="B63HIUDo3AI9SSUsLzak-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-2" value="部署配置" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="294" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-3" value="应用部署" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="240" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-4" target="B63HIUDo3AI9SSUsLzak-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-4" value="根据模板生成部署YML文件" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="200" y="400" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-21" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-8" target="B63HIUDo3AI9SSUsLzak-20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-8" value="config.json" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="294" y="150" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-13" target="B63HIUDo3AI9SSUsLzak-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-13" value="下载镜像" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="30" y="400" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-14" target="B63HIUDo3AI9SSUsLzak-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-14" value="更名镜像" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="30" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-17" value="推送本地Harbor" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="30" y="590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-22" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-20" target="B63HIUDo3AI9SSUsLzak-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-26" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-20" target="B63HIUDo3AI9SSUsLzak-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-28" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-20" target="B63HIUDo3AI9SSUsLzak-27">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-20" value="" style="line;strokeWidth=2;html=1;perimeter=backbonePerimeter;points=[];outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="70" y="260" width="520" height="10" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-23" value="1" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;" vertex="1" parent="1">
          <mxGeometry x="20" y="270" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-24" target="B63HIUDo3AI9SSUsLzak-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-24" value="读取配置" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="240" y="310" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-27" target="B63HIUDo3AI9SSUsLzak-29">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-27" value="读取配置" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="510" y="310" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="B63HIUDo3AI9SSUsLzak-29" target="B63HIUDo3AI9SSUsLzak-31">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-29" value="动态生成Nginx-Proxy部署YML文件" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="470" y="400" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-31" value="部署Nginx-Proxy代理服务端口" style="rounded=0;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="470" y="500" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-33" value="2" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;" vertex="1" parent="1">
          <mxGeometry x="250" y="270" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="B63HIUDo3AI9SSUsLzak-34" value="3" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;" vertex="1" parent="1">
          <mxGeometry x="510" y="270" width="60" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
