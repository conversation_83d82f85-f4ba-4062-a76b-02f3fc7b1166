#!/bin/bash
# 部署服务子菜单
# 用于批量或单独部署服务，并配置Nginx反向代理

# 颜色定义
GREEN="\033[0;32m"
BLUE="\033[0;34m"
RED="\033[0;31m"
YELLOW="\033[1;33m"
NC="\033[0m" # 恢复默认颜色

# 获取脚本所在目录的绝对路径
CURRENT_DIR=$(cd "$(dirname "$0")" && pwd)
echo "当前目录: ${CURRENT_DIR}"
SHELLS_DIR=$(dirname "${CURRENT_DIR}")
echo "shells目录: ${SHELLS_DIR}"
WORK_DIR=$(dirname "${SHELLS_DIR}")
echo "work目录: ${WORK_DIR}"
YML_DIR="${WORK_DIR}/yml/work"
echo "yml目录: ${YML_DIR}"

# 加载工具函数
source "${SHELLS_DIR}/utils/utils.sh"

# 清屏函数
clear_screen() {
    clear
}

# 显示标题
show_title() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}             服务部署子菜单 v1.0               ${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo
}

# 显示部署菜单
show_deploy_menu() {
    clear_screen
    show_title
    echo -e "${GREEN}1. 部署所有服务${NC}"
    echo -e "${GREEN}2. 选择单个服务部署${NC}"
    echo -e "${GREEN}3. 部署/更新Nginx反向代理${NC}"
    echo -e "${GREEN}4. 一键部署所有服务并配置Nginx${NC}"
    echo -e "${YELLOW}9. 返回主菜单${NC}"
    echo -e "${RED}0. 退出${NC}"
    echo
    echo -e "${YELLOW}请选择操作 [0-9]:${NC} "
}

check_jq(){
    if command -v jq &>/dev/null; then
        echo "jq工具已存在"
        return    
    fi
    echo -e "${RED}错误: 需要jq工具但未安装${NC}"
    echo -e "${YELLOW}请使用命令安装: apt-get install jq -y${NC}"
    echo "安装jq工具"
    apt-get install jq -y
    echo
    echo -e "${GREEN}按任意键返回...${NC}"
    read -n 1
}

source_apply_service(){
        # 导入apply_service函数
    if  command -v apply_service &>/dev/null; then
        echo "apply_service函数已存在"
    else
         # 获取当前脚本的绝对路径
        CURRENT_SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
        # 获取shells目录
        SHELLS_DIR=$(dirname "${CURRENT_SCRIPT_DIR}")
        # 工具函数目录
        UTILS_DIR="${SHELLS_DIR}/utils"
        
        echo "当前脚本目录: ${CURRENT_SCRIPT_DIR}"
        echo "Shells目录: ${SHELLS_DIR}"
        echo "工具目录: ${UTILS_DIR}"
        
        # 修正路径引用，确保从utils目录加载
        if [ -f "${UTILS_DIR}/apply_service.sh" ]; then
            source "${UTILS_DIR}/apply_service.sh"
        else
            echo "错误：找不到apply_service.sh (路径: ${UTILS_DIR}/apply_service.sh)"
            echo "请确保工具脚本已正确安装"
            return 1
        fi
    fi
    
}

# 部署所有服务
deploy_all_services() {
    clear_screen
    show_title
    echo -e "${BLUE}正在准备部署所有服务...${NC}"
    
    # 检查配置文件是否存在
    CONFIG_FILE="${SHELLS_DIR}/config.json"
    if [ ! -f "$CONFIG_FILE" ]; then
        echo -e "${RED}错误: 配置文件不存在 ${CONFIG_FILE}${NC}"
        echo
        echo -e "${GREEN}按任意键返回...${NC}"
        read -n 1
        return
    fi
    
    check_jq
    source_apply_service
    # 读取配置文件中的服务列表
    REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})
    
    if [ "$REPLACE_CONFIG_COUNT" -eq 0 ]; then
        echo -e "${RED}错误: 配置文件中没有定义服务${NC}"
        echo
        echo -e "${GREEN}按任意键返回...${NC}"
        read -n 1
        return
    fi
    
    echo -e "${YELLOW}配置文件中发现 ${REPLACE_CONFIG_COUNT} 个服务${NC}"
    echo -e "${YELLOW}即将执行所有服务的部署流程...${NC}"
    echo
    read -p "是否确定部署所有服务? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        echo -e "${YELLOW}操作已取消${NC}"
        echo
        echo -e "${GREEN}按任意键返回...${NC}"
        read -n 1
        return
    fi
    

    # 创建storage-class

    NFS_STORAGE_CLASS_FILE="${YML_DIR}/storage/nfs-storage-class.yml"
    create_storage_class "${NFS_STORAGE_CLASS_FILE}"  "oa-llm" "${CONFIG_FILE}" 
    # 创建namespace
    create_namespace "oa-llm" "${CONFIG_FILE}"
    
    # 构建路径前缀
    PARENT_PARENT_DIR=$(dirname "${SHELLS_DIR}")
    TEST_SCRIPT_DIR="${PARENT_PARENT_DIR}/test-script"
    
    # 循环部署所有服务
    echo -e "${BLUE}开始部署所有服务...${NC}"
    
    # 从config.json中读取所有服务配置并按顺序存储
    declare -a services
    for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
        # 获取service-name
        service_name=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
        # 获取yml-file-name
        yml_file_name=$(jq -r ".[\"replace-config\"][$i][\"yml-file-name\"]" ${CONFIG_FILE})
        # 获取type
        type=$(jq -r ".[\"replace-config\"][$i][\"type\"]" ${CONFIG_FILE})
        
        # 构建路径
        yml_file_path="${YML_DIR}/${type}/${yml_file_name}"
        test_script_path="${TEST_SCRIPT_DIR}/test-${service_name}.sh"
        
        echo -e "${YELLOW}正在部署服务 ${service_name}...${NC}"
        # 调用apply_service函数
        apply_service "$yml_file_path" "$service_name" "$test_script_path" "oa-llm" "true" "${UTILS_DIR}"
        echo -e "${GREEN}服务 ${service_name} 部署完成${NC}"
        echo
    done
    
    echo
    echo -e "${GREEN}所有服务部署完成，按任意键返回...${NC}"
    read -n 1
}

# 选择单个服务部署
select_service_to_deploy() {
    clear_screen
    show_title
    echo -e "${BLUE}选择单个服务部署${NC}"
    echo

    
    # 检查配置文件是否存在
    CONFIG_FILE="${SHELLS_DIR}/config.json"
    if [ ! -f "$CONFIG_FILE" ]; then
        echo -e "${RED}错误: 配置文件不存在 ${CONFIG_FILE}${NC}"
        echo
        echo -e "${GREEN}按任意键返回...${NC}"
        read -n 1
        return
    fi
    
    check_jq
    source_apply_service
    
    # 读取配置文件中的服务列表
    REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})
    
    if [ "$REPLACE_CONFIG_COUNT" -eq 0 ]; then
        echo -e "${RED}错误: 配置文件中没有定义服务${NC}"
        echo
        echo -e "${GREEN}按任意键返回...${NC}"
        read -n 1
        return
    fi
    # 创建storage-class
    NFS_STORAGE_CLASS_FILE="${YML_DIR}/storage/nfs-storage-class.yml"
    echo "创建[NFS_STORAGE_CLASS_FILE]文件: ${NFS_STORAGE_CLASS_FILE}"
    create_storage_class "${NFS_STORAGE_CLASS_FILE}"  "oa-llm" "${CONFIG_FILE}" 
    # 创建namespace
    echo "创建[oa-llm]namespace"
    create_namespace "oa-llm" "${CONFIG_FILE}"
    
    echo -e "${YELLOW}可选择的服务:${NC}"
    for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
        SERVICE_NAME=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
        YML_FILE=$(jq -r ".[\"replace-config\"][$i][\"yml-file-name\"]" ${CONFIG_FILE})
        TYPE=$(jq -r ".[\"replace-config\"][$i][\"type\"]" ${CONFIG_FILE})
        echo -e "${GREEN}$((i+1)). ${SERVICE_NAME}${NC} (类型: ${TYPE}, 配置: ${YML_FILE})"
    done
    
    echo
    read -p "选择服务编号 [1-${REPLACE_CONFIG_COUNT}] (或输入 0 返回): " choice
    
    if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${REPLACE_CONFIG_COUNT} ]; then
        service_index=$((choice-1))
        SERVICE_NAME=$(jq -r ".[\"replace-config\"][$service_index][\"service-name\"]" ${CONFIG_FILE})
        YML_FILE_NAME=$(jq -r ".[\"replace-config\"][$service_index][\"yml-file-name\"]" ${CONFIG_FILE})
        TYPE=$(jq -r ".[\"replace-config\"][$service_index][\"type\"]" ${CONFIG_FILE})
        
        echo -e "${BLUE}已选择服务: ${SERVICE_NAME}${NC}"
        echo -e "类型: ${TYPE}"
        echo -e "配置文件: ${YML_FILE_NAME}"
        echo
        
        read -p "是否确定部署此服务? (yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            echo -e "${YELLOW}操作已取消${NC}"
            echo
            echo -e "${GREEN}按任意键返回...${NC}"
            read -n 1
            return
        fi
        
        # 构建路径
        PARENT_PARENT_DIR=$(dirname "${SHELLS_DIR}")
        YML_DIR="${PARENT_PARENT_DIR}/yml/work"
        TEST_SCRIPT_DIR="${PARENT_PARENT_DIR}/test-script"
        
        YML_FILE_PATH="${YML_DIR}/${TYPE}/${YML_FILE_NAME}"
        TEST_SCRIPT_PATH="${TEST_SCRIPT_DIR}/test-${SERVICE_NAME}.sh"
        
        # 确保在此处导入apply_service函数
        # 获取当前脚本的绝对路径
        CURRENT_SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
        # 获取shells目录
        SHELLS_DIR=$(dirname "${CURRENT_SCRIPT_DIR}")
        # 工具函数目录
        UTILS_DIR="${SHELLS_DIR}/utils"
        
        echo "当前脚本目录: ${CURRENT_SCRIPT_DIR}"
        echo "Shells目录: ${SHELLS_DIR}"
        echo "工具目录: ${UTILS_DIR}"
        
        # 确保加载apply_service.sh
        echo "尝试从${UTILS_DIR}/apply_service.sh加载apply_service函数..."
        if [ -f "${UTILS_DIR}/apply_service.sh" ]; then
            source "${UTILS_DIR}/apply_service.sh"
            if command -v apply_service &>/dev/null; then
                echo "apply_service函数加载成功"
            else
                echo "${RED}错误：apply_service函数加载失败，但文件存在${NC}"
                echo "${RED}将尝试直接执行apply_service.sh脚本${NC}"
                bash "${UTILS_DIR}/apply_service.sh" "$YML_FILE_PATH" "$SERVICE_NAME" "$TEST_SCRIPT_PATH" "oa-llm" "true" "${UTILS_DIR}"
                echo
                echo -e "${GREEN}服务部署操作完成，按任意键返回...${NC}"
                read -n 1
                return
            fi
        else
            echo "${RED}错误：找不到apply_service.sh (路径: ${UTILS_DIR}/apply_service.sh)${NC}"
            echo "${RED}请确保工具脚本已正确安装${NC}"
            echo
            echo -e "${GREEN}按任意键返回...${NC}"
            read -n 1
            return
        fi
        
        # 调用apply_service函数部署服务
        echo -e "${BLUE}启动服务 ${SERVICE_NAME} 的部署流程...${NC}"
        apply_service "$YML_FILE_PATH" "$SERVICE_NAME" "$TEST_SCRIPT_PATH" "oa-llm" "true" "${UTILS_DIR}"
        
        echo
        echo -e "${GREEN}服务 ${SERVICE_NAME} 部署完成，按任意键返回...${NC}"
        read -n 1
    elif [ "$choice" = "0" ]; then
        return
    else
        echo -e "${RED}无效选择，请重试${NC}"
        sleep 1
        select_service_to_deploy
    fi
}

# 部署Nginx反向代理
deploy_nginx_reverse_proxy() {
    clear_screen
    show_title
    echo -e "${BLUE}部署/更新Nginx反向代理...${NC}"
    
    bash "${SHELLS_DIR}/proxy-port-nginx.sh"
    
    echo
    echo -e "${GREEN}Nginx反向代理部署完成，按任意键返回...${NC}"
    read -n 1
}

# 一键部署所有服务并配置Nginx
deploy_all_with_nginx() {
    clear_screen
    show_title
    echo -e "${BLUE}一键部署所有服务并配置Nginx${NC}"
    echo
    
    read -p "此操作将部署所有服务并配置Nginx反向代理，确认继续? (yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        echo -e "${YELLOW}操作已取消${NC}"
        echo
        echo -e "${GREEN}按任意键返回...${NC}"
        read -n 1
        return
    fi
    
    # 部署所有服务
    echo -e "${BLUE}步骤1: 部署所有服务...${NC}"
    deploy_all_services
    
    # 配置Nginx反向代理
    echo -e "${BLUE}步骤2: 配置Nginx反向代理...${NC}"
    bash "${SHELLS_DIR}/proxy-port-nginx.sh"
    
    echo
    echo -e "${GREEN}所有服务和Nginx反向代理部署完成!${NC}"
    echo
    
    # 显示访问指南
    CONFIG_FILE="${SHELLS_DIR}/config.json"
    if [ -f "${CONFIG_FILE}" ]; then
        REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})
        NGINX_PORT=$(jq -r '.["proxy-config"]["nginx-config"]["port"]' ${CONFIG_FILE})
        DOMAIN_SUFFIX=$(jq -r '.["proxy-config"]["nginx-config"]["service-name-suffix"]' ${CONFIG_FILE})
        
        # 检查是否为空，设置默认值
        [[ "$NGINX_PORT" == "null" ]] && NGINX_PORT="30001"
        [[ "$DOMAIN_SUFFIX" == "null" ]] && DOMAIN_SUFFIX="oallm.jtws.com"
        
        # 获取节点IP
        NODE_IP=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
        
        echo -e "${YELLOW}访问信息:${NC}"
        echo -e "Nginx端口: ${NGINX_PORT}"
        echo -e "域名后缀: ${DOMAIN_SUFFIX}"
        echo -e "节点IP: ${NODE_IP}"
        echo
        
        echo -e "${YELLOW}各服务访问地址:${NC}"
        for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
            SERVICE_NAME=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
            echo -e "${SERVICE_NAME}: http://${SERVICE_NAME}.${DOMAIN_SUFFIX}:${NGINX_PORT}"
        done
        
        echo
        echo -e "${YELLOW}hosts文件配置示例:${NC}"
        echo -e "${NODE_IP} *.${DOMAIN_SUFFIX}"
    fi
    
    echo
    echo -e "${GREEN}按任意键返回...${NC}"
    read -n 1
}

# 主函数
main() {
    while true; do
        show_deploy_menu
        read -p "" choice
        
        case $choice in
            1)
                # 部署所有服务
                deploy_all_services
                ;;
            2)
                # 选择单个服务部署
                select_service_to_deploy
                ;;
            3)
                # 部署/更新Nginx反向代理
                deploy_nginx_reverse_proxy
                ;;
            4)
                # 一键部署所有服务并配置Nginx
                deploy_all_with_nginx
                ;;
            9)
                # 返回主菜单
                return
                ;;
            0)
                # 退出程序
                clear_screen
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重试${NC}"
                sleep 1
                ;;
        esac
    done
}

# 如果是直接执行此脚本，则启动主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main
fi
