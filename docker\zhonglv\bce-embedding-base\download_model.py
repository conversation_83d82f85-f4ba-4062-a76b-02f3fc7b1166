#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
from transformers import AutoTokenizer, AutoModel

def download_model():
    """
    从ModelScope下载bce-embedding-base_v1模型
    """
    print("开始下载bce-embedding-base_v1模型...")
    
    # 设置模型保存路径
    model_path = "./bce-embedding-base_v1"
    if not os.path.exists(model_path):
        os.makedirs(model_path)
    
    try:
        # 从ModelScope下载模型和分词器
        model_id = "maidalun/bce-embedding-base_v1"
        
        print(f"下载tokenizer: {model_id}")
        tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
        tokenizer.save_pretrained(model_path)
        
        print(f"下载model: {model_id}")
        model = AutoModel.from_pretrained(model_id, trust_remote_code=True)
        model.save_pretrained(model_path)
        
        print(f"模型成功下载到 {os.path.abspath(model_path)}")
        return True
    except Exception as e:
        print(f"下载模型时出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始准备bce-embedding-base模型...")
    success = download_model()
    
    if success:
        print("模型下载成功，现在可以启动服务了!")
        print("启动服务命令: python app.py")
    else:
        print("模型下载失败，请检查网络连接或手动下载模型。")
        print("模型地址: https://modelscope.cn/models/maidalun/bce-embedding-base_v1")
        sys.exit(1) 