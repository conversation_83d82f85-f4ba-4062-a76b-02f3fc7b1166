#!/bin/bash
# port_forward_utils.sh - 端口转发工具函数库
# 提供端口转发相关的核心功能，可被其他脚本调用

# 获取本机IP (更可靠方式)
get_local_ip() {
    hostname -I | awk '{print $1}'
}

# 检查端口是否已被占用
check_port_availability() {
    local port=$1
    if netstat -tuln | grep -q ":${port}"; then
        return 1  # 端口被占用
    else
        return 0  # 端口可用
    fi
}

# 启动单个服务的端口转发
# 参数: 服务名 目标端口 代理端口 命名空间
start_single_port_forward() {
    local service_name=$1
    local target_port=$2
    local proxy_port=$3
    local namespace=${4:-"oa-llm"}
    local address=${5:-"0.0.0.0"}
    
    # 检查参数
    if [ -z "$service_name" ] || [ -z "$target_port" ] || [ -z "$proxy_port" ]; then
        echo "错误: 缺少必要参数 (服务名、目标端口或代理端口)"
        return 1
    fi
    
    # 检查端口是否已被占用
    if ! check_port_availability $proxy_port; then
        echo "警告: 端口 ${proxy_port} 已被占用，跳过 ${service_name}"
        return 1
    fi
    
    # 获取本机IP
    local local_ip=$(get_local_ip)
    
    # 启动端口转发
    echo "将内部地址映射到K8s集群外: ${service_name}:${target_port} -> ${local_ip}:${proxy_port} "
    echo "kubectl port-forward svc/${service_name} ${proxy_port}:${target_port} -n ${namespace} --address=${address}"
    kubectl port-forward svc/${service_name} ${proxy_port}:${target_port} -n ${namespace} --address=${address} &
    
    # 返回进程ID
    echo $!
}

# 从配置文件启动所有服务的端口转发
# 参数: 配置文件路径 命名空间 YAML文件基础路径
start_all_port_forwards_from_config() {
    local config_file=$1
    local namespace=${2:-"oa-llm"}
    local yaml_base_path=${3:-""}
    local pids=()
    
    # 检查配置文件
    if [ ! -f "$config_file" ]; then
        echo "错误: 配置文件不存在 ${config_file}"
        return 1
    fi
    
    # 检查依赖
    command -v jq >/dev/null 2>&1 || { echo "错误: 需要安装jq"; return 1; }
    command -v kubectl >/dev/null 2>&1 || { echo "错误: 需要安装kubectl"; return 1; }
    
    # 读取配置
    local replace_config_count=$(jq '.["replace-config"] | length' ${config_file})
    
    # 如果没有提供YAML基础路径，尝试计算一个
    if [ -z "$yaml_base_path" ]; then
        # 计算k8s目录的路径
        local k8s_dir=$(dirname $(dirname "$config_file"))
        yaml_base_path="${k8s_dir}/yml"
        echo "未提供YAML基础路径，使用计算值: ${yaml_base_path}"
    else
        echo "使用提供的YAML基础路径: ${yaml_base_path}"
    fi
    
    # 循环处理每个服务
    for (( i=0; i<${replace_config_count}; i++ )); do
        # 获取配置
        local service_name=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${config_file})
        local yml_file_name=$(jq -r ".[\"replace-config\"][$i][\"yml-file-name\"]" ${config_file})
        local type=$(jq -r ".[\"replace-config\"][$i][\"type\"]" ${config_file})
        local proxy_port=$(jq -r ".[\"replace-config\"][$i][\"proxy-port\"]" ${config_file})
        local proxy_enable=$(jq -r ".[\"replace-config\"][$i][\"proxy-enable\"]" ${config_file})
        if [ "${proxy_enable}" = "false" ]; then
            echo "跳过 ${service_name}: proxy-enable=${proxy_enable}"
            continue
        fi
        # 检查必要参数
        if [ "${proxy_port}" = "null" ] || [ -z "${proxy_port}" ]; then
            echo "跳过 ${service_name}: 未配置proxy-port"
            continue
        fi


        echo "正在为${service_name}启动端口转发: ${proxy_port}"
        # 尝试可能的YAML文件位置
        local yml_locations=(
            "${yaml_base_path}/template/${type}/${yml_file_name}"
            "${yaml_base_path}/work/${type}/${yml_file_name}"
        )
        
        local yml_file_path=""
        for location in "${yml_locations[@]}"; do
            echo "尝试查找YAML文件: ${location}"
            if [ -f "${location}" ]; then
                yml_file_path="${location}"
                echo "找到YAML文件: ${yml_file_path}"
                break
            fi
        done
        
        if [ -z "$yml_file_path" ]; then
            echo "警告: 无法找到服务 ${service_name} 的YAML文件"
            continue
        fi
        
        # 获取targetPort
        local target_port=$(grep -A5 "ports:" ${yml_file_path} | grep "targetPort:" | head -1 | awk '{print $2}' | tr -d '[:space:]')
        if [ -z "${target_port}" ]; then
            # 如果targetPort不存在，尝试获取port字段
            local tmp_value=$(grep -A5 "ports:" ${yml_file_path} | grep "port:" )
            # 如果tmp中包含port:，则取port:后面的值
            echo "tmp_value: ${tmp_value}"
            # tmp_value 的格式为 - port: 8080 或者 port: 8080
            # 需要取8080
            # 使用awk提取8080
            echo "echo \"${tmp_value}\" | awk -F'port: ' '{print $2}' | tr -d '[:space:]'"
            target_port=$(echo "${tmp_value}" | awk -F'port: ' '{print $2}' | tr -d '[:space:]')
         
        fi
        
        # 确保端口是纯数字
        if ! [[ "$target_port" =~ ^[0-9]+$ ]]; then
            echo "警告: ${service_name}服务的端口格式不正确: ${target_port}"
            echo "尝试清理端口值..."
            target_port=$(echo "$target_port" | sed 's/[^0-9]//g')
            if [ -z "${target_port}" ] || ! [[ "$target_port" =~ ^[0-9]+$ ]]; then
                echo "错误: 无法获取有效的端口号，跳过${service_name}"
                continue
            else
                echo "已修正端口值为: ${target_port}"
            fi
        fi
        
        # 启动端口转发
        echo "准备启动端口转发: ${service_name} (${target_port} -> ${proxy_port})"
        start_single_port_forward "$service_name" "$target_port" "$proxy_port" "$namespace"
        echo "启动端口转发: ${service_name} (${target_port} -> ${proxy_port}) 完成"
    done
    
    if [ ${#pids[@]} -eq 0 ]; then
        echo "没有启动任何端口转发进程"
        return 1
    fi
    
    # 返回所有进程ID，用空格分隔
    echo "${pids[*]}"
}

# 如果是直接运行此脚本而非导入函数，显示帮助信息
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "此脚本提供端口转发工具函数，不应直接运行。"
    echo "请在其他脚本中导入此文件使用函数。"
    echo "例如: source ./utils/port_forward_utils.sh"
    echo
    echo "可用函数:"
    echo "  start_single_port_forward <服务名> <目标端口> <代理端口> [命名空间] [监听地址]"
    echo "  start_all_port_forwards_from_config <配置文件路径> [命名空间] [YAML文件基础路径]"
    echo "  check_port_availability <端口>"
    echo "  get_local_ip"
fi 