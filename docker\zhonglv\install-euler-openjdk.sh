pipeline {
    agent any

    // 定义参数化构建的参数
    parameters {
        // 允许上传多个 Docker tar 包
        file(name: 'docker_tar_files', description: '上传一个或多个 Docker tar 包')

        // 动态指定目标主机 IP 地址列表（用逗号分隔）
        string(name: 'TARGET_HOSTS', defaultValue: '*************,*************,*************', description: '目标主机 IP 地址列表')

        // 动态指定 SSH 凭据 ID
        string(name: 'SSH_CREDENTIAL_ID', defaultValue: '8b67ae8c-954e-4887-836f-b43ceb907873', description: 'SSH 凭据 ID')
    }

    stages {
        // 阶段1：解压上传的 tar 包并获取文件名
        stage('Extract Tar Files') {
            steps {
                script {
                    // 创建临时目录存储上传的 tar 文件
                    sh "mkdir -p docker-tars && unzip ${params.docker_tar_files} -d docker-tars/"

                    // 获取所有上传的 tar 文件名
                    def tarFiles = findFiles(glob: 'docker-tars/*.tar')
                    env.TAR_FILES = tarFiles.collect { it.name }.join(',')
                    echo "上传的 tar 文件为：${env.TAR_FILES}"
                }
            }
        }

        // 阶段2：推送到目标主机
        stage('Push to Target Hosts') {
            steps {
                script {
                    // 将目标主机 IP 地址字符串拆分为数组
                    def targetHosts = params.TARGET_HOSTS.split(',')

                    // 遍历每个目标主机
                    targetHosts.each { host ->
                        echo "正在处理主机：${host}"

                        // 使用 SSH Publisher 插件推送 tar 文件到目标主机的用户默认目录
                        sshPublisher(
                            configName: params.SSH_CREDENTIAL_ID, // 使用动态输入的凭据 ID
                            transfers: [
                                sshTransfer(
                                    sourceFiles: 'docker-tars/*.tar', // 源文件路径
                                    removePrefix: 'docker-tars/',     // 移除前缀
                                    remoteDirectory: ''               // 默认推送到用户主目录
                                )
                            ],
                            usePromotionTimestamp: false,
                            verbose: true
                        )

                        // 在目标主机上执行命令
                        sshCommand remote: params.SSH_CREDENTIAL_ID, command: """
                            # 创建 /data/images 目录
                            sudo mkdir -p /data/images
                            
                            # 将 tar 文件移动到 /data/images 目录
                            for tarFile in *.tar; do
                                sudo mv "\$tarFile" /data/images/
                            done
                            
                            # 加载所有的 tar 文件
                            cd /data/images
                            for tarFile in *.tar; do
                                sudo docker load -i "\$tarFile"
                            done
                        """, host: host
                    }
                }
            }
        }
    }

    // 后置操作：清理工作区
    post {
        always {
            cleanWs()
        }
    }
}