# Shell菜单设计文档

## 1. 概述

本文档描述了九天.文思系统部署的Shell菜单界面设计方案。该菜单系统通过SSH终端提供交互式界面，使用户能够按顺序执行部署、验证和维护操作，为系统部署和维护提供更友好和可靠的操作体验。

**技术架构说明**：本方案采用混合技术架构。**核心的数据处理、配置生成、远程部署脚本和菜单生成任务由Python实现**（位于`menu-scripts`目录），以利用其强大的数据处理和模板引擎能力。**本地管理菜单和与用户的交互界面则由Shell脚本实现**，以提供直观的命令行操作体验。远程主机上执行的也是由Python生成的Shell脚本和菜单。

<img src="./架构图/部署架构图-shell菜单功能框架.png">

## 2. 预处理流程设计

部署流程分为两个主要阶段：本地预处理阶段和远程部署阶段。预处理阶段在本地执行，生成所需的部署包和脚本；远程部署阶段在目标主机上执行，实际安装和配置各个组件。

### 2.1 预处理阶段流程
<img src="./架构图/部署架构图-预处理.png">

预处理阶段包含以下步骤（核心逻辑由Python实现）：

1.  **解压部署基础包**
    *   解压jiutian-wensi.tar获取基础文件
    *   验证解压后文件的完整性
    *   整理文件结构，准备后续处理
2.  **读取部署规划表 (Python)**
    *   解析规划CSV文件，获取部署主机信息和组件分配
    *   为每个主机创建组件分配清单
    *   提取每个组件的配置参数
    *   生成主机映射关系 (JSON格式)
    *   获取主机登录凭据(用户名、密码)
3.  **生成部署脚本 (Python)**
    *   根据规划表和模板为每个组件生成定制化的Shell部署脚本 (`deploy_*.sh`)
    *   将配置参数嵌入脚本中
    *   为每个组件生成验证脚本 (可选)
4.  **打包应用部署文件 (Python/Shell)**
    *   为每个组件生成最终部署tar包
    *   将配置文件、脚本、镜像文件打包
    *   为每个tar生成校验和
5.  **为每台主机生成远程管理菜单 (Python)**
    *   根据规划表，为每台主机生成定制化Shell菜单
    *   每台主机的菜单只包含需要在该主机上部署的组件
    *   嵌入部署状态检查机制
6.  **生成部署计划 (Python)**
    *   为每台主机创建完整的部署步骤清单
    *   生成组件依赖关系图
    *   设计最优部署顺序

### 2.2 规划表解析与主机组件映射

规划表（CSV格式）包含以下信息：
-   host_group：主机组（如middleware, apps, models, frontend）
-   app_name：应用名称
-   module_name：模块名称
-   ip_address：IP地址
-   hostname：主机名
-   base_dir：基础目录
-   description：描述
-   is_enabled：是否启用
-   deploy_order：部署顺序
-   depends_on：依赖组件
-   username：登录用户名
-   password：登录密码

规划表示例：
```csv
host_group,app_name,module_name,ip_address,hostname,base_dir,description,is_enabled,deploy_order,depends_on,username,password
middleware,redis,middleware,********,redis-server,/data,Redis缓存服务,true,10,,root,Abcd1234
middleware,postgresql,middleware,*********,postgres-server,/data,PostgreSQL数据库,true,20,,postgres,Pg@1234
middleware,elasticsearch,middleware,**********,es-server,/data,Elasticsearch搜索引擎,true,50,,elastic,Es@1234
apps,aiknowledge-controller,znwd,**********,znwd-aiknowledge,/data,AI知识控制器,true,205,"elasticsearch;opensearch;embedding-service-bge-m3",appuser,App@1234
frontend,web_assistant,frontend,**********,znwd-frontend,/data,智能问答前端,true,400,"rbac;qa",webuser,Web@1234
```

解析规划表后，系统会创建主机和组件的映射关系（由Python生成，例如`output/host_mapping.json`）：
```json
{
  "hosts": [
    {
      "hostname": "redis-server",
      "ip": "********",
      "host_group": "middleware",
      "base_dir": "/data",
      "username": "root",
      "password": "Abcd1234",
      "components": [
        {
          "app_name": "redis",
          "module_name": "middleware",
          "description": "Redis缓存服务",
          "deploy_order": 10,
          "depends_on": []
        }
      ]
    },
    {
      "hostname": "postgres-server",
      "ip": "*********",
      "host_group": "middleware",
      "base_dir": "/data",
      "username": "postgres",
      "password": "Pg@1234",
      "components": [
        {
          "app_name": "postgresql",
          "module_name": "middleware",
          "description": "PostgreSQL数据库",
          "deploy_order": 20,
          "depends_on": []
        }
      ]
    }
    // ... 其他主机
  ]
}
```

### 2.3 预处理脚本设计

```mermaid
sequenceDiagram
    participant 本地菜单 as menu.sh
    participant 预处理脚本 as preprocess.py
    participant 解析器 as parser.py
    participant 生成器 as generator.py
    participant 打包工具 as packager.py
    
    本地菜单->>预处理脚本: 调用(传递CSV路径)
    预处理脚本->>解析器: 传递CSV规划表
    解析器-->>预处理脚本: 返回主机组件映射(JSON)
    预处理脚本->>生成器: 传递主机组件映射
    生成器-->>预处理脚本: 生成Shell部署脚本和菜单
    预处理脚本->>打包工具: 传递配置和脚本
    打包工具-->>预处理脚本: 返回部署包路径
    预处理脚本-->>本地菜单: 完成通知
```

预处理的核心逻辑位于 `menu-scripts/preprocess.py`，由Python实现，负责解析规划表、生成配置文件以及远程部署所需的Shell脚本和菜单。该脚本集成了CSV解析器(`menu-scripts/lib/parser.py`)、脚本/菜单生成器(`menu-scripts/lib/generator.py`)和打包工具(`menu-scripts/lib/packager.py`)。

### 2.4 部署工作流程

完整的部署工作流程如下：

1.  **预处理阶段（本地执行）**
    *   运行本地管理菜单（`menu.sh`），选择预处理选项。
    *   本地菜单触发Python预处理脚本 (`menu-scripts/preprocess.py`)。
    *   Python脚本解析规划表CSV，生成主机映射JSON、配置文件、以及目标主机所需的Shell部署脚本和Shell菜单脚本，并将其存放在`output`目录。
    *   Python脚本根据需要打包应用文件和生成的脚本。
2.  **分发阶段 (本地Shell菜单驱动)**
    *   在本地管理菜单（`menu.sh`）中选择分发选项。
    *   根据`output/host_mapping.json`中的主机信息，使用scp/ssh（通过Shell库函数`lib/shell/remote.sh`）将对应的部署包（包含生成的Shell脚本、菜单、配置等）传送到各目标主机。
    *   只传送该主机需要的组件，避免冗余。
    *   验证传输完整性。
    *   在目标主机上解压部署包。
3.  **远程部署阶段（目标主机执行Shell脚本）**
    *   通过本地管理菜单（`menu.sh`）连接到目标主机。
    *   在目标主机上启动由Python生成的定制化Shell菜单 (`deploy_menu.sh`)。
    *   按照菜单指引，执行由Python生成的对应组件的Shell部署脚本 (`deploy_*.sh`)。
4.  **验证阶段 (执行Shell脚本)**
    *   通过远程Shell菜单触发执行验证脚本。
    *   验证各组件的部署状态。
    *   执行特定于该主机的系统集成测试。
    *   确认系统正常运行。

### 2.5 本地管理菜单设计 (Shell实现)

本地管理菜单（`menu.sh`）使用Shell脚本实现，用于执行预处理触发、分发和远程连接等操作，界面设计如下：
**(注：选择菜单中的预处理相关选项会调用对应的Python脚本)**

```
=================================================
       九天.文思系统本地管理菜单 v1.0
=================================================
  1. 解压部署基础包
  2. 解析部署规划表 (触发 menu-scripts/preprocess.py)
  3. 生成部署脚本   (触发 menu-scripts/deploy_generator.py)
  4. 打包应用部署文件 (触发 menu-scripts/packager.py)
  5. 生成主机定制菜单 (触发 menu-scripts/menu_generator.py)
  6. 生成部署计划
  7. 执行完整预处理 (触发 menu-scripts/preprocess.py)
  8. 分发部署包到目标主机 (Shell执行)
  9. 连接到目标主机 (Shell执行)
  10. 退出
=================================================
请输入选择 [1-10]:
```

选择"分发部署包到目标主机"时，会显示以下子菜单（由Shell脚本读取`output/host_mapping.json`动态生成）：

```
=================================================
       部署包分发菜单
=================================================
  1. redis-server (********) - middleware
  2. postgres-server (*********) - middleware
  3. es-server (**********) - middleware
  4. znwd-aiknowledge (**********) - apps
  5. znwd-frontend (**********) - frontend
  6. 所有主机
  7. 返回上级菜单
=================================================
请选择目标主机 [1-7]:
```

选择特定主机后，本地Shell脚本会根据规划表中的登录信息自动连接并传输：

```
=================================================
       正在分发到 redis-server
=================================================
  - 使用用户 root 连接到 ********...  [成功]
  - 分发菜单脚本和基础库...          [成功]
  - 分发Redis组件包...              [成功]
=================================================
分发完成！
按任意键继续...
```

选择"连接到目标主机"时，会显示类似的主机列表，选择后将自动使用规划表中的用户名和密码进行SSH连接，并启动目标主机上由Python生成的Shell菜单：

```
=================================================
       连接目标主机菜单
=================================================
  1. redis-server (********) - middleware
  2. postgres-server (*********) - middleware
  3. es-server (**********) - middleware
  4. znwd-aiknowledge (**********) - apps
  5. znwd-frontend (**********) - frontend
  6. 返回上级菜单
=================================================
请选择目标主机 [1-6]:
```

### 2.6 部署脚本生成 (Python生成Shell脚本)

针对每个应用组件，Python脚本 (`menu-scripts/deploy_generator.py`) 会根据Shell模板 (`templates/shell/`) 生成定制化的Shell部署脚本。

**Python生成逻辑示例:**
```python
# (位于 menu-scripts/deploy_generator.py)
from jinja2 import Environment, FileSystemLoader

env = Environment(loader=FileSystemLoader('templates/shell'))

def generate_redis_deploy_script(host_config):
    template = env.get_template('component/redis.sh.j2') # 假设有redis模板
    script_content = template.render(
        APP_NAME="redis",
        VERSION="6.2.6",
        PORT=6379,
        BASE_DIR=host_config['base_dir'],
        PASSWORD="redis_password", # 密码应从配置或安全方式获取
        MAX_MEMORY="1g"
    )
    # 将 script_content 写入 output/deploy_scripts/deploy_redis.sh
    # ... (文件写入和权限设置逻辑) ...
```

**生成的Shell部署脚本示例 (`output/deploy_scripts/deploy_redis.sh`)**:
```bash
#!/bin/bash
# 自动生成的Redis部署脚本 (由Python生成)
# 生成时间: $(date)
# 适用主机: redis-server (********)

# 加载通用函数 (远程主机上的)
source ./lib/common.sh

# 配置参数 (由Python渲染)
APP_NAME="redis"
VERSION="6.2.6"
PORT=6379
BASE_DIR="/data"
PASSWORD="redis_password"
MAX_MEMORY="1g"

# 记录部署开始
log_info "开始部署 ${APP_NAME} ${VERSION}..."

# 步骤1: 检查部署条件
print_step "检查部署条件"
check_docker || exit 1
check_port ${PORT} || exit 1
check_disk_space ${BASE_DIR} 500 || exit 1

# 步骤2: 创建目录结构
print_step "创建目录结构"
mkdir -p ${BASE_DIR}/${APP_NAME}/{data,logs,config}
chown -R 1000:1000 ${BASE_DIR}/${APP_NAME}

# 步骤3: 准备配置文件 (配置文件也可能由Python生成并打包)
print_step "准备配置文件"
# 假设配置文件模板已存在于远程主机
cp ./templates/redis.conf.template ${BASE_DIR}/${APP_NAME}/config/redis.conf
sed -i "s/{{PORT}}/${PORT}/g" ${BASE_DIR}/${APP_NAME}/config/redis.conf
sed -i "s/{{PASSWORD}}/${PASSWORD}/g" ${BASE_DIR}/${APP_NAME}/config/redis.conf
sed -i "s/{{MAX_MEMORY}}/${MAX_MEMORY}/g" ${BASE_DIR}/${APP_NAME}/config/redis.conf

# 步骤4: 加载Docker镜像 (镜像文件需提前分发)
print_step "加载Docker镜像"
load_image "redis" "${VERSION}" || exit 1

# 步骤5: 创建和启动容器
print_step "创建和启动容器"
stop_container "redis" || true
docker run -d \
  --name redis \
  --restart always \
  -p ${PORT}:6379 \
  -v ${BASE_DIR}/${APP_NAME}/data:/data \
  -v ${BASE_DIR}/${APP_NAME}/config/redis.conf:/etc/redis/redis.conf \
  -v ${BASE_DIR}/${APP_NAME}/logs:/var/log/redis \
  redis:${VERSION} redis-server /etc/redis/redis.conf

# 步骤6: 验证部署
print_step "验证部署"
sleep 3
if ! docker ps | grep -q "redis"; then
  log_error "Redis容器未正常运行"
  exit 1
fi

# 测试连接
if ! docker exec redis redis-cli -a ${PASSWORD} ping | grep -q "PONG"; then
  log_error "Redis连接测试失败"
  exit 1
fi

# 记录部署成功
log_success "${APP_NAME} ${VERSION} 部署成功!"
update_deploy_status "${APP_NAME}" "deployed" # 状态更新可能需要更复杂的机制

# 完成提示
print_complete "Redis部署"
```

### 2.7 主机专属菜单生成 (Python生成Shell脚本)

Python脚本 (`menu-scripts/menu_generator.py`) 会为每台主机生成专属的Shell菜单脚本，只包含该主机需要部署的组件。

**Python生成逻辑示例:**
```python
# (位于 menu-scripts/menu_generator.py)
from jinja2 import Environment, FileSystemLoader

env = Environment(loader=FileSystemLoader('templates/shell'))

def generate_host_menu(host_config):
    template = env.get_template('menu_template.sh.j2') # 菜单模板
    components = [comp['app_name'] for comp in host_config['components']]
    script_content = template.render(
        hostname=host_config['hostname'],
        ip_address=host_config['ip_address'],
        components=components
        # ... 其他需要的变量 ...
    )
    # 将 script_content 写入 output/host_menus/<hostname>.menu.sh
    # ... (文件写入和权限设置逻辑) ...
```

**生成的Shell菜单脚本示例 (`output/host_menus/es-server.menu.sh`)**:
```bash
#!/bin/bash
# 自动生成的主机菜单 - es-server (由Python生成)
# 生成时间: $(date)

# 加载通用函数 (远程主机上的)
source ./lib/common.sh
source ./lib/ui.sh

# 主机信息
HOSTNAME="es-server"
IP_ADDRESS="**********"
HOST_GROUP="middleware"
BASE_DIR="/data"

# 部署状态
declare -A DEPLOY_STATUS

# 加载状态信息 (状态文件需随包分发或远程获取)
load_status

# 主菜单函数
show_main_menu() {
  clear
  print_header "九天.文思系统部署菜单 - ${HOSTNAME}"
  
  # 只显示该主机需要部署的组件 (由Python渲染)
  print_menu_item 1 "Elasticsearch部署" ${DEPLOY_STATUS["elasticsearch"]}
  print_menu_item 2 "Kibana部署" ${DEPLOY_STATUS["kibana"]}
  print_menu_item 3 "Nginx部署" ${DEPLOY_STATUS["nginx"]}
  # ... 其他通用菜单项 ...
  print_menu_item 4 "环境检查"
  print_menu_item 5 "查看部署状态"
  print_menu_item 6 "查看部署日志"
  print_menu_item 7 "系统维护"
  print_menu_item 8 "退出"
  
  print_footer "${HOSTNAME} (${IP_ADDRESS})"
  read -p "请输入选择 [1-8]: " choice
  
  case ${choice} in
    1) deploy_component "elasticsearch" ;;
    2) deploy_component "kibana" ;;
    3) deploy_component "nginx" ;;
    4) check_environment ;;
    5) show_status ;;
    6) show_logs ;;
    7) show_maintenance_menu ;;
    8) exit 0 ;;
    *) echo "无效选择，请重试" ; sleep 1 ;;
  esac
  
  show_main_menu
}

# 部署组件函数 (调用对应的deploy_*.sh脚本)
deploy_component() {
  local component=$1
  
  # 检查是否已部署
  if [ "${DEPLOY_STATUS[$component]}" == "deployed" ]; then
    read -p "$component 已部署，是否重新部署? (y/n): " confirm
    if [ "$confirm" != "y" ]; then
      return
    fi
  fi
  
  # 检查依赖 (依赖关系由Python生成到脚本中)
  check_dependencies "$component" || return
  
  # 执行部署脚本
  if [ -f "./deploy_${component}.sh" ]; then
    bash "./deploy_${component}.sh"
  else
    log_error "找不到部署脚本: ./deploy_${component}.sh"
  fi
}

# ... (其他函数如 load_status, check_dependencies, show_status 等) ...

# 启动菜单
load_status
show_main_menu
```

## 3. 菜单架构

### 3.1 整体架构

菜单系统采用混合架构：
-   **本地管理菜单 (Shell)**：提供预处理触发、分发、远程连接等管理功能。
-   **远程部署菜单 (Python生成的Shell)**：在目标主机上运行，提供组件部署、验证、维护等操作。

### 3.2 菜单层次结构 (远程菜单)

远程目标主机上的菜单层次结构保持不变：
```
主菜单 (远程)
├── 1. 环境检查
├── 2. 基础设施部署
├── 3. 中间件部署
│   ├── 3.1 Redis部署
│   ├── 3.2 PostgreSQL部署
│   ... (根据规划表动态生成) ...
├── 4. 向量模型服务部署
│   ...
├── 5. 应用服务部署
│   ...
├── 6. 前端应用部署
├── 7. 系统验证
│   ...
├── 8. 系统维护
│   ...
└── 9. 退出
```

## 4. 菜单界面设计

### 4.1 本地管理菜单界面 (Shell)

本地管理菜单（`menu.sh`）界面设计保持不变，用于驱动整个流程：
```
=================================================
       九天.文思系统本地管理菜单 v1.0
=================================================
  1. 解压部署基础包
  2. 解析部署规划表 (触发 menu-scripts/preprocess.py)
  ...
  8. 分发部署包到目标主机 (Shell执行)
  9. 连接到目标主机 (Shell执行)
  10. 退出
=================================================
请输入选择 [1-10]:
```

### 4.2 远程子菜单界面 (Python生成的Shell)

目标主机上的子菜单界面（如中间件部署）设计保持不变，由Python生成的Shell脚本负责显示：
```
=================================================
       中间件部署菜单 (远程主机: es-server)
=================================================
  1. Elasticsearch部署     [未部署]
  2. Kibana部署          [未部署]
  3. Nginx部署             [未部署]
  ... (根据规划表动态生成) ...
  9. 返回主菜单
=================================================
请输入选择 [1-X]:
```

### 4.3 操作执行界面 (Python生成的Shell)

组件部署时的操作执行界面设计保持不变，由Python生成的`deploy_*.sh`脚本负责输出：
```
=================================================
       Redis部署 (远程主机: redis-server)
=================================================
准备部署Redis...

步骤 1/6: 检查部署条件...
  - 检查Docker状态        [成功]
  ...

Redis部署完成！

按任意键返回菜单...
```

## 5. 目录结构设计 (混合架构)

为支持混合技术方案，推荐如下目录结构：
```
jiutian-deploy/
├── menu.sh                    # 本地Shell菜单入口脚本
│
├── menu-scripts/              # Python脚本目录
│   ├── preprocess.py          # 预处理主脚本(Python)
│   ├── deploy_generator.py    # 部署脚本生成器(Python)
│   ├── menu_generator.py      # 菜单生成器(Python)
│   └── lib/                   # Python库
│       ├── parser.py          # CSV解析器
│       ├── generator.py       # 脚本生成器基类/工具
│       ├── packager.py        # 打包工具
│       └── template.py        # 模板处理工具 (Jinja2等)
│
├── lib/                       # Shell库目录 (用于本地菜单和远程脚本)
│   ├── common.sh              # 通用函数库 (可本地/远程共用)
│   ├── ui.sh                  # 界面相关函数 (Shell菜单用)
│   ├── remote.sh              # 远程连接/分发函数 (本地菜单用)
│   └── validation.sh          # 验证相关函数 (远程脚本用)
│
├── templates/                 # 模板目录
│   ├── shell/                 # Shell模板 (供Python生成器使用)
│   │   ├── menu_template.sh.j2  # 远程菜单模板
│   │   ├── deploy_generic.sh.j2 # 通用部署脚本模板
│   │   └── component/         # 组件特定模板目录
│   │       ├── redis.sh.j2      # Redis部署模板
│   │       └── ...
│   └── config/                # 配置文件模板 (供Python生成器使用)
│       ├── redis.conf.j2        # Redis配置模板
│       └── ...
│
├── output/                    # 生成文件输出目录 (由Python脚本生成)
│   ├── host_mapping.json      # 主机映射关系
│   ├── host_configs/          # 生成的主机/组件配置文件
│   ├── deploy_scripts/        # 生成的Shell部署脚本
│   ├── host_menus/            # 生成的Shell主机菜单
│   └── packages/              # 生成的部署包
│
└── logs/                      # 日志目录
    ├── preprocess.log         # Python预处理日志
    ├── menu.log               # 本地Shell菜单日志
    └── remote/                # 远程部署日志 (按主机存放)
        ├── redis-server.log
        └── ...
```

## 6. 技术优势说明

采用Python和Shell混合的技术架构，可以充分利用两种语言的优势：

*   **Python**:
    *   **强大的数据处理能力**: 轻松解析CSV、处理JSON、管理复杂的数据结构。
    *   **优秀的模板引擎**: 使用Jinja2等库可以灵活、高效地生成Shell脚本和配置文件。
    *   **丰富的库支持**: 可以方便地集成其他Python库进行更复杂的操作。
    *   **更好的代码组织和可维护性**: 面向对象编程使得代码结构更清晰，易于扩展。
*   **Shell**:
    *   **系统交互直观**: 非常适合创建命令行交互菜单（本地管理菜单）。
    *   **系统命令调用方便**: 直接调用系统命令如`ssh`, `scp`, `docker`等非常方便。
    *   **部署简单**: 目标主机通常默认支持Shell，无需额外安装Python环境（用于执行*生成的*Shell脚本）。

这种结合使得数据处理和生成逻辑更健壮、可维护，同时保留了Shell在系统操作和交互上的便利性。

