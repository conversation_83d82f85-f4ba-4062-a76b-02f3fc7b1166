#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
主机规划表解析工具

用途：解析CSV格式的主机规划表，生成Ansible部署所需的配置文件。
作者：九天·文思团队
日期：2024-05-25
"""

import os
import sys
import csv
import json
import shutil
import argparse
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('host_planning_parser')

def parse_csv(csv_file):
    """解析CSV规划表文件"""
    hosts = []
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # 转换布尔值
                if 'is_enabled' in row:
                    row['is_enabled'] = row['is_enabled'].lower() == 'true'
                
                # 转换依赖关系为列表
                if 'depends_on' in row and row['depends_on']:
                    row['depends_on'] = [dep.strip() for dep in row['depends_on'].split(';')]
                else:
                    row['depends_on'] = []
                
                # 转换部署顺序为整数    
                if 'deploy_order' in row:
                    row['deploy_order'] = int(row['deploy_order'])
                    
                hosts.append(row)
        
        # 按部署顺序排序
        hosts.sort(key=lambda x: x.get('deploy_order', 999))
        return hosts
    
    except Exception as e:
        logger.error(f"解析CSV文件时出错: {str(e)}")
        return None

def load_template(app_name, host_group):
    """加载组件的JSON模板配置"""
    template_path = Path(f"ansible/inventory/host_configs/{host_group}/{app_name}.json")
    
    if not template_path.exists():
        logger.error(f"找不到模板文件: {template_path}")
        return None
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载模板文件时出错: {str(e)}")
        return None

def replace_placeholders(template, host_data):
    """替换模板中的占位符"""
    if not template:
        return None
    
    result = template.copy()
    
    # 将模板转换为字符串以便进行全局替换
    template_str = json.dumps(template)
    
    # 替换IP地址占位符
    if 'ip_address' in host_data:
        template_str = template_str.replace('"{{ip_address}}"', f'"{host_data["ip_address"]}"')
        template_str = template_str.replace('{{ip_address}}', host_data['ip_address'])
    
    # 替换基础目录占位符
    if 'base_dir' in host_data:
        template_str = template_str.replace('"{{base_dir}}"', f'"{host_data["base_dir"]}"')
        template_str = template_str.replace('{{base_dir}}', host_data['base_dir'])
    
    # 替换其他可能的占位符
    for key, value in host_data.items():
        if isinstance(value, str):
            template_str = template_str.replace(f'"{{{{key}}}}"', f'"{value}"')
            template_str = template_str.replace(f'{{{{key}}}}', value)
    
    # 将字符串转回JSON对象
    try:
        result = json.loads(template_str)
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {str(e)}")
        logger.debug(f"原始字符串: {template_str}")
        return template
    
    return result

def generate_inventory(hosts, output_dir):
    """生成Ansible清单文件"""
    inventory = {
        "all": {
            "children": {
                "middleware": {"hosts": {}},
                "models": {"hosts": {}},
                "apps": {"hosts": {}},
                "frontend": {"hosts": {}}
            }
        }
    }
    
    for host in hosts:
        if not host.get('is_enabled', True):
            continue
            
        host_group = host.get('host_group')
        hostname = host.get('hostname')
        
        if not hostname or not host_group:
            continue
            
        if host_group not in inventory["all"]["children"]:
            continue
            
        inventory["all"]["children"][host_group]["hosts"][hostname] = {
            "ansible_host": host.get('ip_address'),
            "app_name": host.get('app_name'),
            "module_name": host.get('module_name'),
            "base_dir": host.get('base_dir')
        }
    
    # 写入inventory文件
    with open(os.path.join(output_dir, 'inventory.yml'), 'w', encoding='utf-8') as f:
        f.write(json.dumps(inventory, indent=2))
    
    return True

def generate_configs(hosts, output_dir):
    """生成组件配置文件"""
    config_dir = os.path.join(output_dir, 'host_configs')
    os.makedirs(config_dir, exist_ok=True)
    
    for host in hosts:
        if not host.get('is_enabled', True):
            continue
            
        app_name = host.get('app_name')
        host_group = host.get('host_group')
        
        if not app_name or not host_group:
            continue
        
        # 加载模板
        template = load_template(app_name, host_group)
        if not template:
            continue
        
        # 替换占位符
        config = replace_placeholders(template, host)
        if not config:
            continue
        
        # 写入配置文件
        config_path = os.path.join(config_dir, f"{app_name}.json")
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(json.dumps(config, indent=2))
        
        logger.info(f"生成配置文件: {config_path}")
    
    return True

def generate_deployment_order(hosts, output_dir):
    """生成部署顺序文件"""
    deployment_order = []
    
    for host in hosts:
        if not host.get('is_enabled', True):
            continue
            
        deployment_order.append({
            "app_name": host.get('app_name'),
            "host_group": host.get('host_group'),
            "deploy_order": host.get('deploy_order', 999),
            "depends_on": host.get('depends_on', [])
        })
    
    # 写入部署顺序文件
    order_path = os.path.join(output_dir, 'deployment_order.json')
    with open(order_path, 'w', encoding='utf-8') as f:
        f.write(json.dumps(deployment_order, indent=2))
    
    return True

def validate_planning(hosts):
    """验证规划表的正确性"""
    # 检查必填字段
    required_fields = ['host_group', 'app_name', 'module_name', 'ip_address', 'base_dir']
    app_names = set()
    
    for i, host in enumerate(hosts):
        # 检查必填字段
        for field in required_fields:
            if field not in host or not host[field]:
                logger.error(f"第{i+2}行: 缺少必填字段 '{field}'")
                return False
        
        # 检查应用名称唯一性
        app_name = host.get('app_name')
        if app_name in app_names:
            logger.warning(f"第{i+2}行: 应用名称 '{app_name}' 重复")
        app_names.add(app_name)
        
        # 检查依赖关系
        for dep in host.get('depends_on', []):
            if not any(h.get('app_name') == dep for h in hosts):
                logger.warning(f"第{i+2}行: 应用 '{app_name}' 依赖 '{dep}'，但该依赖项不存在")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='主机规划表解析工具')
    parser.add_argument('csv_file', help='CSV格式的主机规划表文件路径')
    parser.add_argument('-o', '--output', default='output', help='输出目录路径')
    parser.add_argument('-v', '--validate', action='store_true', help='仅验证规划表格式')
    
    args = parser.parse_args()
    
    # 解析CSV文件
    hosts = parse_csv(args.csv_file)
    if not hosts:
        logger.error("解析CSV文件失败，请检查文件格式。")
        return 1
    
    # 验证规划表
    if not validate_planning(hosts):
        logger.error("规划表验证失败，请修正错误后重试。")
        return 1
    
    if args.validate:
        logger.info("规划表验证通过。")
        return 0
    
    # 创建输出目录
    output_dir = args.output
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成清单文件
    if not generate_inventory(hosts, output_dir):
        logger.error("生成清单文件失败。")
        return 1
    
    # 生成配置文件
    if not generate_configs(hosts, output_dir):
        logger.error("生成配置文件失败。")
        return 1
    
    # 生成部署顺序
    if not generate_deployment_order(hosts, output_dir):
        logger.error("生成部署顺序失败。")
        return 1
    
    logger.info(f"成功生成配置文件，输出目录: {output_dir}")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 