#!/bin/bash
echo "开始部署Filebeat"
# 改用7.x版本的Filebeat
IMAGE_VERSION=7.17.14
IMAGE_NAME=docker.elastic.co/beats/filebeat

ES_USERNAME="elastic"
ES_PASSWORD="Elastic20250417@#"

RBAC_LOG_DIR="/data/znwd/rbac/logs"
QA_LOG_DIR="/data/znwd/qa/logs"
AIKNOWLEDGE_LOG_DIR="/data/znwd/aiknowledge-controller/logs"
RAG_DOC_PARSER_LOG_DIR="/data/znwd/rag-doc-parser/logs"
RAG_QA_ONLINE_LOG_DIR="/data/znwd/rag-qa-online/logs"
QUERY_PARSER_LOG_DIR="/data/znwd/query-parser/logs"
RAG_KG_OS_LOG_DIR="/data/znwd/rag-kg-os/logs"

# 创建必要目录结构
echo "创建必要目录结构: mkdir -p /data/filebeat/config /data/filebeat/data /data/filebeat/logs"
mkdir -p /data/filebeat/config /data/filebeat/data /data/filebeat/logs

# 检查日志目录和文件权限
echo "检查日志目录和文件权限："
for logdir in "$RBAC_LOG_DIR" "$QA_LOG_DIR" "$AIKNOWLEDGE_LOG_DIR" "$RAG_DOC_PARSER_LOG_DIR" "$RAG_QA_ONLINE_LOG_DIR" "$QUERY_PARSER_LOG_DIR" "$RAG_KG_OS_LOG_DIR"; do
  if [ -d "$logdir" ]; then
    echo "目录 $logdir 存在"
    ls -la $logdir/
    # 确保filebeat可以读取日志文件
    chmod -R o+r $logdir/
    echo "已修改权限，确保日志文件可读"
  else
    echo "警告: 目录 $logdir 不存在，创建目录"
    mkdir -p $logdir
    chmod -R 755 $logdir
    echo "创建测试日志文件用于验证Filebeat配置"
    echo "2024-05-20 12:00:00 测试日志内容" > $logdir/test.log
    chmod 644 $logdir/test.log
  fi
done

echo "清理之前的Filebeat容器和数据"
docker rm -f filebeat 2>/dev/null || true
rm -rf /data/filebeat/data/*
rm -rf /data/filebeat/logs/*

echo "拉取Filebeat镜像: docker pull $IMAGE_NAME:$IMAGE_VERSION"
# 拉取Filebeat镜像
docker pull $IMAGE_NAME:$IMAGE_VERSION

# 创建Filebeat配置文件 - 使用7.x兼容的配置
cat > /data/filebeat/config/filebeat.yml <<EOF
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /data/znwd/rbac/logs/*.log
  fields:
    type: rbac
    index: rbac-logs
  multiline:
    pattern: '^\d{4}-\d{2}-\d{2}'  # 匹配任何年份格式的日期开头
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/znwd/qa/logs/*.log
  fields:
    type: qa
    index: qa-logs
  multiline:
    pattern: '^\d{4}-\d{2}-\d{2}'  # 匹配任何年份格式的日期开头
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/znwd/aiknowledge-controller/logs/*.log
  fields:
    type: aiknowledge
    index: aiknowledge-logs
  multiline:
    pattern: '^\d{4}-\d{2}-\d{2}'  # 匹配任何年份格式的日期开头
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/znwd/rag-doc-parser/logs/*.log
  fields:
    type: rag-doc-parser
    index: rag-doc-parser-logs
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'  # 匹配任何年份格式的日期开头
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/znwd/rag-qa-online/logs/service_log_*
    - /data/znwd/rag-qa-online/logs/service_log_info*
    - /data/znwd/rag-qa-online/logs/service_log_error*
  fields:
    type: rag-qa-online
    index: rag-qa-online-logs
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/znwd/query-parser/logs/service_log_*
  fields:
    type: query-parser
    index: query-parser-logs
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'  # 匹配 [YYYY-MM-DD HH:MM:SS] 格式
    negate: false
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/znwd/rag-kg-os/logs/*.log
  fields:
    type: rag-kg-os
    index: rag-kg-os-logs
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'  # 匹配任何年份格式的日期开头
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s


# 基础处理器配置
processors:
  - add_host_metadata: ~
  - add_docker_metadata: ~
  # 解码JSON字段
  - decode_json_fields:
      fields: ["message"]
      process_array: false
      max_depth: 1
      target: ""
      overwrite_keys: true
      when:
        contains:
          message: "{"

# 使用Elasticsearch输出
output.elasticsearch:
  hosts: ["http://**********:9200"]  # Elasticsearch地址
  username: "${ES_USERNAME}"
  password: "${ES_PASSWORD}"
  indices:
    - index: "rbac-%{+yyyy}"
      when.contains:
        fields.type: "rbac"
    - index: "qa-%{+yyyy}"
      when.contains:
        fields.type: "qa"
    - index: "aiknowledge-%{+yyyy}"
      when.contains:
        fields.type: "aiknowledge"
    - index: "rag-doc-parser-%{+yyyy}"
      when.contains:
        fields.type: "rag-doc-parser"
    - index: "rag-qa-online-%{+yyyy}"
      when.contains:
        fields.type: "rag-qa-online"
    - index: "query-parser-%{+yyyy}"
      when.contains:
        fields.type: "query-parser"
    - index: "rag-kg-os-%{+yyyy}"
      when.contains:
        fields.type: "rag-kg-os"
# 配置设置
setup.ilm.enabled: false
setup.template.enabled: false
setup.kibana.host: "http://**********:5601"
  
# 日志配置
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
EOF
echo "Filebeat配置文件创建完成: /data/filebeat/config/filebeat.yml"

# 启动Filebeat容器
echo "启动Filebeat容器"
docker run -d \
  --name=filebeat \
  --user=root \
  --volume="/data/filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro" \
  --volume="/data/filebeat/data:/usr/share/filebeat/data:rw" \
  --volume="/data/filebeat/logs:/var/log/filebeat:rw" \
  --volume="$RBAC_LOG_DIR:$RBAC_LOG_DIR:ro" \
  --volume="$QA_LOG_DIR:$QA_LOG_DIR:ro" \
  --volume="$AIKNOWLEDGE_LOG_DIR:$AIKNOWLEDGE_LOG_DIR:ro" \
  --volume="$RAG_DOC_PARSER_LOG_DIR:$RAG_DOC_PARSER_LOG_DIR:ro" \
  --volume="$RAG_QA_ONLINE_LOG_DIR:$RAG_QA_ONLINE_LOG_DIR:ro" \
  --volume="$QUERY_PARSER_LOG_DIR:$QUERY_PARSER_LOG_DIR:ro" \
  --volume="$RAG_KG_OS_LOG_DIR:$RAG_KG_OS_LOG_DIR:ro" \
  $IMAGE_NAME:$IMAGE_VERSION \
  filebeat -e --strict.perms=false

# 检查容器是否启动成功
echo "检查容器是否启动成功"
if docker ps | grep -q filebeat; then
  echo "Filebeat容器启动成功"
else
  echo "Filebeat容器启动失败"
  docker logs filebeat
  exit 1
fi

# 等待Filebeat启动并处理日志
echo "等待30秒，让Filebeat有足够时间处理日志..."
sleep 30

# 查看filebeat日志
echo "查看Filebeat日志："
docker logs filebeat | tail -n 50

# 检查索引是否创建成功
echo "查看所有索引："
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices?v"

# 检查特定索引是否存在
echo "检查特定索引是否存在："
current_year=$(date +"%Y")
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/rbac-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/qa-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/aiknowledge-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/rag-doc-parser-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/rag-qa-online-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/query-parser-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/rag-kg-os-$current_year?v"


echo "查看索引中的文档数："
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/rbac-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/qa-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/aiknowledge-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/rag-doc-parser-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/rag-qa-online-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/query-parser-$current_year?v"
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/rag-kg-os-$current_year?v"

echo "从ES查询1条日志,今天的索引"
echo "rbac索引:curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET \"http://**********:9200/rbac-$current_year/_search?q=message:*&size=1\""
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/rbac-$current_year/_search?q=message:*&size=1"
echo "--------------------------------"
echo "qa索引:curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET \"http://**********:9200/qa-$current_year/_search?q=message:*&size=1\""
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/qa-$current_year/_search?q=message:*&size=1"
echo "--------------------------------"
echo "aiknowledge索引:curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET \"http://**********:9200/aiknowledge-$current_year/_search?q=message:*&size=1\""
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/aiknowledge-$current_year/_search?q=message:*&size=1"
echo "--------------------------------"
echo "rag-doc-parser索引:curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET \"http://**********:9200/rag-doc-parser-$current_year/_search?q=message:*&size=1\""
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/rag-doc-parser-$current_year/_search?q=message:*&size=1"
echo "--------------------------------"
echo "rag-qa-online索引:curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET \"http://**********:9200/rag-qa-online-$current_year/_search?q=message:*&size=1\""
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/rag-qa-online-$current_year/_search?q=message:*&size=1"
echo "--------------------------------"
echo "query-parser索引:curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET \"http://**********:9200/query-parser-$current_year/_search?q=message:*&size=1\""
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/query-parser-$current_year/_search?q=message:*&size=1"
echo "--------------------------------"
echo "rag-kg-os索引:curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET \"http://**********:9200/rag-kg-os-$current_year/_search?q=message:*&size=1\""
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/rag-kg-os-$current_year/_search?q=message:*&size=1"
echo "--------------------------------"

echo "Filebeat部署完成"