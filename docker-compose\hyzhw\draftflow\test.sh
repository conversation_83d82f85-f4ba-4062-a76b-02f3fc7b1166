docker run -it --rm --net=host officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14 bash


docker run -it --rm --privileged=true   --net=host --shm-size=1g   --device=/dev/davinci_manager   --device=/dev/hisi_hdc   --device=/dev/devmm_svm   --device=/dev/davinci0   --device=/dev/davinci1   --device=/dev/davinci2   --device=/dev/davinci3   --device=/dev/davinci4   --device=/dev/davinci5   --device=/dev/davinci6   --device=/dev/davinci7   -v /usr/local/Ascend/driver:/usr/local/Ascend/driver:ro   -v /usr/local/sbin:/usr/local/sbin:ro   -v /home/<USER>/data/DeepSeek-R1-Distill-Llama-70B:ro   -v /data/deepseek70b/shells/start.sh:/usr/local/Ascend/mindie/latest/mindie-service/bin/start.sh   -v /data/deepseek70b/config/config.json:/usr/local/Ascend/mindie/latest/mindie-service/conf/config.json   swr.cn-south-1.myhuaweicloud.com/ascendhub/mindie:2.0.T3.1-800I-A2-py311-openeuler24.03-lts bash

docker rm -f deepseek70b

docker run -it -d  \
--net=host --shm-size=1g \
--name deepseek70b \
--device=/dev/davinci_manager \
--device=/dev/hisi_hdc \
--device=/dev/devmm_svm \
--device=/dev/davinci0 \
--device=/dev/davinci1 \
--device=/dev/davinci2 \
--device=/dev/davinci3 \
--device=/dev/davinci4 \
--device=/dev/davinci5 \
--device=/dev/davinci6 \
--device=/dev/davinci7 \
-v /usr/local/Ascend/driver:/usr/local/Ascend/driver:ro \
-v /usr/local/sbin:/usr/local/sbin:ro \
-v /:/path-to-weights:ro \
swr.cn-south-1.myhuaweicloud.com/ascendhub/mindie:2.0.T3.1-800I-A2-py311-openeuler24.03-lts bash

docker run -it -d --net=host --shm-size=1g \
    --name deepseek70b \
    --device=/dev/davinci_manager \
    --device=/dev/hisi_hdc \
    --device=/dev/devmm_svm \
    --device=/dev/davinci0 \
    --device=/dev/davinci1 \
    --device=/dev/davinci2 \
    --device=/dev/davinci3 \
    --device=/dev/davinci4 \
    --device=/dev/davinci5 \
    --device=/dev/davinci6 \
    --device=/dev/davinci7 \
    -v /usr/local/Ascend/driver:/usr/local/Ascend/driver:ro \
    -v /usr/local/sbin:/usr/local/sbin:ro \
    -v /path-to-weights:/path-to-weights:ro \
    swr.cn-south-1.myhuaweicloud.com/ascendhub/mindie:2.0.T3.1-800I-A2-py311-openeuler24.03-lts bash

vi  /usr/local/Ascend/mindie/latest/mindie-service/conf/config.json



docker cp /home/<USER>/DeepSeek-R1-Distill-Llama-70B deepseek70b:/data/DeepSeek-R1-Distill-Llama-70B

rsync -aH /home/<USER>/DeepSeek-R1-Distill-Llama-70B deepseek70b:/data/Deepseek-R1-Disti11-Llama-70B

mindie-service start --config /usr/local/Ascend/mindie/latest/mindie-service/conf/config.json

docker login -u hid_3d5e1iozmp-3zs4 -pZzb19830912! swr.cn-south-1.myhuaweicloud.com

docker pull 2.0.RC2-800I-A3-py311-openeuler24.03-lts
