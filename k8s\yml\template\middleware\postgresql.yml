apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-conf
  namespace: oa-llm
data:
  postgresql.conf: |
    # PostgreSQL 配置样例
    listen_addresses = '*'
    port = 5432
    max_connections = 100
    shared_buffers = 128MB
    logging_collector = on
    log_directory = '/var/log/postgresql'
    log_filename = 'postgresql.log'
    log_statement = 'none'
    # 你可以在这里添加更多 PostgreSQL 配置项
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-init-sql
  namespace: oa-llm
data:
  postgresql.sql: |
    CREATE DATABASE aidb;
    \connect aidb
    \i /docker-entrypoint-initdb.d/init-aidb.sql
  init-aidb.sql: |
    -- 删除已存在的表
    DROP TABLE IF EXISTS knowledge_base;
    DROP TABLE IF EXISTS document;
    DROP TABLE IF EXISTS document_chunk;

    -- 存储知识库的基本信息，包括名称、ID、分组等
    CREATE TABLE knowledge_base (
        id SERIAL PRIMARY KEY,                                   -- 自增主键，用于唯一标识知识库
        name VARCHAR(255) NOT NULL,                              -- 知识库名称，用于展示和搜索
        knowledge_base_id VARCHAR(255) NOT NULL UNIQUE,          -- 知识库唯一标识，用于关联其他表
        system_id VARCHAR(20) NOT NULL,                          -- 系统标识，用于区分不同系统
        app_id VARCHAR(20) NOT NULL,                             -- 应用ID，用于区分不同应用
        group_id VARCHAR(50) NOT NULL,                           -- 分组ID，用于分类和管理
        project_id VARCHAR(50) NOT NULL,                         -- 项目ID，用于关联项目信息
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,          -- 创建时间，用于记录创建时间
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP           -- 更新时间，用于记录更新时间
    );

    -- 存储文档的基本信息，包括文档ID、知识库ID、文件名、文件路径、文件大小、处理状态和批次ID等
    CREATE TABLE document (
        id SERIAL PRIMARY KEY,                                  -- 主键ID，自增，用于唯一标识文档
        knowledge_base_id VARCHAR(255) NOT NULL,                -- 关联知识库ID，用于关联知识库信息
        document_id VARCHAR(255) NOT NULL UNIQUE,               -- 文档唯一标识，用于业务层面识别文档
        batch_id VARCHAR(255),                                  -- 批次ID，用于批量处理和管理
        file_name VARCHAR(255) NOT NULL,                        -- 上传的原始文件名，用于展示和搜索
        file_path VARCHAR(255) NOT NULL,                        -- 文件存储的实际路径，用于存储和读取
        file_size BIGINT DEFAULT 0,                             -- 文件大小，用于统计和限制
        process_state SMALLINT DEFAULT 1,                       -- 处理状态: 1-待处理 2-处理中 3-处理成功 4-处理失败 5-已删除
        file_type SMALLINT DEFAULT 0,                           -- 文档类型: 0-普通文档 1-问答对文档
        error_msg VARCHAR(512),                                 -- 记录处理失败的具体原因，便于排查和调试
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,         -- 创建时间，用于记录创建时间
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP          -- 更新时间，自动更新
    );

    -- 存储文档的分块信息，包括文档ID、批次ID、分块索引、分块内容等
    CREATE TABLE document_chunk (
        id SERIAL PRIMARY KEY,                                    -- 主键ID，自增，用于唯一标识分块
        document_id VARCHAR(255) NOT NULL,                        -- 关联文档ID，用于关联文档信息
        chunk_index INTEGER NOT NULL,                             -- 分块索引，用于标识分块顺序
        chunk_content TEXT NOT NULL,                              -- 分块内容，用于存储和读取
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,          -- 创建时间，用于记录创建时间
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP           -- 更新时间，自动更新
    );



    DROP SCHEMA IF EXISTS qa CASCADE;
    CREATE SCHEMA qa;


    DROP SEQUENCE IF EXISTS qa.dialog_detail_dialog_detail_id_seq;

    CREATE SEQUENCE qa.dialog_detail_dialog_detail_id_seq
      INCREMENT BY 1
      MINVALUE 1
      MAXVALUE 9223372036854775807
      START 1
      CACHE 1
      NO CYCLE;

    DROP SEQUENCE IF EXISTS qa.dialog_info_dialog_id_seq;

    CREATE SEQUENCE qa.dialog_info_dialog_id_seq
      INCREMENT BY 1
      MINVALUE 1
      MAXVALUE 9223372036854775807
      START 1
      CACHE 1
      NO CYCLE;
    DROP SEQUENCE IF EXISTS  qa.sys_permission_id_seq;

    CREATE SEQUENCE qa.sys_permission_id_seq
      INCREMENT BY 1
      MINVALUE 1
      MAXVALUE 9223372036854775807
      START 1
      CACHE 1
      NO CYCLE;-- qa.dialog_detail definition

    DROP TABLE IF EXISTS qa.dialog_detail;

    CREATE TABLE qa.dialog_detail (
      dialog_detail_id bigserial NOT NULL,
      session_id varchar(255) NULL,
      done_time timestamp NULL,
      context varchar(10000) NULL,
      dialog_id int8 NULL,
      robot int2 NULL,
      confidence varchar(255) NULL,
      op_id varchar(32) NULL,
      op_name varchar(128) NULL,
      dept_id int8 NULL,
      reference varchar(255) NULL,
      CONSTRAINT dialog_detail_pkey PRIMARY KEY (dialog_detail_id)
    );


    -- qa.dialog_info definition

    DROP TABLE IF EXISTS qa.dialog_info;

    CREATE TABLE qa.dialog_info (
      dialog_id bigserial NOT NULL,
      op_id varchar(32) NULL,
      op_name varchar(128) NULL,
      dept_id int8 NULL,
      session_id varchar(255) NULL,
      start_time timestamp NULL,
      end_time timestamp NULL,
      dialog_type varchar(255) NULL,
      context varchar(255) NULL,
      CONSTRAINT dialog_info_pkey PRIMARY KEY (dialog_id)
    );


    -- qa.knowledge definition

    DROP TABLE IF EXISTS qa.knowledge;

    CREATE TABLE qa.knowledge (
      knowledge_base_id varchar(255) NOT NULL,
      system_id varchar(255) NOT NULL,
      group_id varchar(255) NOT NULL,
      project_id varchar(255) NULL,
      employee_number varchar(255) NULL,
      id int8 NOT NULL,
      "name" varchar(50) DEFAULT NULL::character varying NULL,
      create_date timestamp NULL,
      creator int8 NULL,
      update_date timestamp NULL,
      updater int8 NULL,
      CONSTRAINT ai_knowledge_pkey PRIMARY KEY (knowledge_base_id)
    );


    -- qa.minio_file_knowledge definition

    DROP TABLE IF EXISTS qa.minio_file_knowledge;

    CREATE TABLE qa.minio_file_knowledge (
      file_name varchar(255) NULL,
      "size" float8 NULL,
      del_flag int2 DEFAULT 0 NULL,
      update_time varchar NULL,
      create_time varchar NULL,
      bucket_name varchar(255) NULL,
      parse_status int2 NULL,
      file_id varchar(255) NOT NULL,
      knowledge_base_id varchar(255) NOT NULL,
      user_id int8 NULL,
      CONSTRAINT minio_file_pkey PRIMARY KEY (file_id)
    );


    -- qa.sys_dept definition

    DROP TABLE IF EXISTS qa.sys_dept;

    CREATE TABLE qa.sys_dept (
      id int8 NOT NULL,
      pid int8 NULL,
      pids varchar(500) NULL,
      "name" varchar(50) NULL,
      busi_unit_code varchar(10) NULL,
      sort int4 NULL,
      remaining_usage int8 NULL,
      email_suffix varchar(100) NULL,
      member_limit int4 DEFAULT 20 NULL,
      expire_time timestamp NULL,
      description varchar(128) NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      updater int8 NULL,
      update_date timestamp NULL,
      status int2 NULL,
      CONSTRAINT sys_dept_pkey PRIMARY KEY (id)
    );
    CREATE INDEX sys_dept_pid_idx ON qa.sys_dept USING btree (pid);
    CREATE INDEX sys_dept_sort_idx ON qa.sys_dept USING btree (sort);


    -- qa.sys_dept_knowledge definition

    DROP TABLE IF EXISTS qa.sys_dept_knowledge;

    CREATE TABLE qa.sys_dept_knowledge (
      id int8 NOT NULL,
      dept_id int8 NULL,
      knowledge_base_id varchar(255) NOT NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      updater int8 NULL,
      update_date timestamp NULL
    );


    -- qa.sys_dict_data definition

    DROP TABLE IF EXISTS qa.sys_dict_data;

    CREATE TABLE qa.sys_dict_data (
      id int8 NOT NULL,
      dict_type_id int8 NOT NULL,
      dict_label varchar(255) NOT NULL,
      dict_value varchar(255) NULL,
      remark varchar(255) NULL,
      sort int4 NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      updater int8 NULL,
      update_date timestamp NULL,
      CONSTRAINT sys_dict_data_pkey PRIMARY KEY (id),
      CONSTRAINT uk_dict_type_value UNIQUE (dict_type_id, dict_value)
    );
    CREATE INDEX sys_dict_data_sort_idx ON qa.sys_dict_data USING btree (sort);


    -- qa.sys_dict_type definition

    DROP TABLE IF EXISTS qa.sys_dict_type;

    CREATE TABLE qa.sys_dict_type (
      id int8 NOT NULL,
      dict_type varchar(100) NOT NULL,
      dict_name varchar(255) NOT NULL,
      remark varchar(255) NULL,
      sort int4 NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      updater int8 NULL,
      update_date timestamp NULL,
      CONSTRAINT dict_type UNIQUE (dict_type),
      CONSTRAINT sys_dict_type_pkey PRIMARY KEY (id)
    );


    -- qa.sys_log_error definition

    DROP TABLE IF EXISTS qa.sys_log_error;

    CREATE TABLE qa.sys_log_error (
      id int8 NOT NULL,
      request_uri varchar(200) NULL,
      request_method varchar(20) NULL,
      request_params text NULL,
      user_agent varchar(500) NULL,
      ip varchar(32) NULL,
      error_info text NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      CONSTRAINT sys_log_error_pkey PRIMARY KEY (id)
    );
    CREATE INDEX idx_create_date ON qa.sys_log_error USING btree (create_date);
    CREATE INDEX sys_log_error_create_date_idx ON qa.sys_log_error USING btree (create_date);


    -- qa.sys_log_login definition

    DROP TABLE IF EXISTS qa.sys_log_login;

    CREATE TABLE qa.sys_log_login (
      id int8 NOT NULL,
      operation int2 NULL,
      status int2 NOT NULL,
      user_agent varchar(500) NULL,
      ip varchar(100) NULL,
      creator_name varchar(50) NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      CONSTRAINT sys_log_login_pkey PRIMARY KEY (id)
    );
    CREATE INDEX sys_log_login_create_date_idx ON qa.sys_log_login USING btree (create_date);
    CREATE INDEX sys_log_login_status_idx ON qa.sys_log_login USING btree (status);


    -- qa.sys_log_operation definition

    DROP TABLE IF EXISTS qa.sys_log_operation;

    CREATE TABLE qa.sys_log_operation (
      id int8 NOT NULL,
      operation varchar(50) NULL,
      request_uri varchar(200) NULL,
      request_method varchar(20) NULL,
      request_params text NULL,
      request_time int4 NOT NULL,
      user_agent varchar(500) NULL,
      ip varchar(32) NULL,
      status int2 NOT NULL,
      creator_name varchar(50) NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      CONSTRAINT sys_log_operation_pkey PRIMARY KEY (id)
    );
    CREATE INDEX sys_log_operation_create_date_idx ON qa.sys_log_operation USING btree (create_date);


    -- qa.sys_menu definition

    DROP TABLE IF EXISTS qa.sys_menu;

    CREATE TABLE qa.sys_menu (
      id int8 NOT NULL,
      pid int8 NULL,
      "name" varchar(200) NULL,
      url varchar(200) NULL,
      permissions varchar(500) NULL,
      menu_type int2 NULL,
      icon varchar(50) NULL,
      sort int4 NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      updater int8 NULL,
      update_date timestamp NULL,
      CONSTRAINT sys_menu_pkey PRIMARY KEY (id)
    );
    CREATE INDEX sys_menu_pid_idx ON qa.sys_menu USING btree (pid);
    CREATE INDEX sys_menu_sort_idx ON qa.sys_menu USING btree (sort);


    -- qa.sys_menu_permission definition

    DROP TABLE IF EXISTS qa.sys_menu_permission;

    CREATE TABLE qa.sys_menu_permission (
      id int8 NOT NULL,
      menu_id int8 NULL,
      permission_id int8 NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      CONSTRAINT sys_menu_permission_pkey PRIMARY KEY (id)
    );
    CREATE INDEX sys_menu_permission_menu_id_idx ON qa.sys_menu_permission USING btree (menu_id);
    CREATE INDEX sys_menu_permission_permission_id_idx ON qa.sys_menu_permission USING btree (permission_id);


    -- qa.sys_params definition

    DROP TABLE IF EXISTS qa.sys_params;

    CREATE TABLE qa.sys_params (
      id int8 NOT NULL,
      param_code varchar(32) NULL,
      param_value varchar(2000) NULL,
      param_type int2 DEFAULT 1 NULL,
      remark varchar(200) NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      updater int8 NULL,
      update_date timestamp NULL,
      CONSTRAINT sys_params_pkey PRIMARY KEY (id),
      CONSTRAINT uk_param_code UNIQUE (param_code)
    );
    CREATE INDEX sys_params_create_date_idx ON qa.sys_params USING btree (create_date);


    -- qa.sys_permission definition

    DROP TABLE IF EXISTS qa.sys_permission;

    CREATE TABLE qa.sys_permission (
      id bigserial NOT NULL,
      service varchar(100) DEFAULT 'default'::character varying NOT NULL,
      "path" varchar(100) NOT NULL,
      remark varchar(200) NULL,
      update_date timestamp NULL,
      create_date timestamp NULL,
      creator int8 NULL,
      CONSTRAINT "path" UNIQUE (service, path),
      CONSTRAINT sys_permission_pkey PRIMARY KEY (id)
    );


    -- qa.sys_role definition

    DROP TABLE IF EXISTS qa.sys_role;

    CREATE TABLE qa.sys_role (
      id int8 NOT NULL,
      "name" varchar(50) NULL,
      remark varchar(100) NULL,
      dept_id int8 NULL,
      index_view varchar(100) NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      updater int8 NULL,
      update_date timestamp NULL,
      CONSTRAINT sys_role_pkey PRIMARY KEY (id)
    );
    CREATE INDEX sys_role_dept_id_idx ON qa.sys_role USING btree (dept_id);


    -- qa.sys_role_data_scope definition

    -- Drop table

    DROP TABLE IF EXISTS qa.sys_role_data_scope;

    CREATE TABLE qa.sys_role_data_scope (
      id int8 NOT NULL,
      role_id int8 NULL,
      dept_id int8 NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      CONSTRAINT sys_role_data_scope_pkey PRIMARY KEY (id)
    );
    CREATE INDEX sys_role_data_scope_role_id_idx ON qa.sys_role_data_scope USING btree (role_id);


    -- qa.sys_role_menu definition

    DROP TABLE IF EXISTS qa.sys_role_menu;

    CREATE TABLE qa.sys_role_menu (
      id int8 NOT NULL,
      role_id int8 NULL,
      menu_id int8 NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      CONSTRAINT sys_role_menu_pkey PRIMARY KEY (id)
    );
    CREATE INDEX sys_role_menu_menu_id_idx ON qa.sys_role_menu USING btree (menu_id);
    CREATE INDEX sys_role_menu_role_id_idx ON qa.sys_role_menu USING btree (role_id);


    -- qa.sys_role_user definition

    DROP TABLE IF EXISTS qa.sys_role_user;

    CREATE TABLE qa.sys_role_user (
      id int8 NOT NULL,
      role_id int8 NULL,
      user_id int8 NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      CONSTRAINT sys_role_user_pkey PRIMARY KEY (id)
    );
    CREATE INDEX sys_role_user_role_id_idx ON qa.sys_role_user USING btree (role_id);
    CREATE INDEX sys_role_user_user_id_idx ON qa.sys_role_user USING btree (user_id);


    -- qa.sys_user definition

    DROP TABLE IF EXISTS qa.sys_user;

    CREATE TABLE qa.sys_user (
      id int8 NOT NULL,
      username varchar(64) NOT NULL,
      "password" varchar(100) NULL,
      real_name varchar(64) NULL,
      head_url varchar(200) NULL,
      gender int2 NULL,
      email varchar(100) NULL,
      mobile varchar(100) NULL,
      dept_id int8 NULL,
      super_admin int2 NULL,
      status int2 NULL,
      account_type int2 DEFAULT 0 NULL,
      enable_safety_tip int2 DEFAULT 1 NULL,
      "level" int8 NULL,
      nickname varchar(64) NULL,
      birthday timestamp NULL,
      city varchar(64) NULL,
      career varchar(64) NULL,
      signature varchar(256) NULL,
      remaining_usage int8 NULL,
      "type" int2 DEFAULT 3 NOT NULL,
      token_count int8 DEFAULT 0 NULL,
      description varchar(128) NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      updater int8 NULL,
      update_date timestamp NULL,
      CONSTRAINT sys_user_pkey PRIMARY KEY (id),
      CONSTRAINT uk_username UNIQUE (username)
    );
    CREATE INDEX idx_sys_user_create_date ON qa.sys_user USING btree (create_date);
    CREATE INDEX sys_user_create_date_idx ON qa.sys_user USING btree (create_date);


    -- qa.sys_user_dept definition

    DROP TABLE IF EXISTS qa.sys_user_dept;

    CREATE TABLE qa.sys_user_dept (
      id int8 NOT NULL,
      user_id int8 NULL,
      dept_id int8 NULL,
      creator int8 NULL,
      create_date timestamp NULL,
      CONSTRAINT sys_user_dept_pkey PRIMARY KEY (id)
    );
    CREATE INDEX sys_user_dept_user_id_idx ON qa.sys_user_dept USING btree (user_id);
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql
  template:
    metadata:
      labels:
        app: postgresql
    spec:
      containers:
      - name: postgresql
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/postgresql:12.18
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          value: "admin"
        - name: POSTGRES_PASSWORD
          value: "PgSQL@2025"
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: pg-data
          mountPath: /var/lib/postgresql/data
        - name: pg-logs
          mountPath: /var/log/postgresql
        - name: pg-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        - name: pg-init-sql
          mountPath: /docker-entrypoint-initdb.d/postgresql.sql
          subPath: postgresql.sql
        - name: pg-init-aidb
          mountPath: /docker-entrypoint-initdb.d/init-aidb.sql
          subPath: init-aidb.sql
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      volumes:
      - name: pg-data
        persistentVolumeClaim:
          claimName: postgresql-data-pvc
      - name: pg-logs
        persistentVolumeClaim:
          claimName: postgresql-logs-pvc
      - name: pg-config
        configMap:
          name: postgresql-conf
      - name: pg-init-sql
        configMap:
          name: postgresql-init-sql
          items:
          - key: postgresql.sql
            path: postgresql.sql
      - name: pg-init-aidb
        configMap:
          name: postgresql-init-sql
          items:
          - key: init-aidb.sql
            path: init-aidb.sql
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-data-pvc
  namespace: oa-llm
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-logs-pvc
  namespace: oa-llm
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: Service
metadata:
  name: postgresql
  namespace: oa-llm
spec:
  ports:
  - port: 5432
    targetPort: 5432
  selector:
    app: postgresql
