version: '3'

services:
  ai-writer-stream:
    image: ai-writer/ai-writer-stream:v1
    container_name: ai-writer-stream
    ports:
      - "${PORT:-8095}:8080"
    volumes:
      - ${BASE_DIR:-/data/ai-writer}/ai-writer-stream/config/config.py:/app/web_source_code/source_app/config.py
      - ${BASE_DIR:-/data/ai-writer}/ai-writer-stream/logs:/app/web_source_code/log
      - ${BASE_DIR:-/data/ai-writer}/ai-writer-stream/shells:/app/shells
      - ${BASE_DIR:-/data/ai-writer}/ai-writer-stream/data/公文语料库2024-12-24.xlsx:/app/web_source_code/backend/公文语料库2024-12-24.xlsx
    environment:
      - TZ=Asia/Shanghai
    networks:
      - jiutianwensi-network
    restart: always
  
networks:
  jiutianwensi-network:
    external: true 