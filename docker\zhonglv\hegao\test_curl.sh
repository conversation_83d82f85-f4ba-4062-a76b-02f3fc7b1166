#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# ====================== 文档检查API测试 ======================
echo -e "${BLUE}=== 文档检查API测试 ===${NC}"
echo -e "${YELLOW}正在测试文档检查接口...${NC}"

# 显示将要执行的命令
echo 'curl -X POST \'
echo '  -F "file=@test.txt;type=text/plain" \'
echo '  -F "accessMode=STANDALONE" \'
echo '  -F "checkUnitId=BALANCEABILITY" \'
echo '  -F "fileType=HTML" \'
echo '  -F "extension={\"title\":\"请在此输入标题\",\"mainSubmit\":[],\"copySubmit\":[]}" \'
echo '  http://localhost:8093/ai-doc-poc/api/v1/check'
echo

# 测试文档为test.txt，请确保该文件存在
curl -X POST \
  -F "file=@test.txt;type=text/plain" \
  -F "accessMode=STANDALONE" \
  -F "checkUnitId=BALANCEABILITY" \
  -F "fileType=HTML" \
  -F "extension={\"title\":\"请在此输入标题\",\"mainSubmit\":[],\"copySubmit\":[]}" \
  http://localhost:8093/ai-doc-poc/api/v1/check

echo
echo -e "${GREEN}=== 文档检查API测试结束 ===${NC}"





