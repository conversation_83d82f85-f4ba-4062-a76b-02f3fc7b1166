apiVersion: v1
kind: Service
metadata:
  labels:
    cmcc-gitops-file-name: poc-intelligence-view-write.yaml
    cmcc-gitops-project-tag: oallm
  name: poc-intelligence-view-write
  namespace: oa-llm
spec:
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: poc-intelligence-view-write
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    cmcc-gitops-file-name: poc-intelligence-view-write.yaml
    cmcc-gitops-project-tag: oallm
  name: poc-intelligence-view-write
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: poc-intelligence-view-write
  serviceName: poc-intelligence-view-write
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: poc-intelligence-view-write
    spec:
      containers:
      - image: artifactory.dep.devops.cmit.cloud:20101/native_common/poc-intelligence-view-write:x86-250517-1643
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 5
          tcpSocket:
            port: 80
          timeoutSeconds: 5
        name: poc-intelligence-view-write
        ports:
        - containerPort: 80
          protocol: TCP
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 20
          periodSeconds: 5
          tcpSocket:
            port: 80
          timeoutSeconds: 10
        resources: {}
        volumeMounts:
        - mountPath: /etc/nginx/nginx.conf
          name: poc-intelligence-view-write-config
          subPath: nginx.conf
        - mountPath: /var/log/nginx/
          name: poc-intelligence-view-write-logs
      - args:
        - -c
        - /opt/filebeat/filebeat.yml
        - -e
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/filebeat:7.17.14
        imagePullPolicy: Always
        name: filebeat
        resources: {}
        terminationMessagePath: /var/log/err.log
        volumeMounts:
        - mountPath: /opt/filebeat/filebeat.yml
          name: poc-intelligence-view-write-filebeat-cm
          subPath: filebeat.yml
        - mountPath: /poc-intelligence-view-write/data/logs 
          name: poc-intelligence-view-write-logs
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: poc-intelligence-view-write-logs
      - configMap:
          defaultMode: 420
          name: poc-intelligence-view-write-config
        name: poc-intelligence-view-write-config
      - configMap:
          defaultMode: 420
          name: poc-intelligence-view-write-filebeat-cm
        name: poc-intelligence-view-write-filebeat-cm
        
  updateStrategy: {}
status:
  replicas: 0
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: poc-intelligence-view-write-config
  namespace: oa-llm
data:
  nginx.conf: |-
    events {
        worker_connections  1000;
    }

    error_log  /var/log/nginx/error.log debug;

    http {
        include       mime.types;
        default_type  application/octet-stream;
        sendfile        on;
        keepalive_timeout  65;

        log_format  main  '$remote_addr:$remote_port - $remote_user [$time_local] '
                              '"$request_method $request_uri $server_protocol" '
                              '$status $body_bytes_sent '
                              '"$http_referer" "$http_user_agent" "$upstream_addr" ';#"$request_body"
        access_log  /var/log/nginx/access.log main;
    
      

        limit_conn_zone $server_name zone=auth_conn:20m;
        #limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
        limit_req_zone $server_name zone=auth_req:20m rate=1r/s; #确定每个请求发起后的冷却周期


        server {
            listen       80;
            server_name  localhost;

            # 开启gzip压缩
            gzip on;
            gzip_min_length 1k;
            gzip_comp_level 6;
            gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
            gzip_vary on;
            gzip_disable "MSIE [1-6]\.";

            # 静态资源缓存设置
            location / {
                root   /usr/share/nginx/html;
                index  index.html index.htm;
                try_files $uri $uri/ /index.html;
                
                # 缓存设置
                expires 7d;
            }
            location = /poc-intelligence-view-write { 
                return 301 /poc-intelligence-view-write/;
            }

            location ^~ /poc-intelligence-view-write/ {
                alias /usr/share/nginx/html/poc-intelligence-view-write/;
                index index.html;
                try_files $uri $uri/ /poc-intelligence-view-write/index.html;
            }
            location /ai-compose-poc {
                proxy_pass http://article-auto-compose-server.oa-llm:8080/ai-compose-poc;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_http_version 1.1;
                proxy_connect_timeout 60s;
                proxy_read_timeout 60s;
                proxy_send_timeout 60s;
            }


        }

    }
---
apiVersion: v1
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: filestream
      id: my-filestream-id
      paths:
        - /poc-intelligence-view-write/data/logs/*.log
      fields:
        envTag: "oa-llm"
        appTag: "poc-intelligence-view-write"
        namespace: oa-llm
    processors:
    - drop_fields:
        fields: ["agent","input","ecs"]
        ignore_missing: true
    output.elasticsearch:
      hosts: ["http://elasticsearch.oa-llm:9200"]
      index: "poc-intelligence-view-write-logs-%{+yyyy.MM.dd}"
    setup.template.name: "poc-intelligence-view-write-logs"
    setup.template.pattern: "poc-intelligence-view-write-logs*"
    setup.ilm.enabled: false  # 禁用 ILM，避免自动 rollover 到 filebeat-*
kind: ConfigMap
metadata:
  labels:
    cmcc-gitops-project-tag: oa-llm 
  name: poc-intelligence-view-write-filebeat-cm
  namespace: oa-llm