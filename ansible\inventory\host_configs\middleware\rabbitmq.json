{"middleware_name": "rabbitmq", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "m.daocloud.io/docker.io/rabbitmq:3.12-management", "host_port": "5672", "container_port": "5672", "management_port": "15672", "username": "admin", "password": "admin123", "host_data_dir": "{{base_dir}}/rabbitmq/data", "host_logs_dir": "{{base_dir}}/rabbitmq/log", "container_data_dir": "/var/lib/rabbitmq", "container_logs_dir": "/var/log/rabbitmq", "restart_script": "{{base_dir}}/rabbitmq/restart.sh", "test_script": "{{base_dir}}/rabbitmq/test_curl.sh", "env_vars": [{"name": "RABBITMQ_DEFAULT_USER", "value": "admin"}, {"name": "RABBITMQ_DEFAULT_PASS", "value": "admin123"}], "test_commands": ["docker exec -it rabbitmq rabbitmqadmin declare queue name=test", "docker exec -it rabbitmq rabbitmqadmin publish routing_key=test payload='hello'"]}