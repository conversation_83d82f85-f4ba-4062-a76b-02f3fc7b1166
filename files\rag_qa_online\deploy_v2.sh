#!/bin/bash
APP_NAME=rag-qa-online
APP_VERSION=v2
MODULE_NAME=znwd
APP_DIR=/data/$MODULE_NAME/$APP_NAME
APP_LOGS_DIR=$APP_DIR/logs
APP_CONTAINER_PATH=/home/<USER>
APP_CONTAINER_SERVICE_JSON_CONFIG_PATH=$APP_CONTAINER_PATH/config/services.json
APP_CONTAINER_SERVICE_JSON_CONFIG_TEST_PATH=$APP_CONTAINER_PATH/config/services_test.json
APP_CONTAINER_MODEL_YAML_CONFIG_PATH=$APP_CONTAINER_PATH/configs/models.yaml

# 容器内日志文件路径
APP_CONTAINER_LOGS_DIR=$APP_CONTAINER_PATH/log
APP_CONTAINER_SHELLS_PATH=/home/<USER>
DEPLOY_DIR=/data/build/rag-qa-online
DOCKERFILE_PATH=$DEPLOY_DIR/Dockerfile
echo "=====================容器内信息======================="
echo "容器内路径: $APP_CONTAINER_PATH"
echo "容器内日志目录: $APP_CONTAINER_LOGS_DIR"
echo "容器内服务配置文件路径: $APP_CONTAINER_SERVICE_JSON_CONFIG_PATH"
echo "容器内模型配置文件路径: $APP_CONTAINER_MODEL_YAML_CONFIG_PATH"
echo "容器内shells目录路径: $APP_CONTAINER_SHELLS_PATH"
echo "=====================容器内信息======================="

echo "=====================宿主机信息======================="
echo "宿主机路径: $APP_DIR"
echo "宿主机日志目录2: $APP_LOGS_DIR"
echo "宿主机服务配置文件路径: $APP_DIR/config/services.json"
echo "宿主机模型配置文件路径: $APP_DIR/config/models.yaml"
echo "宿主机shells目录路径: $APP_DIR/shells"
echo "=====================宿主机信息======================="

# 创建目录
echo "创建目录: $APP_DIR"
sudo mkdir -p $APP_DIR
echo "清空/$APP_DIR目录"
rm -rf $APP_DIR/*
echo "创建日志目录: $APP_LOGS_DIR"
mkdir -p $APP_LOGS_DIR
echo "创建服务配置文件目录: $APP_DIR/config"
mkdir -p $APP_DIR/config
echo "创建shells目录: $APP_DIR/shells"
mkdir -p $APP_DIR/shells


echo "设置权限: chmod -R 755 $APP_DIR"
sudo chmod -R 755 $APP_DIR
# 创建/data/build/rag_qa_online/目录
echo "创建$DEPLOY_DIR目录"
mkdir -p $DEPLOY_DIR

echo "将配置文件从$DEPLOY_DIR目录的json和yaml文件复制到宿主机目录"
cp -r $DEPLOY_DIR/*.json $APP_DIR/config
cp -r $DEPLOY_DIR/*.yaml $APP_DIR/config
cp -r $DEPLOY_DIR/start.sh $APP_DIR/shells
cp -r $DEPLOY_DIR/test_url.sh $APP_DIR
tree $DEPLOY_DIR
tree $APP_DIR
# 查看容器是否存在，存在则删除
CONTAINER_ID=$(docker ps -a -q --filter "name=$APP_NAME")
if [ -n "$CONTAINER_ID" ]; then
    echo "容器存在，删除容器"
    docker rm -f $APP_NAME
fi  

# 构建镜像
echo "构建镜像"
# 判断是否重新制作镜像,默认是重新制作
read -p "是否重新制作镜像? (y/n): " REBUILD_IMAGE
if [ "$REBUILD_IMAGE" = "y" ]; then
    # 删除之前的镜像
    echo -e "${YELLOW}删除之前的镜像...${NC}"
    #如果镜像存在，则删除   
    if docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^$MODULE_NAME/$APP_NAME:$APP_VERSION$"; then
        docker rmi -f  $MODULE_NAME/$APP_NAME:$APP_VERSION 
    fi
    # 将none镜像删除
    echo -e "${YELLOW}删除none镜像...${NC}"
    docker rmi -f $(docker images -f "dangling=true" -q)
    # 构建Docker镜像
    echo -e "${YELLOW}构建Docker镜像...${NC}"
    echo "docker build -t $MODULE_NAME/$APP_NAME:$APP_VERSION -f $DOCKERFILE_PATH ."
    docker build -t $MODULE_NAME/$APP_NAME:$APP_VERSION -f $DOCKERFILE_PATH .

    if [ $? -ne 0 ]; then
        echo -e "${RED}镜像构建失败，请检查错误信息${NC}"
        exit 1
    fi

    echo -e "${GREEN}镜像构建成功!${NC}"
else
    echo -e "${YELLOW}本次部署不重新制作镜像${NC}"
fi

CONTAINER_CMD="docker run -itd --name $APP_NAME  \
    --restart=always \
    --network=jiutianwensi \
    -p 8085:8080 \
    -e TZ=Asia/Shanghai \
    -v $APP_LOGS_DIR:$APP_CONTAINER_LOGS_DIR \
    -v $APP_DIR/config/models.yaml:$APP_CONTAINER_MODEL_YAML_CONFIG_PATH \
    -v $APP_DIR/config/services.json:$APP_CONTAINER_SERVICE_JSON_CONFIG_PATH \
    -v $APP_DIR/config/services_test.json:$APP_CONTAINER_SERVICE_JSON_CONFIG_TEST_PATH \
    -v $APP_DIR/shells:$APP_CONTAINER_SHELLS_PATH \
    $MODULE_NAME/$APP_NAME:$APP_VERSION"
echo "启动容器命令: $CONTAINER_CMD"

# 执行启动命令
$CONTAINER_CMD

# 获取容器id
CONTAINER_ID=$(docker ps -q --filter "name=$APP_NAME")
echo "容器id: $CONTAINER_ID"
# 查看 容器状态
docker ps  --filter "name=$APP_NAME"

# 等待10秒
echo "等待10秒"
sleep 10

# 获取容器日志
echo "获取容器日志"
docker logs  $APP_NAME

# 验证容器是否启动成功
echo "验证容器是否启动成功"
docker ps  --filter "name=$APP_NAME"

# 设置权限
echo "设置权限: chmod 777 $APP_LOGS_DIR"
chmod 777 $APP_LOGS_DIR



# 验证接口
echo '验证接口：curl -X POST -H "Content-Type: application/json" -d {"text": "研发项目立项有哪些环节", "history": []} http://localhost:8085/Rag/QAOnlineSSE'
curl -X POST -H "Content-Type: application/json" \
     -d '{"text": "研发项目立项有哪些环节", "history": []}' \
     http://localhost:8085/Rag/QAOnlineSSE

./test_url.sh
echo -e "\n接口验证完成"

# 重启filebeat-rag-qa-online容器
echo "重启filebeat-rag-qa-online容器"
docker restart filebeat-rag-qa-online
