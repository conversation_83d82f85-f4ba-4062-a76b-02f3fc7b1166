#!/bin/bash

# 获取当前目录
CURRENT_DIR=$(pwd)
# 获取父目录
PARENT_DIR=$(dirname ${CURRENT_DIR})
# 读取config.json文件
CONFIG_FILE="${CURRENT_DIR}/config.json"
# 读取config.json文件中的base-config字段
BASE_CONFIG=$(jq -r '.["base-config"]' ${CONFIG_FILE})
# 读取config.json文件中的replace-config字段
REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})
YML_DIR="${PARENT_DIR}/yml/work"
TEST_SCRIPT_DIR="${PARENT_DIR}/test-script"

# 加载工具函数
source "${CURRENT_DIR}/utils/utils.sh"
source "${CURRENT_DIR}/utils/apply_service.sh"

# 兼容旧版本的apply函数调用，内部转发到apply_service
function apply() {
    YML_FILE_PATH=$1
    SERVICE_NAME=$2
    # 测试脚本路径
    TEST_SCRIPT_PATH=$3
    
    # 调用新的apply_service函数
    apply_service "$YML_FILE_PATH" "$SERVICE_NAME" "$TEST_SCRIPT_PATH" "oa-llm" "false"
}

# 创建namespace
# 查看是否存在namespace
echo "查看是否存在namespace oa-llm"
echo "kubectl get namespace oa-llm"
kubectl get namespace oa-llm
if [ $? -ne 0 ]; then
    echo "创建namespace oa-llm"
    echo "kubectl create namespace oa-llm"
    kubectl create namespace oa-llm
else
    echo "namespace oa-llm已存在"
fi

# 创建imagepullsecret
echo "创建imagepullsecret"
# 从config.json文件中获取target-image-repo-url
TARGET_IMAGE_REPO_URL=$(jq -r '.["base-config"]["target-image-repo-url"]' ${CONFIG_FILE})
# 从config.json文件中获取target-image-repo-user
TARGET_IMAGE_REPO_USER=$(jq -r '.["base-config"]["target-image-repo-user"]' ${CONFIG_FILE})
# 从config.json文件中获取target-image-repo-password
TARGET_IMAGE_REPO_PASSWORD=$(jq -r '.["base-config"]["target-image-repo-password"]' ${CONFIG_FILE})
# 查看是否存在imagepullsecret
echo "kubectl get secret oa-llm-imagepullsecret -n oa-llm"
kubectl get secret oa-llm-imagepullsecret -n oa-llm
if [ $? -ne 0 ]; then
    echo "oa-llm-imagepullsecret不存在"
    echo "kubectl create secret docker-registry oa-llm-imagepullsecret --docker-server=${TARGET_IMAGE_REPO_URL} --docker-username=${TARGET_IMAGE_REPO_USER} --docker-password=${TARGET_IMAGE_REPO_PASSWORD}  -n oa-llm"
    kubectl create secret docker-registry oa-llm-imagepullsecret --docker-server=${TARGET_IMAGE_REPO_URL} --docker-username=${TARGET_IMAGE_REPO_USER} --docker-password=${TARGET_IMAGE_REPO_PASSWORD}  -n oa-llm
else
    echo "oa-llm-imagepullsecret已存在，不创建"
fi

# 从config.json中读取所有服务配置并按顺序存储
declare -a services
for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
    # 获取service-name
    service_name=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
    # 获取yml-file-name
    yml_file_name=$(jq -r ".[\"replace-config\"][$i][\"yml-file-name\"]" ${CONFIG_FILE})
    # 获取type
    type=$(jq -r ".[\"replace-config\"][$i][\"type\"]" ${CONFIG_FILE})
    
    # 以JSON格式存储服务信息
    service_info="{\"name\":\"$service_name\",\"yml\":\"$yml_file_name\",\"type\":\"$type\"}"
    services[$i]=$service_info
done

# 依次部署每个服务
for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
    # 解析服务信息
    service_info="${services[$i]}"
    service_name=$(echo $service_info | jq -r '.name')
    yml_file_name=$(echo $service_info | jq -r '.yml')
    type=$(echo $service_info | jq -r '.type')
    
    # 构建路径
    yml_file_path="${YML_DIR}/${type}/${yml_file_name}"
    test_script_path="${TEST_SCRIPT_DIR}/test-${service_name}.sh"
    
    # 部署服务，使用新的apply_service函数
    apply_service "$yml_file_path" "$service_name" "$test_script_path" "oa-llm" "false"
done

echo "所有服务部署完成!"

