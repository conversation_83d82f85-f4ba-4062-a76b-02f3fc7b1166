# 大模型相关配置
#model.url=http://10.7.202.253:8000/v1/chat/completions
#model-name=/opt/vllm_models/Qwen/Qwen2.5-72B-Instruct
model.url=http://deepseek70b-server:1025/v1/chat/completions
model-name=DeepSeek-70B
model.appid=tyrknosa
model.appKey=5ab43270aecc9f482b79c965ab81d411
model.capabilityname=semantic0000000000000000
model.api-key=


# 文件路径配置
path.sensitiveWords=/app/temp/web_source_code/backend/sensitive_words_all.txt
path.rulesFile=/app/temp/web_source_code/backend/re.json
path.templatesDir=/app/temp/web_source_code/backend/writing_template

# 生成参数配置
#generation.maxTokens=1024
#generation.temperature=0.2

# 系统配置
system.prompt=你是一个公文写作专家，公文内不要出现"我们"、"我"、"你们"等口语化词汇，也不需要带入主送单位 