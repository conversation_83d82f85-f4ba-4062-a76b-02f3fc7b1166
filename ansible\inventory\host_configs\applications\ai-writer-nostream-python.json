{"app_name": "ai-writer-nostream-python", "module_name": "ai-writer", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "v1", "image_name": "ai-writer/ai-writer-nostream-python:v1", "container_port": "8080", "host_port": "8096", "app_data_dir": "/app/temp/web_source_code/local_data", "app_logs_dir": "/app/temp/web_source_code/log", "app_config_dir": "/app/temp/web_source_code/backend/application.properties", "host_data_dir": "{{base_dir}}/ai-writer/ai-writer-nostream-python/data", "host_logs_dir": "{{base_dir}}/ai-writer/ai-writer-nostream-python/logs", "host_config_dir": "{{base_dir}}/ai-writer/ai-writer-nostream-python/config", "restart_script": "{{base_dir}}/ai-writer/ai-writer-nostream-python/restart.sh", "test_script": "{{base_dir}}/ai-writer/ai-writer-nostream-python/test_curl.sh", "runtime": "python", "env_vars": [{"name": "PYTHONPATH", "value": "/app/temp/web_source_code"}, {"name": "TZ", "value": "Asia/Shanghai"}], "external_dependencies": [{"type": "service", "name": "qwen-32b-instruct", "url": "https://ecloud.10086.cn/api/openapi-icp/inference-api/2185090615607296/aiops-1340745253490184192/qwen-32b-instruct/service/8080/v1/chat/completions"}], "test_commands": ["curl -s http://localhost:8096/health | grep OK"]}