#!/bin/bash
set -e

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# 配置参数,APP_DIR为部署的目录，如果为空，则使用当前目录    
APP_DIR=${1:-$(pwd)}
echo "APP_DIR: ${APP_DIR}"
USER_NAME="admin"
USER_PASSWORD="QmffyH1zxSWE5Nke"

echo -e "${BLUE}=== OpenSearch 和 OpenSearch Dashboard 重启脚本 ===${NC}"

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}错误: Docker 未运行，请先启动 Docker${NC}"
  exit 1
fi

# 判断docker-compose.yml文件是否存在
if [ ! -f "${APP_DIR}/docker-compose.yml" ]; then
  echo -e "${RED}错误: ${APP_DIR}/docker-compose.yml 文件不存在${NC}"
  exit 1
fi

echo -e "${YELLOW}停止现有容器...${NC}"
# 停止并删除容器
docker-compose -f ${APP_DIR}/docker-compose.yml down

echo -e "${YELLOW}启动容器...${NC}"
# 启动容器
BASE_DIR=${APP_DIR} docker-compose -f ${APP_DIR}/docker-compose.yml up -d

echo -e "${YELLOW}等待服务启动...${NC}"
sleep 10

# 检查容器状态
echo -e "${YELLOW}检查容器状态...${NC}"
if docker ps | grep -q "opensearch" && docker ps | grep -q "opensearch-dashboard"; then
  echo -e "${GREEN}容器已成功启动${NC}"
else
  echo -e "${RED}容器启动异常，请检查日志${NC}"
  docker-compose -f ${APP_DIR}/docker-compose.yml logs
  exit 1
fi

# 输出服务信息
echo -e "${GREEN}=== 服务信息 ===${NC}"
echo "OpenSearch: https://localhost:9200 (用户: ${USER_NAME}, 密码: ${USER_PASSWORD})"
echo "OpenSearch Dashboard: http://localhost:5601"

# 测试服务可用性
echo -e "${YELLOW}测试 OpenSearch 服务...${NC}"
if curl -s -k -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/ > /dev/null 2>&1; then
  echo -e "${GREEN}OpenSearch 服务正常${NC}"
else
  echo -e "${RED}OpenSearch 服务异常，请检查日志${NC}"
  docker logs opensearch | tail -n 20
fi

echo -e "${BLUE}=== 重启完成 ===${NC}"
