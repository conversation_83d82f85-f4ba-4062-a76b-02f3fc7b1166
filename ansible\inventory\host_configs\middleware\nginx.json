{"middleware_name": "nginx", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "m.daocloud.io/docker.io/nginx:1.27.4", "host_port": "80", "container_port": "80", "host_logs_dir": "{{base_dir}}/nginx/log", "host_config_dir": "{{base_dir}}/nginx/conf", "host_html_dir": "{{base_dir}}/nginx/html", "container_logs_dir": "/var/log/nginx", "container_config_dir": "/etc/nginx/nginx.conf", "container_html_dir": "/usr/share/nginx/html", "restart_script": "{{base_dir}}/nginx/restart.sh", "test_script": "{{base_dir}}/nginx/test_curl.sh", "proxy_services": [{"name": "redis", "port": "6379"}, {"name": "rabbitmq", "port": "5672"}, {"name": "zookeeper", "port": "2181"}], "test_commands": ["curl -s http://localhost/status | grep -q 'server is running'"]}