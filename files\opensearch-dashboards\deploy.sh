
#!/bin/bash
set -e

# APP目录
APP_DIR=$1
USER_NAME="admin"
USER_PASSWORD="QmffyH1zxSWE5Nke"

#判断APP目录是否存在，如果不存在则创建
if [ ! -d "${APP_DIR}" ]; then
    echo "APP目录不存在，创建APP目录"
    mkdir -p ${APP_DIR}
fi
# 创建卷
if ! docker volume ls | grep -q "opensearch-data"; then
    echo "卷不存在，创建卷"
    docker volume create opensearch-data
fi
# 创建目录
mkdir -p ${APP_DIR}/opensearch/logs
mkdir -p ${APP_DIR}/opensearch-dashboard/logs
# 将restart.sh文件复制到APP_DIR目录下
cp -r restart.sh ${APP_DIR}/restart.sh
tree ${APP_DIR}

# 判断镜像public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0
# public.ecr.aws/opensearchproject/opensearch:2.9.0
#是否存在，如果不存在，则在当前的images下load
if ! docker images | grep -q "public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0"; then
    echo "镜像不存在，在当前的images下load"
    # 判断opensearch-dashboards*.tar是否存在
    if [ ! -f "${APP_DIR}/images/opensearch-dashboards*.tar" ]; then
        echo "❌ opensearch-dashboards镜像文件不存在"
        exit 1
    fi
    # 从images中load镜像
    docker load -i ${APP_DIR}/images/opensearch-dashboards*.tar public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0
fi

if ! docker images | grep -q "public.ecr.aws/opensearchproject/opensearch:2.9.0"; then
    echo "镜像不存在，在当前的images下load"
    # 判断opensearch*.tar是否存在
    if [ ! -f "${APP_DIR}/images/opensearch*.tar" ]; then
        echo "❌ opensearch镜像文件不存在"
        exit 1
    fi  
    # 从images中load镜像
    docker load -i ${APP_DIR}/images/opensearch*.tar public.ecr.aws/opensearchproject/opensearch:2.9.0
fi
echo "镜像加载完成"
docker images | grep "public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0"
docker images | grep "public.ecr.aws/opensearchproject/opensearch:2.9.0"
# 判断opensearch-dashboards容器是否存在，如果存在则停止并删除
if docker ps | grep -q "opensearch-dashboards"; then
    echo "opensearch-dashboards容器存在，停止并删除"
    docker stop opensearch-dashboards
    docker rm -f opensearch-dashboards
fi

# 判断opensearch容器是否存在，如果存在则停止并删除
if docker ps | grep -q "opensearch"; then
    echo "opensearch容器存在，停止并删除"
    docker stop opensearch
    docker rm -f opensearch
fi

# 启动opensearch-dashboards容器
BASE_DIR=${APP_DIR} docker-compose -f ${APP_DIR}/docker-compose.yml up -d
sleep 10
#  查看启动日志

echo "opensearch启动日志"
docker logs opensearch

echo "opensearch-dashboards启动日志   "
docker logs opensearch-dashboards

# 查看容器状态
echo "opensearch-dashboards容器状态"
docker ps | grep "opensearch-dashboards"

echo "opensearch容器状态"
docker ps | grep "opensearch"


# 用curl测试opensearch是否正常运行
echo "curl -s -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/ | grep -q '"status":"success"' && echo "OpenSearch 运行正常" || echo "OpenSearch 运行异常""
curl -s -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/ | grep -q '"status":"success"' && echo "OpenSearch 运行正常" || echo "OpenSearch 运行异常"

# 用curl测试opensearch创建测试索引test
echo "curl -s -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/test | grep -q '"status":"success"' && echo "OpenSearch 创建索引成功" || echo "OpenSearch 创建索引失败""
curl -s -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/test | grep -q '"status":"success"' && echo "OpenSearch 创建索引成功" || echo "OpenSearch 创建索引失败"

# opensearch发送测试数据
echo "curl -s -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/test/_doc/1 -X POST -H 'Content-Type: application/json' -d '{\"name\":\"test\",\"age\":18}'"
curl -s -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/test/_doc/1 -X POST -H 'Content-Type: application/json' -d '{\"name\":\"test\",\"age\":18}'

# 查看opensearch的索引的数据  
echo "curl -s -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/test/_search?q=*&size=1"
curl -s -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/test/_search?q=*&size=1

# 删除opensearch的索引及数据
echo "curl -s -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/test/_doc/1 -X DELETE"
curl -s -u ${USER_NAME}:${USER_PASSWORD} https://localhost:9200/test/_doc/1 -X DELETE

# 测试opensearch-dashboards是否能访问
echo "curl -s -u ${USER_NAME}:${USER_PASSWORD} http://localhost:5601/ | grep -q '"status":"success"' && echo "OpenSearch-dashboards 运行正常" || echo "OpenSearch-dashboards 运行异常""
curl -s -u ${USER_NAME}:${USER_PASSWORD} http://localhost:5601/ | grep -q '"status":"success"' && echo "OpenSearch-dashboards 运行正常" || echo "OpenSearch-dashboards 运行异常"
























