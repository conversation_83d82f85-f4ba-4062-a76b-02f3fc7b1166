#!/bin/bash
# debug_pod.sh - 无镜像依赖的K8S Pod调试脚本

set -eo pipefail

# 参数检查
if [ $# -lt 1 ]; then
  echo "用法: $0 <POD_NAME> [NAMESPACE] [COMMAND]"
  echo "示例: $0 poc-intelligence-view-0 oa-llm"
  echo "命令选项:"
  echo "  debug   - 调试模式 (默认)"
  echo "  delete  - 删除模式"
  exit 1
fi

POD_NAME=$1
NAMESPACE=${2:-"oa-llm"}
COMMAND=${3:-"debug"}
TEMP_DIR_NAME=$(date +%Y%m%d%H%M%S)
mkdir -p k8s_backup/${TEMP_DIR_NAME}
BACKUP_DIR="k8s_backup/${TEMP_DIR_NAME}"
# 颜色定义
GREEN="\033[0;32m"
BLUE="\033[0;34m"
RED="\033[0;31m"
YELLOW="\033[1;33m"
NC="\033[0m" # 恢复默认颜色

# 依赖检查
check_dependencies() {
  command -v kubectl >/dev/null 2>&1 || { echo >&2 "需要kubectl但未安装"; exit 1; }
  command -v jq >/dev/null 2>&1 || { echo >&2 "需要jq但未安装"; exit 1; }
}

# 备份原始配置
backup_config() {
  mkdir -p "${BACKUP_DIR}"
  echo "备份原始配置到 ${BACKUP_DIR}"
  kubectl get pod "${POD_NAME}" -n "${NAMESPACE}" -o yaml > "${BACKUP_DIR}/pod.yaml"
}

# 获取控制器信息
get_controller_info() {
  echo "正在获取控制器信息..."
  CONTROLLER_TYPE=$(kubectl get pod "${POD_NAME}" -n "${NAMESPACE}" -o jsonpath='{.metadata.ownerReferences[0].kind}' 2>/dev/null)
  CONTROLLER_NAME=$(kubectl get pod "${POD_NAME}" -n "${NAMESPACE}" -o jsonpath='{.metadata.ownerReferences[0].name}' 2>/dev/null)
  
  echo "控制器类型: ${CONTROLLER_TYPE}"
  echo "控制器名称: ${CONTROLLER_NAME}"
  
  if [[ -z "${CONTROLLER_TYPE}" ]] || [[ -z "${CONTROLLER_NAME}" ]]; then
    echo "无法获取控制器信息，可能是独立Pod"
    HAS_CONTROLLER=false
    return
  fi
  
  HAS_CONTROLLER=true
  
  # 如果控制器是ReplicaSet，尝试获取其父Deployment
  if [ "${CONTROLLER_TYPE}" == "ReplicaSet" ]; then
    echo "检测到ReplicaSet控制器，尝试查找父Deployment..."
    DEPLOYMENT_NAME=$(kubectl get rs "${CONTROLLER_NAME}" -n "${NAMESPACE}" -o jsonpath='{.metadata.ownerReferences[0].name}' 2>/dev/null)
    
    if [ -n "${DEPLOYMENT_NAME}" ]; then
      echo "发现Deployment: ${DEPLOYMENT_NAME}"
      CONTROLLER_TYPE="Deployment"
      CONTROLLER_NAME="${DEPLOYMENT_NAME}"
    fi
  fi
}

# 获取原始容器镜像
get_original_image() {
  ORIGINAL_IMAGE=$(kubectl get pod "${POD_NAME}" -n "${NAMESPACE}" -o jsonpath='{.spec.containers[0].image}')
  echo "原始镜像: ${ORIGINAL_IMAGE}"
}

# 修改控制器配置，创建带debug标识的新控制器
modify_controller() {
  echo -e "${BLUE}正在创建调试版本的 ${CONTROLLER_TYPE} ${CONTROLLER_NAME}-debug ...${NC}"
  
  # 获取原始控制器配置
  kubectl get "${CONTROLLER_TYPE,,}" "${CONTROLLER_NAME}" -n "${NAMESPACE}" -o json > "${BACKUP_DIR}/controller_original.json"
  
  # 读取配置并修改
  local controller_json=$(cat "${BACKUP_DIR}/controller_original.json")
  
  # 获取容器名称
  CONTAINER_NAME=$(kubectl get pod "${POD_NAME}" -n "${NAMESPACE}" -o jsonpath='{.spec.containers[0].name}')
  
  # 修改名称、标签和命令
  local debug_controller_json=$(echo "$controller_json" | jq '
    .metadata.name = .metadata.name + "-debug" |
    .spec.selector.matchLabels.app = .spec.selector.matchLabels.app + "-debug" |
    .spec.template.metadata.labels.app = .spec.template.metadata.labels.app + "-debug" |
    .spec.template.spec.containers[0].command = ["/bin/sh", "-c", "--"] |
    .spec.template.spec.containers[0].args = ["while true; do sleep 30; done;"]
  ')
  
  # 删除资源版本和状态字段，避免冲突
  debug_controller_json=$(echo "$debug_controller_json" | jq 'del(.metadata.resourceVersion, .metadata.uid, .metadata.generation, .status)')
  
  # 保存到文件
  echo "$debug_controller_json" > "${BACKUP_DIR}/debug_controller.json"
  
  # 创建调试控制器
  echo -e "${GREEN}创建调试控制器: ${CONTROLLER_NAME}-debug${NC}"
  kubectl apply -f "${BACKUP_DIR}/debug_controller.json" -n "${NAMESPACE}"
  
  # 保存调试控制器名称，供后续使用
  DEBUG_CONTROLLER_NAME="${CONTROLLER_NAME}-debug"
}

# 等待调试Pod启动
wait_for_pod() {
  echo -e "${YELLOW}等待调试Pod启动...${NC}"
  echo -e "${YELLOW}注意：如果Pod长时间未就绪，请检查Pod容器是否支持/bin/sh命令${NC}"
  
  # 等待新Pod创建
  echo -e "${BLUE}等待调试Pod创建完成...${NC}"
  sleep 5
  
  # 获取调试Pod名称
  if [ "${CONTROLLER_TYPE}" == "StatefulSet" ]; then
    # StatefulSet格式: [controller-name]-[ordinal]
    # 从原Pod名称提取序号
    local pod_ordinal=$(echo "${POD_NAME}" | awk -F'-' '{print $NF}')
    DEBUG_POD_NAME="${DEBUG_CONTROLLER_NAME}-${pod_ordinal}"
  else
    # Deployment/ReplicaSet格式: [controller-name]-[hash]-[hash]
    # 等待任意一个新Pod
    local counter=0
    while [ $counter -lt 30 ]; do
      DEBUG_POD_NAME=$(kubectl get pods -n "${NAMESPACE}" -l "app=${CONTROLLER_NAME}-debug" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
      if [ -n "${DEBUG_POD_NAME}" ]; then
        break
      fi
      echo -e "${YELLOW}等待调试Pod创建... ($counter/30)${NC}"
      sleep 2
      counter=$((counter+1))
    done
  fi
  
  if [ -z "${DEBUG_POD_NAME}" ]; then
    echo -e "${RED}错误: 无法获取调试Pod名称${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}调试Pod名称: ${DEBUG_POD_NAME}${NC}"
  
  # 等待Pod就绪
  counter=0
  while [ $counter -lt 30 ]; do
    STATUS=$(kubectl get pod "${DEBUG_POD_NAME}" -n "${NAMESPACE}" -o jsonpath='{.status.phase}' 2>/dev/null)
    if [[ "$STATUS" == "Running" ]]; then
      echo -e "${GREEN}调试Pod已就绪${NC}"
      break
    fi
    echo -e "${YELLOW}调试Pod状态: ${STATUS}, 等待中... ($counter/30)${NC}"
    sleep 3
    counter=$((counter+1))
  done
  
  if [[ "$STATUS" != "Running" ]]; then
    echo -e "${RED}警告: 调试Pod未达到Running状态，可能出现问题${NC}"
    echo -e "${YELLOW}请检查Pod状态: kubectl describe pod ${DEBUG_POD_NAME} -n ${NAMESPACE}${NC}"
  fi
}

# 进入调试模式
enter_debug() {
  echo -e "${GREEN}进入调试Pod: ${DEBUG_POD_NAME}${NC}"
  kubectl exec -n "${NAMESPACE}" "${DEBUG_POD_NAME}" -it -- /bin/sh
}

# 删除调试控制器和相关资源
delete_debug_controller() {
  if [ -n "${DEBUG_CONTROLLER_NAME}" ]; then
    echo -e "${YELLOW}是否删除调试控制器 ${DEBUG_CONTROLLER_NAME}? (yes/no): ${NC}"
    read -r confirm
    
    if [[ "$confirm" =~ ^[Yy][Ee][Ss]$ ]]; then
      echo -e "${BLUE}删除调试控制器: ${DEBUG_CONTROLLER_NAME}${NC}"
      kubectl delete "${CONTROLLER_TYPE,,}" "${DEBUG_CONTROLLER_NAME}" -n "${NAMESPACE}"
      echo -e "${GREEN}调试控制器已删除${NC}"
    else
      echo -e "${YELLOW}保留调试控制器${NC}"
      echo -e "${BLUE}若要稍后手动删除，请运行:${NC}"
      echo -e "kubectl delete ${CONTROLLER_TYPE,,} ${DEBUG_CONTROLLER_NAME} -n ${NAMESPACE}"
    fi
  else
    echo -e "${YELLOW}未发现调试控制器需要删除${NC}"
  fi
}

# 检查Pod是否处于调试状态
is_pod_in_debug_mode() {
  local pod_name=$1
  local namespace=$2
  
  echo -e "${YELLOW}检查Pod是否处于调试状态...${NC}"
  
  # 获取Pod的容器命令
  local pod_command=$(kubectl get pod $pod_name -n $namespace -o jsonpath='{.spec.containers[0].command}' 2>/dev/null)
  local pod_args=$(kubectl get pod $pod_name -n $namespace -o jsonpath='{.spec.containers[0].args}' 2>/dev/null)
  
  # 检查是否包含典型的调试命令模式
  if [[ "$pod_command" == *"/bin/sh"* ]] && [[ "$pod_args" == *"while true; do sleep"* ]]; then
    echo -e "${GREEN}确认: Pod处于调试状态${NC}"
    return 0  # 是调试状态
  else
    echo -e "${YELLOW}该Pod不是处于调试状态${NC}"
    return 1  # 不是调试状态
  fi
}

# 获取当前命名空间中所有处于调试状态的Pod
list_debug_pods() {
  local namespace=$1
  local debug_pods=()
  
  echo -e "${BLUE}检索命名空间 ${namespace} 中的调试状态Pod...${NC}"
  
  # 方法1: 检查Pod名称中是否包含"-debug-"标记
  echo -e "${YELLOW}方法1: 查找名称包含-debug-的Pod${NC}"
  local name_debug_pods=($(kubectl get pods -n $namespace --no-headers | grep -E ".*-debug-.*" | awk '{print $1}'))
  
  if [ ${#name_debug_pods[@]} -gt 0 ]; then
    echo -e "${GREEN}通过名称找到 ${#name_debug_pods[@]} 个可能的调试Pod${NC}"
    for pod in "${name_debug_pods[@]}"; do
      debug_pods+=("$pod")
      echo -e "  • ${pod} (通过名称匹配)"
    done
  fi
  
  # 方法2: 检查Pod的command/args是否符合调试特征
  echo -e "${YELLOW}方法2: 检查Pod命令行参数${NC}"
  
  # 获取所有Pod
  local all_pods=($(kubectl get pods -n $namespace -o jsonpath='{.items[*].metadata.name}'))
  
  for pod in "${all_pods[@]}"; do
    # 跳过已经通过名称匹配的Pod
    if [[ " ${name_debug_pods[*]} " == *" $pod "* ]]; then
      continue
    fi
    
    # 获取Pod的容器命令和参数
    local pod_command=$(kubectl get pod $pod -n $namespace -o jsonpath='{.spec.containers[0].command}' 2>/dev/null)
    local pod_args=$(kubectl get pod $pod -n $namespace -o jsonpath='{.spec.containers[0].args}' 2>/dev/null)
    
    # 判断是否是调试状态
    if [[ "$pod_command" == *"/bin/sh"* ]] && [[ "$pod_args" == *"while true; do sleep"* ]]; then
      debug_pods+=("$pod")
      echo -e "  • ${pod} (通过命令匹配)"
    fi
  done
  
  # 打印调试Pod列表汇总
  if [ ${#debug_pods[@]} -eq 0 ]; then
    echo -e "${YELLOW}未发现任何处于调试状态的Pod${NC}"
  else
    echo -e "${GREEN}找到 ${#debug_pods[@]} 个处于调试状态的Pod${NC}"
  fi
  
  # 返回调试Pod数组
  DEBUG_PODS=("${debug_pods[@]}")
}

# 删除调试Pod或控制器
delete_resource() {
  echo -e "${RED}===== 删除调试资源操作 =====${NC}"
  
  # 列出当前命名空间中所有调试状态的Pod
  list_debug_pods "${NAMESPACE}"
  
  if [ ${#DEBUG_PODS[@]} -gt 0 ]; then
    echo -e "${YELLOW}请选择要删除的调试Pod:${NC}"
    for i in "${!DEBUG_PODS[@]}"; do
      echo -e "${GREEN}$((i+1)). ${DEBUG_PODS[$i]}${NC}"
    done
    echo -e "${RED}0. 取消操作${NC}"
    
    read -p "请选择 [0-${#DEBUG_PODS[@]}]: " debug_pod_choice
    
    if [[ "$debug_pod_choice" =~ ^[0-9]+$ ]] && [ "$debug_pod_choice" -ge 1 ] && [ "$debug_pod_choice" -le ${#DEBUG_PODS[@]} ]; then
      local selected_debug_pod=${DEBUG_PODS[$((debug_pod_choice-1))]}
      
      echo -e "${YELLOW}您选择了Pod: ${selected_debug_pod}${NC}"
      
      # 获取Pod的控制器信息
      local sel_controller_type=$(kubectl get pod "${selected_debug_pod}" -n "${NAMESPACE}" -o jsonpath='{.metadata.ownerReferences[0].kind}' 2>/dev/null)
      local sel_controller_name=$(kubectl get pod "${selected_debug_pod}" -n "${NAMESPACE}" -o jsonpath='{.metadata.ownerReferences[0].name}' 2>/dev/null)
      
      # 如果控制器是ReplicaSet，尝试获取其父Deployment
      if [ "${sel_controller_type}" == "ReplicaSet" ]; then
        local sel_deployment=$(kubectl get rs "${sel_controller_name}" -n "${NAMESPACE}" -o jsonpath='{.metadata.ownerReferences[0].name}' 2>/dev/null)
        if [ -n "${sel_deployment}" ]; then
          sel_controller_type="Deployment"
          sel_controller_name="${sel_deployment}"
        fi
      fi
      
      echo -e "${BLUE}控制器类型: ${sel_controller_type}${NC}"
      echo -e "${BLUE}控制器名称: ${sel_controller_name}${NC}"
      
      # 是否包含"-debug"标识，通常表示这是我们创建的调试控制器
      if [[ "${sel_controller_name}" == *"-debug"* ]]; then
        echo -e "${YELLOW}这是由调试控制器 ${sel_controller_name} 创建的Pod${NC}"
        echo -e "${YELLOW}请选择操作:${NC}"
        echo -e "1. 仅删除Pod (控制器会创建新Pod)"
        echo -e "2. 删除整个调试控制器 ${sel_controller_name} (删除所有相关Pod)"
        echo -e "3. 取消操作"
        
        read -p "请选择 [1-3]: " del_choice
        case $del_choice in
          1)
            echo -e "${YELLOW}正在删除Pod: ${selected_debug_pod}${NC}"
            kubectl delete pod "${selected_debug_pod}" -n "${NAMESPACE}" --force --grace-period=0
            echo -e "${GREEN}Pod已删除${NC}"
            ;;
          2)
            echo -e "${RED}警告: 将删除整个调试控制器 ${sel_controller_name}${NC}"
            read -p "确认删除? (yes/no): " confirm
            if [ "$confirm" = "yes" ]; then
              echo -e "${YELLOW}正在删除控制器: ${sel_controller_name}${NC}"
              kubectl delete "${sel_controller_type,,}" "${sel_controller_name}" -n "${NAMESPACE}"
              echo -e "${GREEN}控制器已删除${NC}"
            else
              echo -e "${YELLOW}操作已取消${NC}"
            fi
            ;;
          *)
            echo -e "${YELLOW}操作已取消${NC}"
            ;;
        esac
      else
        # 普通调试Pod，直接删除
        read -p "确认删除Pod ${selected_debug_pod}? (yes/no): " confirm
        if [ "$confirm" = "yes" ]; then
          kubectl delete pod "${selected_debug_pod}" -n "${NAMESPACE}" --force --grace-period=0
          echo -e "${GREEN}Pod ${selected_debug_pod} 已删除${NC}"
        else
          echo -e "${YELLOW}操作已取消${NC}"
        fi
      fi
    else
      echo -e "${YELLOW}操作已取消${NC}"
    fi
  else
    echo -e "${YELLOW}没有找到调试状态的Pod${NC}"
  fi
}

# 调试模式主流程
debug_mode() {
  backup_config
  get_original_image
  modify_controller
  wait_for_pod
  enter_debug
  delete_debug_controller
}

# 主流程
main() {
  check_dependencies
  get_controller_info
  
  case "${COMMAND}" in
    debug)
      echo -e "${BLUE}===== 进入调试模式 =====${NC}"
      if [ "${HAS_CONTROLLER}" = true ]; then
        debug_mode
      else
        echo -e "${YELLOW}警告: 未检测到控制器，无法进入调试模式${NC}"
        echo -e "${YELLOW}尝试直接进入容器...${NC}"
        kubectl exec -n "${NAMESPACE}" "${POD_NAME}" -it -- /bin/sh || echo -e "${RED}无法进入容器${NC}"
      fi
      ;;
    delete)
      delete_resource
      ;;
    *)
      echo -e "${RED}未知命令: ${COMMAND}${NC}"
      echo "支持的命令: debug, delete"
      exit 1
      ;;
  esac
}

# 执行
main "$@"