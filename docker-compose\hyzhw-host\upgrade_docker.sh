#!/bin/bash

# 设置错误时退出
set -e

DOCKER_VERSION="24.0.9"
#ARCH="aarch64"
ARCH="x86_64"
DOCKER_TGZ="docker-${DOCKER_VERSION}.tgz"
DOWNLOAD_URL="https://download.docker.com/linux/static/stable/${ARCH}/${DOCKER_TGZ}"

echo "开始Docker升级流程..."

# 1. 备份当前配置
echo "备份Docker配置..."
cp /etc/docker/daemon.json /etc/docker/daemon.json.bak 2>/dev/null || true

# 2. 停止Docker服务
echo "停止Docker服务..."
systemctl stop docker || true

# 3. 卸载旧版本Docker
echo "卸载旧版本Docker..."
(dnf remove -y docker docker-engine docker-ce || yum remove -y docker docker-engine docker-ce) || true


# 4. 下载 Docker 二进制包
echo "下载Docker二进制包..."
cd /usr/local
# 查看 DOCKER_TGZ 是否存在，如果存在就不下载
if [ ! -f "${DOCKER_TGZ}" ]; then
    wget -O ${DOCKER_TGZ} ${DOWNLOAD_URL}
fi
tar -xzvf ${DOCKER_TGZ}
cp -f docker/* /usr/bin/

# 5. 配置 systemd 服务
echo "配置Docker systemd服务..."
cat > /etc/systemd/system/docker.service <<EOF
[Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network-online.target firewalld.service
Wants=network-online.target

[Service]
Type=notify
ExecStart=/usr/bin/dockerd 
ExecReload=/bin/kill -s HUP $MAINPID
TimeoutSec=0
RestartSec=2
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 6. 恢复配置文件
echo "恢复Docker配置..."
mkdir -p /etc/docker
cat > /etc/docker/daemon.json <<EOF
{
    "data-root": "/data/docker",
    "icc": true
}
EOF

# 7. 启动Docker服务
echo "启动Docker服务..."
systemctl daemon-reload
systemctl start docker

# 8. 设置开机自启
echo "设置Docker开机自启..."
systemctl enable docker

# 9. 验证安装
echo "验证Docker安装..."
docker --version
docker info

echo "Docker升级完成！"
