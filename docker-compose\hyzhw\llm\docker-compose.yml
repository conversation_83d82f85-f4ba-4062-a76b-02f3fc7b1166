version: '3.5'

services:
  deepseek70b:
    image: swr.cn-south-1.myhuaweicloud.com/ascendhub/mindie:2.0.T3.1-800I-A2-py311-openeuler24.03-lts
    container_name: deepseek70b-server
    privileged: true
    shm_size: 10g
    ports:
      - "1025:1025"
    networks:
      - jiutianwensi-network
    volumes:
      - ./deepseek70b/shells/start.sh:/usr/local/Ascend/mindie/latest/mindie-service/bin/start.sh
      - ./deepseek70b/config/config.json:/usr/local/Ascend/mindie/latest/mindie-service/conf/config.json:ro
      - ./deepseek70b/logs:/usr/local/Ascend/mindie/latest/mindie-service/logs
      - ./deepseek70b/init-logs:/root/mindie/log/debug/
      # 如有权重目录可补充挂载
      # - /home/<USER>/DeepSeek-R1-Distill-Llama-70B:/data/DeepSeek-R1-Distill-Llama-70B:ro
    devices:
      - /dev/davinci_manager
      - /dev/hisi_hdc
      - /dev/devmm_svm
      - /dev/davinci0
      - /dev/davinci1
      - /dev/davinci2
      - /dev/davinci3
      - /dev/davinci4
      - /dev/davinci5
      - /dev/davinci6
      - /dev/davinci7
    command: ["/bin/bash", "/usr/local/Ascend/mindie/latest/mindie-service/bin/start.sh"]
    restart: always

  filebeat:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
    container_name: filebeat-deepseek70b
    user: root
    networks:
      - jiutianwensi-network
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./deepseek70b/init-logs:/data/deepseek70b/init-logs
    command: ["filebeat", "-e", "-strict.perms=false"]
    restart: always

networks:
  jiutianwensi-network:
    name: jiutianwensi-network 