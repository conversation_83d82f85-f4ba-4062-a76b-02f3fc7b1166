#!/bin/bash
echo "===== Filebeat 故障排除 ====="

# 1. 停止当前的Filebeat容器
echo "1. 停止当前的Filebeat容器"
docker rm -f filebeat 2>/dev/null || true

# 2. 删除并重新创建Filebeat数据目录（重要：这将删除状态！）
echo "2. 清理Filebeat状态数据"
rm -rf /data/filebeat/data/*

# 3. 检查日志文件权限
echo "3. 检查日志文件权限"
for logdir in "/data/znwd/rbac/logs" "/data/znwd/qa/logs" "/data/znwd/aiknowledge-controller/logs"; do
  if [ -d "$logdir" ]; then
    echo "目录 $logdir 存在，设置正确权限"
    chmod -R 755 $logdir
    chmod -R o+r $logdir/*.log 2>/dev/null || true
  else
    echo "警告: 目录 $logdir 不存在，创建目录"
    mkdir -p $logdir
    chmod 755 $logdir
  fi
done

# 4. 执行索引修复脚本
echo "4. 修复OpenSearch索引"
bash fix_mappings.sh

# 5. 重启Filebeat
echo "5. 重启Filebeat"
bash restart_filebeat.sh

# 6. 生成测试日志
echo "6. 生成测试日志"
bash generate_test_logs.sh

# 7. 检查Filebeat日志
echo "7. 检查Filebeat日志"
docker logs filebeat | tail -n 50



# 8. 显示OpenSearch索引状态
echo "8. 显示OpenSearch索引状态"
curl -k -u admin:admin -X GET "https://10.1.4.16:9200/_cat/indices?v"

echo "===== 故障排除完成 ====="
echo "如果问题仍然存在，请执行以下命令检查详细的Filebeat日志:"
echo "docker logs filebeat" 