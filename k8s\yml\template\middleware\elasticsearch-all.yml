apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: elasticsearch-pvc
  namespace: oa-llm
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: {{storage-class-name}}
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: elasticsearch-pv
  namespace: oa-llm
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: {{storage-class-name}}
  nfs:
    server: {{server}}
    path: {{path}}/elasticsearch
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elasticsearch
  namespace: oa-llm
  labels:
    k8s-app: elasticsearch
spec:
  serviceName: elasticsearch
  selector:
    matchLabels:
      k8s-app: elasticsearch
  template:
    metadata:
      labels:
        k8s-app: elasticsearch
    spec:
      containers:
      - image: {{elasticsearch.image-url}}
        name: elasticsearch
        resources:
          limits:
            cpu: 2
            memory: 2Gi
          requests:
            cpu: 2
            memory: 2Gi
        env:
          - name: "discovery.type"
            value: "single-node"
          - name: ES_JAVA_OPTS
            value: "-Xms512m -Xmx2g"
        ports:
        - containerPort: 9200
          name: db
          protocol: TCP
        volumeMounts:
        - name: elasticsearch-persistent-storage
          mountPath: /usr/share/elasticsearch/data
      volumes:
      - name: elasticsearch-persistent-storage
        persistentVolumeClaim:
          claimName: elasticsearch-pvc
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
---
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch
  namespace: oa-llm
spec:
  type: ClusterIP
  ports:
  - port: 9200
    protocol: TCP
    targetPort: 9200
  selector:
    k8s-app: elasticsearch 