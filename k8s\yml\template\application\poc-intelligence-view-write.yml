apiVersion: v1
kind: Service
metadata:
  labels:
    cmcc-gitops-file-name: poc-intelligence-view-write.yaml
    cmcc-gitops-project-tag: oallm
  name: poc-intelligence-view-write
  namespace: oa-llm
spec:
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: poc-intelligence-view-write
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    cmcc-gitops-file-name: poc-intelligence-view-write.yaml
    cmcc-gitops-project-tag: oallm
  name: poc-intelligence-view-write
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: poc-intelligence-view-write
  serviceName: poc-intelligence-view-write
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: poc-intelligence-view-write
    spec:
      containers:
      - image: {{poc-intelligence-view-write.image-url}}
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 5
          tcpSocket:
            port: 80
          timeoutSeconds: 5
        name: poc-intelligence-view-write
        ports:
        - containerPort: 80
          protocol: TCP
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 20
          periodSeconds: 5
          tcpSocket:
            port: 80
          timeoutSeconds: 10
        resources: {}
        volumeMounts:
        - mountPath: /etc/nginx/nginx.conf
          name: poc-intelligence-view-write-config
          subPath: nginx.conf
        - mountPath: /test.txt
          name: poc-intelligence-view-write-config
          subPath: test.txt
        - mountPath: /test.sh
          name: poc-intelligence-view-write-config
          subPath: test.sh
        - mountPath: /var/log/nginx/
          name: poc-intelligence-view-write-logs
      - args:
        - -c
        - /opt/filebeat/filebeat.yml
        - -e
        image: {{filebeat.image-url}}
        imagePullPolicy: Always
        name: filebeat
        resources: {}
        terminationMessagePath: /var/log/err.log
        volumeMounts:
        - mountPath: /opt/filebeat/filebeat.yml
          name: poc-intelligence-view-write-filebeat-cm
          subPath: filebeat.yml
        - mountPath: /poc-intelligence-view-write/data/logs 
          name: poc-intelligence-view-write-logs
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: poc-intelligence-view-write-logs
      - configMap:
          defaultMode: 420
          name: poc-intelligence-view-write-config
        name: poc-intelligence-view-write-config
      - configMap:
          defaultMode: 420
          name: poc-intelligence-view-write-filebeat-cm
        name: poc-intelligence-view-write-filebeat-cm
      imagePullSecrets:
      - name: oa-llm-imagepullsecret

  updateStrategy: {}
status:
  replicas: 0
  availableReplicas: 0
  currentReplicas: 0
  updatedReplicas: 0
  currentRevision: ""
  updateRevision: ""
  collisionCount: 0
  conditions: []
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: poc-intelligence-view-write-config
  namespace: oa-llm
data:
  nginx.conf: |-
    events {
        worker_connections  1000;
    }

    error_log  /var/log/nginx/error.log debug;

    http {
        include       mime.types;
        default_type  application/octet-stream;
        sendfile        on;
        keepalive_timeout  3000;

        log_format  main  '$remote_addr:$remote_port - $remote_user [$time_local] '
                              '"$request_method $request_uri $server_protocol" '
                              '$status $body_bytes_sent '
                              '"$http_referer" "$http_user_agent" "$upstream_addr" ';#"$request_body"
        access_log  /var/log/nginx/access.log main;
    
      

        limit_conn_zone $server_name zone=auth_conn:20m;
        #limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
        limit_req_zone $server_name zone=auth_req:20m rate=1r/s; #确定每个请求发起后的冷却周期


        server {
            listen       80;
            server_name  localhost;

            # 开启gzip压缩
            gzip on;
            gzip_min_length 1k;
            gzip_comp_level 6;
            gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
            gzip_vary on;
            gzip_disable "MSIE [1-6]\.";

            # 静态资源缓存设置
            location / {
                root   /usr/share/nginx/html;
                index  index.html index.htm;
                try_files $uri $uri/ /index.html;
                
                # 缓存设置
                expires 7d;
            }
            location = /poc-intelligence-view-write { 
                return 301 /poc-intelligence-view-write/;
            }

            location ^~ /poc-intelligence-view-write/ {
                alias /usr/share/nginx/html/poc-intelligence-view-write/;
                index index.html;
                try_files $uri $uri/ /poc-intelligence-view-write/index.html;
            }
            location /ai-compose-poc {
                proxy_pass http://article-auto-compose-server.oa-llm:8080/ai-compose-poc;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_http_version 1.1;
                proxy_connect_timeout 3000s;
                proxy_read_timeout 3000s;
                proxy_send_timeout 3000s;
            }


        }

    }
  test.sh: |-
    #!/bin/bash
    # 颜色设置
    GREEN='\033[0;32m'
    BLUE='\033[0;34m'
    RED='\033[0;31m'
    YELLOW='\033[1;33m'
    NC='\033[0m' # 无颜色

    # 测试文本
    test_text="请以《公司关于开展软件测试成本管理专项活动通知》为主题生成一篇通知类型的公文。字数要求100字以上"

    # ====================== 直连服务测试 ======================
    echo -e "${BLUE}=== 直连服务测试 ===${NC}"
    echo -e "${YELLOW}正在测试直连智能写作服务...${NC}"

    # 构建直连请求命令
    cmd="curl -v -X POST \"http://article-auto-compose-server.oa-llm:8080/ai-compose-poc/api/v1/compose\" \
      -H \"Content-Type: application/json\" \
      -d '{
        \"composeUnitId\": \"glmwriter\",
        \"text\": \"${test_text}\"
      }'"

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行直连请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}直连请求耗时: $((end_time - start_time)) 秒${NC}"

    # ====================== 代理服务测试 ======================
    echo -e "${BLUE}=== 代理服务测试 ===${NC}"
    echo -e "${YELLOW}正在测试代理智能写作服务...${NC}"

    # 构建代理请求命令
    cmd="curl -v -X POST \"http://localhost/ai-compose-poc/api/v1/compose\" \
      -H \"Content-Type: application/json\" \
      -d '{
        \"composeUnitId\": \"glmwriter\",
        \"text\": \"${test_text}\",
        \"extension\": {
          \"docInfo\": {
            \"sourceText\": \"公司关于开展软件测试成本管理专项活动通知\",
            \"universalType\": \"通知\",
            \"subTypeName\": \"活动通知\",
            \"mainDeliveryUnit\": \"各部门\"
          }
        }
      }'"

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行代理请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}代理请求耗时: $((end_time - start_time)) 秒${NC}"
    echo
    echo -e "${GREEN}=== 所有测试完成 ===${NC}"
---
apiVersion: v1
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: filestream
      id: my-filestream-id
      paths:
        - /poc-intelligence-view-write/data/logs/*.log
      fields:
        envTag: "oa-llm"
        appTag: "poc-intelligence-view-write"
        namespace: oa-llm
    processors:
    - drop_fields:
        fields: ["agent","input","ecs"]
        ignore_missing: true
    output.elasticsearch:
      hosts: ["http://elasticsearch.oa-llm:9200"]
      index: "poc-intelligence-view-write-logs-%{+yyyy.MM.dd}"
    setup.template.name: "poc-intelligence-view-write-logs"
    setup.template.pattern: "poc-intelligence-view-write-logs*"
    setup.ilm.enabled: false  # 禁用 ILM，避免自动 rollover 到 filebeat-*
kind: ConfigMap
metadata:
  labels:
    cmcc-gitops-project-tag: oa-llm 
  name: poc-intelligence-view-write-filebeat-cm
  namespace: oa-llm