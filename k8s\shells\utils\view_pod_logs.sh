#!/bin/bash

# 查看Pod日志
# 参数: $1 - Pod名称, $2 - 命名空间
function view_pod_logs() {
    local pod_name=$1
    local namespace=$2
    
    if [ -n "$pod_name" ]; then
        # 检查Pod中的容器数量
        CONTAINERS=($(kubectl get pod $pod_name -n $namespace -o jsonpath='{.spec.containers[*].name}'))
        CONTAINER_COUNT=${#CONTAINERS[@]}
        
        if [ $CONTAINER_COUNT -gt 1 ]; then
            echo "Pod $pod_name 包含多个容器："
            for (( i=0; i<$CONTAINER_COUNT; i++ )); do
                echo "[$i] ${CONTAINERS[$i]}"
            done
            read -p "请选择要查看日志的容器序号: " CONTAINER_INDEX
            if [[ $CONTAINER_INDEX =~ ^[0-9]+$ ]] && [ $CONTAINER_INDEX -lt $CONTAINER_COUNT ]; then
                CONTAINER_NAME=${CONTAINERS[$CONTAINER_INDEX]}
                echo "正在查看容器 $CONTAINER_NAME 的日志，按 Ctrl+C 可退出日志查看..."
                
                # 保存当前的SIGINT处理程序
                trap_save=$(trap -p SIGINT)
                
                # 设置新的SIGINT处理程序
                trap "echo '退出日志查看'; return" SIGINT
                
                # 显示最近的10行日志
                echo "最近的10行日志："
                kubectl logs -n $namespace $pod_name -c $CONTAINER_NAME --tail=10
                
                # 使用-f选项持续查看日志
                echo "正在持续监控日志（按Ctrl+C退出监控）..."
                kubectl logs -n $namespace -f $pod_name -c $CONTAINER_NAME
                
                # 恢复原来的SIGINT处理程序
                if [ -z "$trap_save" ]; then
                    trap - SIGINT
                else
                    eval $trap_save
                fi
                
                echo "日志查看结束，继续执行脚本..."
            else
                echo "无效的选择，跳过日志查看"
            fi
        else
            echo "正在查看日志，按 Ctrl+C 可退出日志查看..."
            
            # 保存当前的SIGINT处理程序
            trap_save=$(trap -p SIGINT)
            
            # 设置新的SIGINT处理程序
            trap "echo '退出日志查看'; return" SIGINT
            
            # 显示最近的10行日志
            echo "最近的10行日志："
            kubectl logs -n $namespace $pod_name --tail=10
            
            # 使用-f选项持续查看日志
            echo "正在持续监控日志（按Ctrl+C退出监控）..."
            kubectl logs -n $namespace -f $pod_name
            
            # 恢复原来的SIGINT处理程序
            if [ -z "$trap_save" ]; then
                trap - SIGINT
            else
                eval $trap_save
            fi
            
            echo "日志查看结束，继续执行脚本..."
        fi
    else
        echo "Pod名称为空，无法查看日志"
    fi
}

# 导出函数
export -f view_pod_logs 