#!/bin/bash
# 重启aiknowledge应用的脚本

# 设置应用参数
APP_NAME=aiknowledge
MODUL_NAME=znwd
HOST_PORT=8083
APP_DATA_DIR=/data/${MODUL_NAME}/${APP_NAME}
IMAGE_TAG="0.0.1-SNAPSHOT"

echo "===== 重启 ${APP_NAME} 应用 ====="

# 检查镜像是否存在
if ! docker images ${MODUL_NAME}/${APP_NAME}:${IMAGE_TAG} | grep -q ${IMAGE_TAG}; then
    echo "错误: 镜像 ${MODUL_NAME}/${APP_NAME}:${IMAGE_TAG} 不存在!"
    echo "请先运行部署脚本来创建镜像。"
    exit 1
fi

# 1. 停止并删除已存在的容器
echo "1. 停止并删除容器 ${APP_NAME}..."
docker stop ${APP_NAME} || true
docker rm -f ${APP_NAME} || true
echo "容器已停止并删除"

# 2. 启动新容器
echo "测试命令: docker run -it --rm --name ${APP_NAME}  -v ${APP_DATA_DIR}/config:/app/config -v ${APP_DATA_DIR}/logs:/app/logs -v ${APP_DATA_DIR}/files:/app/files -p ${HOST_PORT}:8080 ${MODUL_NAME}/${APP_NAME}:${IMAGE_TAG} bash"
docker run -it --rm --name ${APP_NAME}  -v ${APP_DATA_DIR}/config:/app/config -v ${APP_DATA_DIR}/logs:/app/logs -v ${APP_DATA_DIR}/files:/app/files -p ${HOST_PORT}:8080 ${MODUL_NAME}/${APP_NAME}:${IMAGE_TAG} bash
echo "进入容器启动：java -jar /app/app.jar --spring.config.location=file:/app/config/ --server.port=8080 --server.servlet.context-path=/ --server.context.path=/  --spring.logging.level.root=info"
