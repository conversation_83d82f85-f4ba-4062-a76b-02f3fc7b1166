
# 创建持久化目录和配置文件
echo "创建持久化目录和配置文件"
echo "mkdir -p /data/redis/{conf,data}"
mkdir -p /data/redis/{conf,data} 
echo "设置权限"
echo "chmod -R 777 /data/redis"
chmod -R 777 /data/redis
echo "创建空配置文件"
echo "touch /data/redis/conf/redis.conf"
touch /data/redis/conf/redis.conf 
# 创建配置文件并写入配置内容（一步完成）
echo "创建配置文件并写入配置内容"
cat << EOF > /data/redis/conf/redis.conf
bind 0.0.0.0                  
protected-mode no             
requirepass admin123      
appendonly yes                
EOF
echo "配置文件内容"
cat /data/redis/conf/redis.conf
# 启动容器
echo "启动容器：docker run -d --name redis -p 6379:6379   -v /data/redis/conf/redis.conf:/etc/redis/redis.conf -v /data/redis/data:/data   --restart always m.daocloud.io/docker.io/redis:7.4.1 redis-server /etc/redis/redis.conf "
docker run -d --name redis -p 6379:6379   -v /data/redis/conf/redis.conf:/etc/redis/redis.conf -v /data/redis/data:/data   --restart always m.daocloud.io/docker.io/redis:7.4.1 redis-server /etc/redis/redis.conf 
echo "查看容器状态"
echo "docker ps | grep redis"
docker ps | grep redis
echo "连接redis"
echo "docker exec -it redis redis-cli -a admin123"
docker exec -it redis redis-cli -a admin123  
echo "设置测试数据"
echo "docker exec -it redis redis-cli -a admin123"
docker exec -it redis redis-cli -a admin123  
echo "set test "hello""
echo "获取测试数据"
echo "docker exec -it redis redis-cli -a admin123"
docker exec -it redis redis-cli -a admin123  
echo "get test"
echo "删除测试数据"
echo "docker exec -it redis redis-cli -a admin123"
docker exec -it redis redis-cli -a admin123  
echo "del test"


