# 惠州市惠阳区政务服务和数据管理局智能公文系统

## 服务连接图
![服务连接图](doc/架构图-部署架构图.png)

部署架构图说明：
- 本系统所有服务均通过宿主机 IP（如 *************）和固定端口对外提供服务。
- nginx-common（20004端口）为统一网关，负责静态资源、操作手册下载等。
- 前端核稿系统（20003端口）和拟文系统（20002端口）分别对应 poc-intelligence-view 和 poc-intelligence-view-write 服务，用户通过浏览器直接访问。
- 后端服务如 ai-doc-poc、ai-redactor、article-auto-compose-server、ai-writer-nostream 分别通过 20005、8000、20006、20008 等端口进行服务调用。
- 大模型服务 DeepSeek-R1-Distill-Llama-70B 通过 1025 端口对接。
- 日志采集由 filebeat 负责，日志上传至 elasticsearch（9200端口），Kibana（5601端口）用于日志和数据可视化。
- 操作手册等静态资源通过 nginx-common 统一对外提供。
- 所有服务均采用 host 网络模式，端口映射与图中一致。
- 接口一览：
  - 核稿前端：http://*************:20003/poc-intelligence-view/
  - 拟文前端：http://*************:20002/poc-intelligence-view-write/intelligent-writing
  - Kibana监控：http://*************:5601
  - 操作手册下载：http://*************:20004/操作手册/AI核稿操作手册.docx
  - 统一网关（nginx-common）：http://*************:20004

## 前端登录

### 核稿登录

在浏览器输入下面的地址

```shell
http://*************:20003/poc-intelligence-view/
```

操作手册下载地址

- http://*************:20004/操作手册/AI核稿操作手册.docx
- http://*************:20004/操作手册/国务院公文测试案例.docx
- http://*************:20004/操作手册/国务院部门文测试案例.docx

### 拟文登录

在浏览器输入下面的地址

```shell
http://*************:20002/poc-intelligence-view-write/intelligent-writing
```

## 部署

### 环境要求

- 操作系统：Windows 10/11 或 Linux
- Docker 20.10.0+
- Docker Compose 2.0.0+
- 服务器建议配置：8核CPU/16GB内存/100GB硬盘

### 部署步骤

1. **安装依赖**

```shell
# Windows 需先安装 Docker Desktop
# Linux 安装命令：
sudo apt-get update && sudo apt-get install -y docker.io docker-compose
```

创建目录

```shell
mkdir -p /opt/jtws
# 清理目录
rm -rf /opt/jtws
mkdir -p /opt/jtws
```

2. 将安装jtws.tar包上传到主机 *************:/data/jtws`
   解压目录

```shell
# 解压到当前目录
cd /opt/jtws
tar -xvf jtws.tar
```

3. **修改配置（可选）**

```shell
# 修改核稿服务配置
vi ai-doc-poc/config/application-prod.yml

# 修改大模型服务配置
vi ai-redactor/config/application.properties

# 修改Elasticsearch配置
vi elasticsearch/config/elasticsearch.yml

# 修改Kibana配置
vi kibana/config/kibana.yml
```

4. **执行部署**

```shell
# 赋予执行权限
chmod +x deploy.sh

# 启动所有服务（首次执行会自动下载镜像）
./deploy.sh
```

5. **验证部署**

```shell
# 查看容器状态
docker ps -a

# 查看实时日志
docker logs -f ai-doc-poc

# 访问服务
echo "Kibana监控：http://*************:5601"
echo "拟文系统：http://*************:20002/poc-intelligence-view-write/intelligent-writing"
echo "核稿系统：http://*************:20003/poc-intelligence-view/"
```

6. 进入容器
   使用 `docker exec -it <服务名> bash` 所有服务信息的请参照服务台账
   以 ai-doc-poc 为例

```shell
docker exec -it ai-doc-poc bash
```

### 服务台账

#### AI核稿前端 (poc-intelligence-view)

服务名称：poc-intelligence-view
镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
IP地址：*************
端口：20003（宿主端口）
网络模式：host
配置文件路径：poc-intelligence-view/config/nginx.conf
日志文件路径：poc-intelligence-view/logs
数据文件路径：poc-intelligence-view/html
容器内端口：80
容器内配置文件路径：/etc/nginx/nginx.conf
容器内日志文件路径：/var/log/nginx
容器内数据文件路径：/usr/share/nginx/html
容器内测试脚本路径：/test.sh

#### 拟文前端 (poc-intelligence-view-write)

服务名称：poc-intelligence-view-write
镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0
IP地址：*************
端口：20002（宿主端口）
网络模式：host
配置文件路径：poc-intelligence-view-write/config/nginx.conf
日志文件路径：poc-intelligence-view-write/logs
数据文件路径：poc-intelligence-view-write/html
容器内端口：80
容器内配置文件路径：/etc/nginx/nginx.conf
容器内日志文件路径：/var/log/nginx
容器内数据文件路径：/usr/share/nginx/html
容器内测试脚本路径：/test.sh

#### 核稿java端服务 (ai-doc-poc)

服务名称：ai-doc-poc
镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT
IP地址：*************
端口：20005（宿主端口）
网络模式：host
配置文件路径：ai-doc-poc/config
日志文件路径：ai-doc-poc/logs
数据文件路径：ai-doc-poc/shells
容器内配置文件路径：/app/config
容器内日志文件路径：/app/logs
容器内脚本路径：/app/shells
环境变量：JAVA_OPTS=-Xms512m -Xmx1g

#### 核稿python端服务 (ai-redactor)

服务名称：ai-redactor
镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18
IP地址：*************
端口：20006（宿主端口）
网络模式：host
配置文件路径：ai-redactor/config/application.properties
日志文件路径：ai-redactor/logs
数据文件路径：ai-redactor/shells
容器内配置文件路径：/app/ai_hegao_plus_app/application.properties
容器内日志文件路径：/app/log
容器内脚本路径：/app/shells

#### 拟文java端服务 (article-auto-compose-server)

服务名称：article-auto-compose-server
镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT
IP地址：*************
端口：20007（宿主端口）
网络模式：host
配置文件路径：article-auto-compose-server/config
日志文件路径：article-auto-compose-server/logs
数据文件路径：article-auto-compose-server/shells
容器内配置路径：/app/config
容器内日志文件路径：/app/logs
容器内脚本路径：/app/shells

#### 拟文python端服务 (ai-writer-nostream)

服务名称：ai-writer-nostream
镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python:v528
IP地址：*************
端口：20008（宿主端口）
网络模式：host
配置文件路径：ai-writer-nostream/config/application.properties
日志文件路径：ai-writer-nostream/logs
数据文件路径：ai-writer-nostream/shells
容器内配置路径：/app/temp/web_source_code/backend/application.properties
容器内日志文件路径：/app/temp/web_source_code/log
容器内脚本路径：/app/shells

#### ELK监控组件 (elasticsearch)

服务名称：elasticsearch
镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
IP地址：*************
端口：9200, 9300（宿主端口）
网络模式：host
配置文件路径：elasticsearch/config/elasticsearch.yml
日志文件路径：elasticsearch/logs
数据存储路径：elasticsearch/data
容器内配置文件路径：/usr/share/elasticsearch/config/elasticsearch.yml
容器内日志文件路径：/usr/share/elasticsearch/logs
容器内数据文件路径：/usr/share/elasticsearch/data
环境变量：
- discovery.type=single-node
- ES_JAVA_OPTS=-Xms1g -Xmx1g
- xpack.security.enabled=true
- ELASTIC_PASSWORD=elastic@123
- bootstrap.memory_lock=true

#### 可视化监控 (kibana)

服务名称：kibana
镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14
IP地址：*************
访问端口：20001（宿主端口）
网络模式：host
容器内端口：5601
配置文件路径：kibana/config/kibana.yml
数据文件路径：kibana/data
容器内配置文件路径：/usr/share/kibana/config/kibana.yml
容器内数据文件路径：/usr/share/kibana/data
环境变量：
- TZ=Asia/Shanghai
- ELASTICSEARCH_USERNAME=elastic
- ELASTICSEARCH_PASSWORD=elastic@123

#### 统一网关 (nginx-common)

服务名称：nginx-common
镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4
IP地址：*************
访问端口：20004
网络模式：host
配置文件路径：nginx-common/config/nginx.conf
日志路径：nginx-common/logs
数据文件路径：nginx-common/shells
容器内配置文件路径：/etc/nginx/nginx.conf
容器内日志文件路径：/var/log/nginx
容器内测试脚本路径：/test.sh

#### 日志采集 (filebeat-common)

服务名称：filebeat-common
镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
IP地址：*************
网络模式：host
配置文件路径：filebeat/config/filebeat.yml
日志文件路径：filebeat/logs
容器内配置路径：/usr/share/filebeat/filebeat.yml
容器内日志路径：/var/log/filebeat
日志采集路径：
- /data/ai-doc-poc/logs
- /data/ai-redactor/logs
- /data/ai-writer-nostream/logs
- /data/article-auto-compose-server/logs
- /data/poc-intelligence-view-write/logs
- /data/poc-intelligence-view/logs
- /data/nginx-common/logs
环境变量：
- TZ=Asia/Shanghai
- strict.perms=false

### 配置文件说明

#### elasticsearch/config/elasticsearch.yml

| 参数名                 | 说明     | 默认值             |
| ---------------------- | -------- | ------------------ |
| cluster.name           | 集群名称 | jtws-elasticsearch |
| node.name              | 节点名称 | node-1             |
| network.host           | 绑定主机 | 0.0.0.0            |
| http.port              | HTTP端口 | 9200               |
| discovery.type         | 发现类型 | single-node        |
| xpack.security.enabled | 安全认证 | true               |

#### kibana/config/kibana.yml

| 参数名                 | 说明     | 默认值                        |
| ---------------------- | -------- | ----------------------------- |
| server.port            | 服务端口 | 5601                          |
| server.host            | 绑定主机 | 0.0.0.0                       |
| elasticsearch.hosts    | ES地址   | ["http://localhost:9200"] |
| elasticsearch.username | ES用户名 | elastic                       |
| elasticsearch.password | ES密码   | elastic@123                   |

#### ai-doc-poc/config/application-prod.yml

| 参数名                                    | 说明           | 默认值                                |
| ----------------------------------------- | -------------- | ------------------------------------- |
| server.port                               | 服务端口       | 8080                                  |
| server.servlet.context-path               | 上下文路径     | /ai-doc-poc                           |
| server.shutdown                           | 关闭方式       | graceful                              |
| spring.application.name                   | 应用名称       | ai-doc                                |
| spring.servlet.multipart.location         | 文件上传路径   | /app/files                            |
| spring.servlet.multipart.max-request-size | 总文件大小限制 | 100MB                                 |
| spring.servlet.multipart.max-file-size    | 单文件大小限制 | 100MB                                 |
| ai-service.providers[0].base-url          | 模型服务地址   | http://localhost:8000/spellcheck/oa |
| ai-service.providers[1].base-url          | 模型服务地址   | http://localhost:8000/spellcheck/oa |
| logging.config                            | 日志配置路径   | /app/config/logback.xml               |

#### filebeat/config/filebeat.yml

| 参数名               | 说明         | 默认值                        |
| -------------------- | ------------ | ----------------------------- |
| filebeat.inputs      | 日志输入配置 | type: log, paths: [...]       |
| output.elasticsearch | ES输出配置   | hosts: ["http://localhost:9200"] |
| setup.kibana         | Kibana设置   | host: "http://localhost:5601"           |

#### ai-redactor/config/application.properties

| 参数名     | 说明          | 默认值                                    |
| ---------- | ------------- | ----------------------------------------- |
| model-url  | 大模型API地址 | http://localhost:1025/v1/chat/completions |
| api-key    | API密钥       | -                                         |
| model-name | 模型名称      | DeepSeek-R1-Distill-Llama-70B                          |

#### ai-writer-nostream/config/application.properties

| 参数名                 | 说明            | 默认值                                                                  |
| ---------------------- | --------------- | ----------------------------------------------------------------------- |
| model.url              | 大模型API地址   | http://localhost:1025/v1/chat/completions                               |
| model.appid            | 应用ID          | tyrknosa                                                                |
| model.appKey           | 应用密钥        | 5ab43270aecc9f482b79c965ab81d411                                        |
| model.capabilityname   | 能力名称        | semantic0000000000000000                                                |
| model.model-name       | 模型名称        | DeepSeek-R1-Distill-Llama-70B                                                        |
| path.sensitiveWords    | 敏感词文件路径  | /app/temp/web_source_code/backend/sensitive_words_all.txt               |
| path.rulesFile         | 规则文件路径    | /app/temp/web_source_code/backend/re.json                               |
| path.templatesDir      | 模板目录路径    | /app/temp/web_source_code/backend/writing_template                      |
| generation.maxTokens   | 最大生成token数 | 1024                                                                    |
| generation.temperature | 模型温度参数    | 0.2                                                                     |
| system.prompt          | 系统提示词      | 你是一个公文写作专家，公文内不要出现"我们"、"我"、"你们"等口语化词汇... |

#### article-auto-compose-server/config/application-prod.yml

| 参数名                                    | 说明                   | 默认值                                                |
| ----------------------------------------- | ---------------------- | ----------------------------------------------------- |
| server.port                               | 服务端口               | 8080                                                  |
| server.servlet.context-path               | 上下文路径             | /ai-compose-poc                                       |
| server.shutdown                           | 关闭方式               | graceful                                              |
| spring.application.name                   | 应用名称               | ai-compose                                            |
| spring.servlet.multipart.location         | 文件上传路径           | /app/files                                            |
| spring.servlet.multipart.max-request-size | 总文件大小限制         | 100MB                                                 |
| spring.servlet.multipart.max-file-size    | 单文件大小限制         | 100MB                                                 |
| ai-service.providers[0].base-url          | 模型服务地址           | http://localhost:20008/mubanwriter/v1/service |
| moa.check.checkUnitId                     | 核稿使用的模型ID       | SQUARE                                                |
| moa.check.attachmentExpire                | 核稿结果文件有效期(天) | 7                                                     |
| logging.config                            | 日志配置路径           | /app/config/logback.xml                               |

#### nginx-common/config/nginx.conf

| 配置块   | 参数               | 说明         | 示例值                 |
| -------- | ------------------ | ------------ | ---------------------- |
| http     | worker_connections | 工作连接数   | 1024                   |
| http     | keepalive_timeout  | 连接保持时间 | 65                     |
| server   | listen             | 监听端口     | 80                     |
| server   | server_name        | 服务器名称   | localhost              |
| location | proxy_pass         | 代理转发地址 | http://localhost:20005 |
| location | proxy_set_header   | 代理头设置   | Host $host             |

#### poc-intelligence-view/config/nginx.conf

| 配置块   | 参数      | 说明         | 示例值                   |
| -------- | --------- | ------------ | ------------------------ |
| http     | sendfile  | 启用零拷贝   | on                       |
| server   | listen    | 监听端口     | 80                       |
| server   | root      | 网站根目录   | /usr/share/nginx/html    |
| server   | index     | 默认索引     | index.html               |
| location | try_files | 文件查找规则 | $uri $uri/ /index.html |

#### poc-intelligence-view-write/config/nginx.conf

| 配置块                                                        | 参数      | 说明         | 示例值                   |
| ------------------------------------------------------------- | --------- | ------------ | ------------------------ |
| http                                                          | sendfile  | 启用零拷贝   | on                       |
| server                                                        | listen    | 监听端口     | 80                       |
| server                                                        | root      | 网站根目录   | /usr/share/nginx/html    |
| server                                                        | index     | 默认索引     | index.html               |
| location                                                      | try_files | 文件查找规则 | $uri $uri/ /index.html |
| 注意：修改配置文件后，重启服务：`docker restart <服务名称>` |           |              |                          |

## 运维


### 日志查询

#### EFK日志

1.登录kibana：http://*************:5601 用户名/密码：elastic/elastic@123
2.然后转到：http://*************:5601/app/discover，里面有每个服务的日志，查询语法，请参照ELK官网的KQL语法

#### 容器内查询

1.进入容器

```shell
docker exec -it <服务名称> bash
```

2.根据服务台账上的信息，找到日志的路径

```shell
tail -f <日志路径>/<日志文件名>
```

### 服务管理

#### 服务重启

```shell
docker restart <服务名称>
```

#### 服务停止

```shell
docker stop <服务名称>
```

#### 服务删除

```shell
docker rm -f <服务名称>
```

#### 服务删除

```shell
docker rm -f <服务名称>
```
### 接口测试
根据服务台账上的信息，找到测试脚本的路径，执行测试脚本
```shell
#进入容器
docker exec -it <服务名称> bash
#执行测试脚本
cd <测试脚本路径>
sh test.sh
```
### 更新版本
每当有版本更新，按照邮件中的更新说明，更新版本
