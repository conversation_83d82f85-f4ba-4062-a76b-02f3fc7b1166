# BCE Embedding服务

基于[bce-embedding-base_v1](https://modelscope.cn/models/maidalun/bce-embedding-base_v1)模型的HTTP服务。

## 环境要求

- Python 3.7或更高版本
- PyTorch 1.9.0或更高版本
- 其他依赖见`requirements.txt`

## 快速开始

### 使用已下载的模型

本服务默认从`./bce-embedding-base_v1`目录加载模型。如果您已经下载了模型，请确保将模型文件放在此目录下，或设置环境变量`MODEL_PATH`指向您的模型目录：

```bash
# Windows
set MODEL_PATH=你的模型路径
# Linux/macOS
export MODEL_PATH=你的模型路径
```

### 在Windows上运行

直接双击`run.bat`或在命令行中运行：

```bash
run.bat
```

### 在Linux上运行

首先给脚本添加执行权限：

```bash
chmod +x run.sh
```

然后运行脚本：

```bash
./run.sh
```

如果需要指定模型路径，可以设置环境变量：

```bash
export MODEL_PATH=/path/to/your/model
./run.sh
```

### 手动启动

1. 安装依赖：

```bash
pip install -r requirements.txt
```

2. 启动服务：

```bash
python app.py
```

默认情况下，服务将在`http://localhost:8000`上运行。

## API接口

### 1. 健康检查

- **URL**: `/health`
- **方法**: `GET`
- **返回示例**:
  ```json
  {"status": "ok"}
  ```

### 2. 单文本嵌入

- **URL**: `/embed`
- **方法**: `POST`
- **Content-Type**: `application/json`
- **请求体**:
  ```json
  {
    "text": "需要嵌入的文本"
  }
  ```
- **返回示例**:
  ```json
  {
    "text": "需要嵌入的文本",
    "embedding": [0.1, 0.2, ...],
    "dimensions": 768
  }
  ```

### 3. 批量文本嵌入

- **URL**: `/embed_batch`
- **方法**: `POST`
- **Content-Type**: `application/json`
- **请求体**:
  ```json
  {
    "texts": ["文本1", "文本2", "文本3"]
  }
  ```
- **返回示例**:
  ```json
  {
    "results": [
      {
        "text": "文本1",
        "embedding": [0.1, 0.2, ...],
        "dimensions": 768
      },
      {
        "text": "文本2",
        "embedding": [0.3, 0.4, ...],
        "dimensions": 768
      },
      ...
    ]
  }
  ```

### 4. OpenAI兼容的嵌入接口

- **URL**: `/embeddings`
- **方法**: `POST`
- **Content-Type**: `application/json`
- **请求体**:
  ```json
  {
    "texts": ["需要嵌入的文本1", "需要嵌入的文本2"]
  }
  ```
  或
  ```json
  {
    "input": ["需要嵌入的文本1", "需要嵌入的文本2"]
  }
  ```
- **返回示例** (OpenAI兼容格式):
  ```json
  {
    "data": [
      {
        "object": "embedding",
        "embedding": [0.1, 0.2, ...],
        "index": 0
      },
      {
        "object": "embedding",
        "embedding": [0.3, 0.4, ...],
        "index": 1
      }
    ],
    "model": "bce-embedding-base",
    "object": "list",
    "usage": {
      "prompt_tokens": 10,
      "total_tokens": 10
    }
  }
  ```

## 日志系统

服务包含完整的日志记录功能：

- 日志文件保存在 `logs` 目录下
- 日志文件命名格式为 `bce_embedding_service_YYYYMMDD_HHMMSS.log`
- 日志同时输出到控制台和文件
- 记录了服务启动、模型加载、请求处理等关键信息

日志级别：
- INFO: 正常操作信息
- ERROR: 错误和异常情况

查看日志文件可以帮助您了解服务运行状况和诊断问题。

## 测试客户端

使用提供的测试脚本测试服务：

```bash
python test_client.py
```

## CURL测试样例

我们提供了一个curl测试脚本，可以直接从命令行测试API：

```bash
# 添加执行权限
chmod +x test_curl.sh

# 运行测试脚本
./test_curl.sh
```

也可以直接使用以下命令进行测试：

### 健康检查接口
```bash
curl -X GET http://localhost:8000/health
```

### 单文本嵌入接口
```bash
curl -X POST http://localhost:8000/embed \
  -H "Content-Type: application/json" \
  -d '{"text": "这是一个测试文本"}'
```

### 批量文本嵌入接口
```bash
curl -X POST http://localhost:8000/embed_batch \
  -H "Content-Type: application/json" \
  -d '{"texts": ["第一个测试文本", "第二个测试文本"]}'
```

### OpenAI兼容接口
```bash
curl -X POST http://localhost:8000/embeddings \
  -H "Content-Type: application/json" \
  -d '{"texts": ["这是一个OpenAI兼容的测试"]}'
```

## 参考

- 模型来源：https://modelscope.cn/models/maidalun/bce-embedding-base_v1
- 官方示例：https://github.com/netease-youdao/BCEmbedding