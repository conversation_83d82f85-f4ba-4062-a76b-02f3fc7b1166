#!/bin/bash

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色
APP_BASE_DIR=$1
# 定义变量
ES_USERNAME="elastic"
ES_PASSWORD="Elastic20250417@#"
COMPOSE_DIR="${APP_BASE_DIR}/elasticsearch_kibana"
ES_DATA_DIR="${APP_BASE_DIR}/elasticsearch/data"
ES_LOG_DIR="${APP_BASE_DIR}/elasticsearch/logs"
KIBANA_DATA_DIR="${APP_BASE_DIR}/kibana/data"

# 检查Docker和Docker Compose是否安装
check_requirements() {
  echo -e "${YELLOW}检查必要组件...${NC}"
  if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker未安装${NC}"
    exit 1
  fi
  
  if ! docker compose version &> /dev/null && ! docker-compose --version &> /dev/null; then
    echo -e "${RED}错误: Docker Compose未安装${NC}"
    exit 1
  fi
  echo -e "${GREEN}所有必要组件已安装!${NC}"
}

# 显示服务状态
status() {
  echo -e "${YELLOW}检查服务状态...${NC}"
  if docker ps | grep -q "elasticsearch"; then
    echo -e "${GREEN}Elasticsearch正在运行${NC}"
    # 显示集群健康状态
    echo "集群健康状态:"
    curl -s -u ${ES_USERNAME}:${ES_PASSWORD} "http://localhost:9200/_cluster/health?pretty"
    # 显示节点信息
    echo "节点信息:"
    curl -s -u ${ES_USERNAME}:${ES_PASSWORD} "http://localhost:9200/_cat/nodes?v"
    # 显示索引信息
    echo "索引信息:"
    curl -s -u ${ES_USERNAME}:${ES_PASSWORD} "http://localhost:9200/_cat/indices?v"
  else
    echo -e "${RED}Elasticsearch未运行${NC}"
  fi
  
  if docker ps | grep -q "kibana"; then
    echo -e "${GREEN}Kibana正在运行${NC}"
  else
    echo -e "${RED}Kibana未运行${NC}"
  fi
}

# 启动服务
start() {
  echo -e "${YELLOW}启动Elasticsearch和Kibana...${NC}"
  if [ -f "${COMPOSE_DIR}/docker-compose.yml" ]; then
    cd ${COMPOSE_DIR} && docker-compose up -d
    echo -e "${GREEN}服务启动命令已执行，请等待服务完全启动${NC}"
    # 等待服务启动
    wait_for_services
  else
    echo -e "${RED}错误: docker-compose文件不存在，请先运行deploy.sh脚本${NC}"
    exit 1
  fi
}

# 停止服务
stop() {
  echo -e "${YELLOW}停止Elasticsearch和Kibana...${NC}"
  if [ -f "${COMPOSE_DIR}/docker-compose.yml" ]; then
    cd ${COMPOSE_DIR} && docker-compose stop
    echo -e "${GREEN}服务已停止${NC}"
  else
    echo -e "${RED}错误: docker-compose文件不存在${NC}"
    exit 1
  fi
}

# 重启服务
restart() {
  echo -e "${YELLOW}重启Elasticsearch和Kibana...${NC}"
  if [ -f "${COMPOSE_DIR}/docker-compose.yml" ]; then
    cd ${COMPOSE_DIR} && docker-compose restart
    echo -e "${GREEN}服务重启命令已执行，请等待服务完全启动${NC}"
    # 等待服务启动
    wait_for_services
  else
    echo -e "${RED}错误: docker-compose文件不存在${NC}"
    exit 1
  fi
}

# 等待服务启动
wait_for_services() {
  echo -e "${YELLOW}等待Elasticsearch启动...${NC}"
  MAX_TRIES=20
  COUNTER=0
  DELAY=5
  
  while [ $COUNTER -lt $MAX_TRIES ]; do
    echo "尝试 $((COUNTER+1))/$MAX_TRIES: 检查Elasticsearch是否可访问..."
    
    if curl -s -m 5 -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://localhost:9200/" | grep -q "version"; then
      echo -e "${GREEN}Elasticsearch服务已成功启动!${NC}"
      break
    fi
    
    COUNTER=$((COUNTER+1))
    
    if [ $COUNTER -eq $MAX_TRIES ]; then
      echo -e "${RED}在 $((MAX_TRIES * DELAY)) 秒内无法连接到Elasticsearch${NC}"
    else
      echo "Elasticsearch尚未准备好，等待 ${DELAY} 秒后重试..."
      sleep $DELAY
    fi
  done
  
  echo -e "${YELLOW}等待Kibana启动...${NC}"
  MAX_TRIES=20
  COUNTER=0
  
  while [ $COUNTER -lt $MAX_TRIES ]; do
    echo "尝试 $((COUNTER+1))/$MAX_TRIES: 检查Kibana是否可访问..."
    
    if curl -s -m 5 -I "http://localhost:5601" | grep -q "200 OK"; then
      echo -e "${GREEN}Kibana服务已成功启动!${NC}"
      break
    fi
    
    COUNTER=$((COUNTER+1))
    
    if [ $COUNTER -eq $MAX_TRIES ]; then
      echo -e "${YELLOW}警告: 在 $((MAX_TRIES * DELAY)) 秒内无法连接到Kibana${NC}"
      echo "Kibana可能需要更多时间初始化，请稍后手动检查"
    else
      echo "Kibana尚未准备好，等待 ${DELAY} 秒后重试..."
      sleep $DELAY
    fi
  done
}

# 查看日志
logs() {
  SERVICE=$1
  LINES=${2:-100}  # 默认显示100行
  
  if [ "$SERVICE" == "elasticsearch" ]; then
    echo -e "${YELLOW}查看Elasticsearch日志 (最近 ${LINES} 行):${NC}"
    docker logs --tail ${LINES} elasticsearch
  elif [ "$SERVICE" == "kibana" ]; then
    echo -e "${YELLOW}查看Kibana日志 (最近 ${LINES} 行):${NC}"
    docker logs --tail ${LINES} kibana
  else
    echo -e "${RED}错误: 未知服务 ${SERVICE}${NC}"
    echo "可用服务: elasticsearch, kibana"
    exit 1
  fi
}

# 清理数据(慎用)
clean_data() {
  echo -e "${RED}警告: 此操作将删除所有Elasticsearch和Kibana数据!${NC}"
  echo -e "${RED}所有索引和存储的数据将被永久删除!${NC}"
  read -p "确定要继续吗? (y/n): " confirm
  
  if [ "$confirm" == "y" ] || [ "$confirm" == "Y" ]; then
    echo -e "${YELLOW}停止服务...${NC}"
    cd ${COMPOSE_DIR} && docker-compose stop
    
    echo -e "${YELLOW}清理数据目录...${NC}"
    rm -rf ${ES_DATA_DIR}/*
    rm -rf ${ES_LOG_DIR}/*
    rm -rf ${KIBANA_DATA_DIR}/*
    
    echo -e "${GREEN}数据已清理${NC}"
    echo "要重新启动服务，请运行: $0 start"
  else
    echo -e "${YELLOW}操作已取消${NC}"
  fi
}

# 备份索引
backup() {
  INDEX_NAME=$1
  BACKUP_DIR="/data/elasticsearch/backups"
  
  if [ -z "$INDEX_NAME" ]; then
    echo -e "${RED}错误: 请指定要备份的索引名称${NC}"
    echo "例如: $0 backup my-index"
    exit 1
  fi
  
  # 创建备份目录
  mkdir -p ${BACKUP_DIR}
  chmod 777 ${BACKUP_DIR}
  
  # 注册备份仓库(如果尚未注册)
  echo -e "${YELLOW}注册备份仓库...${NC}"
  curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X PUT "http://localhost:9200/_snapshot/my_backup" -H 'Content-Type: application/json' -d'
  {
    "type": "fs",
    "settings": {
      "location": "/usr/share/elasticsearch/backups"
    }
  }' | grep -q "acknowledged\":true" && echo -e "${GREEN}备份仓库注册成功或已存在${NC}" || echo -e "${RED}备份仓库注册失败${NC}"
  
  # 执行备份
  SNAPSHOT_NAME="${INDEX_NAME}_$(date +%Y%m%d%H%M%S)"
  echo -e "${YELLOW}开始备份索引 ${INDEX_NAME} 到快照 ${SNAPSHOT_NAME}...${NC}"
  curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X PUT "http://localhost:9200/_snapshot/my_backup/${SNAPSHOT_NAME}?wait_for_completion=true" -H 'Content-Type: application/json' -d"
  {
    \"indices\": \"${INDEX_NAME}\",
    \"ignore_unavailable\": true,
    \"include_global_state\": false
  }" | grep -q "snapshot\":{\"snapshot\":\"${SNAPSHOT_NAME}\"" && echo -e "${GREEN}备份成功完成${NC}" || echo -e "${RED}备份失败${NC}"
  
  # 列出可用备份
  echo -e "${YELLOW}可用备份:${NC}"
  curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://localhost:9200/_snapshot/my_backup/_all?pretty"
}

# 恢复索引
restore() {
  SNAPSHOT_NAME=$1
  INDEX_NAME=$2
  
  if [ -z "$SNAPSHOT_NAME" ] || [ -z "$INDEX_NAME" ]; then
    echo -e "${RED}错误: 请指定快照名称和索引名称${NC}"
    echo "例如: $0 restore my-snapshot my-index"
    echo -e "${YELLOW}可用备份:${NC}"
    curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://localhost:9200/_snapshot/my_backup/_all?pretty"
    exit 1
  fi
  
  # 执行恢复
  echo -e "${YELLOW}开始从快照 ${SNAPSHOT_NAME} 恢复索引 ${INDEX_NAME}...${NC}"
  curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X POST "http://localhost:9200/_snapshot/my_backup/${SNAPSHOT_NAME}/_restore?wait_for_completion=true" -H 'Content-Type: application/json' -d"
  {
    \"indices\": \"${INDEX_NAME}\",
    \"ignore_unavailable\": true,
    \"include_global_state\": false,
    \"rename_pattern\": \"${INDEX_NAME}\",
    \"rename_replacement\": \"${INDEX_NAME}_restored\"
  }" && echo -e "${GREEN}恢复操作已启动${NC}" || echo -e "${RED}恢复操作失败${NC}"
  
  # 检查恢复后的索引
  echo -e "${YELLOW}恢复后的索引:${NC}"
  curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://localhost:9200/_cat/indices/${INDEX_NAME}_restored?v"
}

# 显示帮助信息
show_help() {
  echo "ES和Kibana管理脚本 - 用法:"
  echo ""
  echo "  $0 status               - 显示服务状态和健康信息"
  echo "  $0 start                - 启动服务"
  echo "  $0 stop                 - 停止服务"
  echo "  $0 restart              - 重启服务"
  echo "  $0 logs elasticsearch   - 查看Elasticsearch日志"
  echo "  $0 logs kibana          - 查看Kibana日志"
  echo "  $0 logs elasticsearch 500 - 查看Elasticsearch最近500行日志"
  echo "  $0 clean                - 清理所有数据(慎用!)"
  echo "  $0 backup INDEX_NAME    - 备份指定索引"
  echo "  $0 restore SNAPSHOT INDEX - 从快照恢复索引"
  echo "  $0 help                 - 显示此帮助信息"
  echo ""
  echo "访问信息:"
  echo "  Elasticsearch: http://服务器IP:9200"
  echo "  Kibana: http://服务器IP:5601"
  echo "  用户名/密码: ${ES_USERNAME}/${ES_PASSWORD}"
}

# 检查是否以root用户运行
if [ "$(id -u)" != "0" ]; then
   echo -e "${RED}错误: 此脚本需要以root用户运行${NC}"
   exit 1
fi

# 主函数
main() {
  # 检查必要组件
  check_requirements
  
  # 命令行参数处理
  case "$1" in
    status)
      status
      ;;
    start)
      start
      ;;
    stop)
      stop
      ;;
    restart)
      restart
      ;;
    logs)
      logs $2 $3
      ;;
    clean)
      clean_data
      ;;
    backup)
      backup $2
      ;;
    restore)
      restore $2 $3
      ;;
    help|--help|-h)
      show_help
      ;;
    *)
      show_help
      exit 1
      ;;
  esac
}

# 执行主函数
main "$@" 