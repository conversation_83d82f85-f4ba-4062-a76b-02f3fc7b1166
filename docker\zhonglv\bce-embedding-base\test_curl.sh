#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 服务地址
API_HOST="http://localhost:8000"

echo -e "${BLUE}BCE Embedding 服务 - CURL 测试示例${NC}"
echo "==============================================="

# 测试健康检查接口
echo -e "\n${GREEN}1. 测试健康检查接口 (/health)${NC}"
echo "命令:"
echo "curl -X GET ${API_HOST}/health"
echo "结果:"
curl -s -X GET "${API_HOST}/health" | jq -C '.' || echo -e "${RED}请求失败或未安装jq${NC}"

# 测试单文本嵌入接口
echo -e "\n${GREEN}2. 测试单文本嵌入接口 (/embed)${NC}"
echo "命令:"
echo "curl -X POST ${API_HOST}/embed -H \"Content-Type: application/json\" -d '{\"text\": \"这是一个测试文本\"}'"
echo "结果:"
curl -s -X POST "${API_HOST}/embed" \
  -H "Content-Type: application/json" \
  -d '{"text": "这是一个测试文本"}' | jq -C '.dimensions' || echo -e "${RED}请求失败或未安装jq${NC}"

# 测试批量文本嵌入接口
echo -e "\n${GREEN}3. 测试批量文本嵌入接口 (/embed_batch)${NC}"
echo "命令:"
echo "curl -X POST ${API_HOST}/embed_batch -H \"Content-Type: application/json\" -d '{\"texts\": [\"第一个测试文本\", \"第二个测试文本\"]}'"
echo "结果:"
curl -s -X POST "${API_HOST}/embed_batch" \
  -H "Content-Type: application/json" \
  -d '{"texts": ["第一个测试文本", "第二个测试文本"]}' | jq -C '.results | length' || echo -e "${RED}请求失败或未安装jq${NC}"

# 测试OpenAI兼容的embeddings接口
echo -e "\n${GREEN}4. 测试OpenAI兼容接口 (/embeddings)${NC}"
echo "命令:"
echo "curl -X POST ${API_HOST}/embeddings -H \"Content-Type: application/json\" -d '{\"texts\": [\"这是一个OpenAI兼容的测试\"]}'"
echo "结果:"
curl -s -X POST "${API_HOST}/embeddings" \
  -H "Content-Type: application/json" \
  -d '{"texts": ["这是一个OpenAI兼容的测试"]}' | jq -C '.data[0].index' || echo -e "${RED}请求失败或未安装jq${NC}"

echo -e "\n${BLUE}简单使用示例 (可复制到终端执行)${NC}"
echo "-----------------------------------------------"
echo "# 健康检查"
echo "curl -X GET ${API_HOST}/health"
echo
echo "# 单文本嵌入"
echo "curl -X POST ${API_HOST}/embed -H \"Content-Type: application/json\" -d '{\"text\": \"这是一个测试文本\"}'"
echo
echo "# 批量文本嵌入"
echo "curl -X POST ${API_HOST}/embed_batch -H \"Content-Type: application/json\" -d '{\"texts\": [\"第一个测试文本\", \"第二个测试文本\"]}'"
echo
echo "# OpenAI兼容接口"
echo "curl -X POST ${API_HOST}/embeddings -H \"Content-Type: application/json\" -d '{\"texts\": [\"这是一个OpenAI兼容的测试\"]}'"
echo "-----------------------------------------------"

echo -e "\n${BLUE}测试完成${NC}" 