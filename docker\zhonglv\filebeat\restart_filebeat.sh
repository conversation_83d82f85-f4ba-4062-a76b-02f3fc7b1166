#!/bin/bash
echo "重启Filebeat容器"

# 停止并删除现有容器
echo "停止并删除旧容器"
docker rm -f filebeat 2>/dev/null || true

# 启动Filebeat容器
echo "启动Filebeat容器"
IMAGE_VERSION=8.18.0
IMAGE_NAME=docker.elastic.co/beats/filebeat

docker run -d \
  --name=filebeat \
  --user=root \
  --volume="/data/filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro" \
  --volume="/data/filebeat/data:/usr/share/filebeat/data:rw" \
  --volume="/data/filebeat/logs:/var/log/filebeat:rw" \
  --volume="/data/znwd/rbac/logs:/data/znwd/rbac/logs:ro" \
  --volume="/data/znwd/qa/logs:/data/znwd/qa/logs:ro" \
  --volume="/data/znwd/aiknowledge-controller/logs:/data/znwd/aiknowledge-controller/logs:ro" \
  $IMAGE_NAME:$IMAGE_VERSION \
  filebeat -e --strict.perms=false

# 查看日志
echo "Filebeat日志："
sleep 5
docker logs filebeat | tail -n 20 