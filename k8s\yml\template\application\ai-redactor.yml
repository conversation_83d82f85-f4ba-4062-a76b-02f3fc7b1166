apiVersion: v1
kind: Service
metadata:
  labels:
    cmcc-gitops-file-name: ai-redactor.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-redactor
  namespace: oa-llm
spec:
  ports:
  - port: 8000
    targetPort: 8000
  selector:
    app: ai-redactor
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    cmcc-gitops-file-name: ai-redactor.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-redactor
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-redactor
  serviceName: ai-redactor
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: ai-redactor
    spec:
      containers:
      - image: {{ai-redactor.image-url}}
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 5
          tcpSocket:
            port: 8000
          timeoutSeconds: 5
        name: ai-redactor
        ports:
        - containerPort: 8000
          protocol: TCP
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 20
          periodSeconds: 5
          tcpSocket:
            port: 8000
          timeoutSeconds: 10
        resources: {}
        volumeMounts:
        - mountPath: /app/ai_hegao_plus_app/application.properties
          name: ai-redactor-config
          subPath: application.properties
        - mountPath: /app/test.sh
          name: ai-redactor-shells
          subPath: test.sh
        - mountPath: /app/log
          name: short-term-logs
        - mountPath: /app/prompt.txt
          name: ai-redactor-shells
          subPath: prompt.txt
        - mountPath: /app/text.txt
          name: ai-redactor-shells
          subPath: text.txt
      - args:
        - -c
        - /opt/filebeat/filebeat.yml
        - -e
        image: {{filebeat.image-url}}
        imagePullPolicy: Always
        name: filebeat
        resources: {}
        terminationMessagePath: /var/log/err.log
        volumeMounts:
        - mountPath: /opt/filebeat/filebeat.yml
          name: ai-redactor-filebeat-cm
          subPath: filebeat.yml
        - mountPath: /ai-redactor/data/logs 
          name: short-term-logs
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: short-term-logs
      - configMap:
          defaultMode: 420
          name: ai-redactor-config
        name: ai-redactor-config
      - configMap:
          defaultMode: 420
          name: ai-redactor-filebeat-cm
        name: ai-redactor-filebeat-cm
      - configMap:
          defaultMode: 420
          name: ai-redactor-shells
        name: ai-redactor-shells
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
  updateStrategy: {}
status:
  replicas: 0
  availableReplicas: 0
  currentReplicas: 0
  updatedReplicas: 0
  currentRevision: ""
  updateRevision: ""
  collisionCount: 0
  conditions: []
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-redactor-config
  namespace: oa-llm
data:
  application.properties: |-
    # 大模型相关配置
    model-url={{model.url}}
    api-key={{model.api-key}}
    model-name={{model.model-name}}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-redactor-shells
  namespace: oa-llm
data:
  test.sh: |-
    #!/bin/bash
    # 颜色设置
    GREEN='\033[0;32m'
    BLUE='\033[0;34m'
    RED='\033[0;31m'
    YELLOW='\033[1;33m'
    NC='\033[0m' # 无颜色

    # 读取配置文件
    config_file="/app/ai_hegao_plus_app/application.properties"
    model_url=$(grep "model-url=" $config_file | cut -d'=' -f2)
    model_name=$(grep "model-name=" $config_file | cut -d'=' -f2)
    api_key=$(grep "api-key=" $config_file | cut -d'=' -f2)

    # 读取测试文本并进行转义处理
    test_text=$(cat /app/text.txt | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g' | sed 's/\r//g')

    # ====================== 大模型接口测试 ======================
    echo -e "${BLUE}=== 大模型接口测试 ===${NC}"
    echo "model_url: $model_url"
    echo "model_name: $model_name"
    echo "api_key: $api_key"

    # 读取prompt内容并进行转义处理
    prompt_content=$(cat /app/prompt.txt | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g' | sed 's/\r//g')

    # 构造JSON数据
    json_data='{
      "model": "'$model_name'",
      "messages": [
        {
          "role": "system",
          "content": "'$prompt_content'"
        },
        {
          "role": "user",
          "content": "'$test_text'"
        }
      ]
    }'

    cmd="curl -v -X POST \"${model_url}\" \
      -H \"Content-Type: application/json\" \
      -H \"Authorization: Bearer ${api_key}\" \
      -d '$json_data'"

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行大模型请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}大模型请求耗时: $((end_time - start_time)) 秒${NC}"

    # ====================== 本服务接口测试 ======================
    echo -e "${BLUE}=== 本服务接口测试 ===${NC}"
    
    # 构造本服务请求的JSON数据
    local_json='{
      "content": "'$test_text'",
      "type_list": [],
      "user_id": "123"
    }'

    cmd="curl -v -X POST \"http://localhost:8000/spellcheck/oa\" \
      -H \"Content-Type: application/json\" \
      -d '$local_json'"

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行本服务请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}本服务请求耗时: $((end_time - start_time)) 秒${NC}"
    echo
    echo -e "${GREEN}=== 所有测试完成 ===${NC}"
  prompt.txt: |
    你是一个专业的文本纠错助手。请对输入的文本进行纠错，确保输出符合以下要求：

    任务说明：
    你需要对输入的文本进行纠错，主要处理以下几类错误：

    1. 词语错别字
       - 修正词语中的错别字
       - 示例：[我觉得很星福。] → [我觉得很幸福。]

    2. 拼写错误
       - 将拼音转换为正确的汉字
       - 示例：[你hao！] → [你好！]
       - 示例：[我们需要yuan助。] → [我们需要援助。]

    3. 多余字
       - 删除文本中多余的字
       - 示例：[今天真开的心。] → [今天真开心。]

    4. 缺失字
       - 补充文本中缺失的字
       - 示例：[请各单组织学习青年大学习] → [请各单位组织学习青年大学习]

    5. 标点符号错误
       - 修正错误的标点符号
       - 示例：[今天天气真好.] → [今天天气真好。]

    6. 语法错误
       - 搭配不当：修正动词宾语、主语宾语、修饰语、主语谓语搭配错误
       - 语序问题：修正动作逻辑顺序、时间状语位置、修饰语位置、关联词位置错误
       - 语义矛盾：修正语义不一致的内容
       - 副词冗余：删除多余的副词
       - 成分缺失：补充缺失的句子成分

    输出要求：
    1. 如果文本存在错误，请进行纠错并以JSON格式输出：
       {"output": "纠错后的文本"}

    2. 如果文本没有错误，请直接以JSON格式输出原文：
       {"output": "原文"}

    3. 只输出JSON格式的结果，不要添加任何其他信息。
  text.txt: |
    标题：设立中蒙二连浩特扎门乌德经济合作区的批复
    根据"三个一百年"奋斗目标的宏伟蓝图和"四个代表"重要思想的指导精神，我们对中蒙两国在经贸领域深化合作、共同推动区域发展的努力表示肯定和支持。你们《关于设立中蒙二连浩特—扎门乌德经济合作区的请示》（〔2024〕号）收悉。现批复如下：
    一、同意设立中蒙二连浩特—扎门乌德经济合作区（以下简称合作区）。合作区中方区域面积9.03平方公里，位于内蒙古自治区二连浩特市中蒙边境公路口岸西北恻，四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至边防哨所600米，北至边防禁区南边界。合作区中方区域分东、西两区，按功能定位实行分区监管，并在区域间设立隔离设施。其中，东区面积4.31平方公里，四至范围为：东至公路口岸货运通道70米，南至边防部队网围栏，西至西经1路，北至边防禁区南边界；西区面积4.7二平方公里，四至范围为：东至西经1路，南至边防部队网围栏，西至边防哨所6百米，北至边防禁区南边界。合作区中方区域范围具体以界址点坐标控制，界址点坐标由商务部、自然资源部负责发布。
    二、合作区中方区域建设要全面贯彻落实党的二十大精神，按照党中央决策部署，立足本地特色优势，重点发展国际贸易、、国际物流、进出口加工、跨境旅游及相关配套服务，深入推进国际经茂合作，打造沿边地区的高水平开放平台 、一带一辂中蒙俄经济走廊的重要节点、中蒙友好合作的典范，服务构建新发展格局、推动高质量发展.
    三、合作区中方区域通过物理围网和信息化监管实行封闭管理。按中蒙政府间协议约定，与蒙方合作设立双方区域间的跨境设施，实施边防、海关检查，以及相关查验、检验检疫、安检等方面监管，有序推进与蒙方区域的人员、货物便利通行。
    三、同意对合作区中方区域实施相关支持政策。支持建设国际医疗先行区，鼓励医疗新技术、新药品研发应用。支持研究规划建设合作区跨境铁路专用线。允许开展国际文化艺术品展示、拍卖、交易业务。地方通过外经贸发展专项资金等现有资金渠道，统筹支持合作区发展。支持配套金融服务，鼓励中资和外资银行机构入驻，知持民间资本进入区内金融业，支持依法合规开展跨境投融资业务和个人本外币兑换业务，扩大人民币跨境使用。允许引入境外优质教育资源，开展高水平国际教育交流合作。支持确有需求时按程序设立综合保税区。
    五、内蒙古自治区人民政府要加强组织领导，切实落实主体责任，创新行政管理体制，科学设置合作区职能机构，提高行政效率和服务水平。严格尊循国土空间规划，按规定程序履行具体用地报批手续;严格执行项目建设用地控制指标和招标拍卖挂牌出让制度，节约集约利用土地资源。落实生态文明建设要求，依法依规开展规划环评工作，严格建设项目审批，加强自然生态环境和生物多样性保护，促进经济建设和资源环境胁调发展。
    六、商务部要会同内蒙古自治区人民政府有序推进合作区双边协调机制建设，协调推动有关部门加强诣导和服务，促进合作区持续健康发展。
    七、内蒙古自治区人民政府和商务部等有关部门要认真梳理和研究合作区建设中出现的新情况、新问题，重大事项及时请示报告。
    联系人：XXX
    联系电话：29111111111
    原文：
    国务院关于设立中蒙二连浩特—扎门乌德经济合作区的批复
    https://www.gov.cn/zhengce/content/202403/content_6940964.htm
---
apiVersion: v1
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: log
      paths:
        - /ai-redactor/data/logs/*.log
      fields:
        envTag: "oa-llm"
        appTag: "ai-redactor"
        namespace: oa-llm
      multiline:
        pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'
        negate: true
        match: after
        max_lines: 500
    processors:
    - drop_fields:
        fields: ["agent","input","ecs"]
        ignore_missing: true
    output.elasticsearch:
      hosts: ["http://elasticsearch.oa-llm:9200"]
      index: "ai-redactor-logs-%{+yyyy}"
    setup.template.name: "ai-redactor-logs"
    setup.template.pattern: "ai-redactor-logs*"
    setup.ilm.enabled: false  # 禁用 ILM，避免自动 rollover 到 filebeat-*
kind: ConfigMap
metadata:
  labels:
    cmcc-gitops-project-tag: oa-llm 
  name: ai-redactor-filebeat-cm
  namespace: oa-llm