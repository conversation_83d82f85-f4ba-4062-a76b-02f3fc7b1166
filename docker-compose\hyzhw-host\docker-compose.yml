version: '3.5'

services:
  elasticsearch:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=elastic@123
      - bootstrap.memory_lock=true
    volumes:
      - ./elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - ./elasticsearch/data:/usr/share/elasticsearch/data
      - ./elasticsearch/logs:/usr/share/elasticsearch/logs
    network_mode: host
    restart: always
  kibana:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14
    container_name: kibana
    environment:
      - TZ=Asia/Shanghai
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD=elastic@123
    volumes:
      - ./kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml
      - ./kibana/data:/usr/share/kibana/data
    network_mode: host
    restart: always
    depends_on:
      - elasticsearch
  poc-intelligence-view-write:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0
    container_name: poc-intelligence-view-write
    volumes:
      - ./poc-intelligence-view-write/config/nginx.conf:/etc/nginx/nginx.conf
      - ./poc-intelligence-view-write/shells/test.txt:/test.txt
      - ./poc-intelligence-view-write/shells/test.sh:/test.sh
      - ./poc-intelligence-view-write/logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    command: ["/bin/bash", "-c", "chmod -R 755 /usr/share/nginx/html && nginx -g 'daemon off;'"]  
    network_mode: host
    restart: always
  poc-intelligence-view:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
    container_name: poc-intelligence-view
    volumes:
      - ./poc-intelligence-view/config/nginx.conf:/etc/nginx/nginx.conf
      - ./poc-intelligence-view/shells/test.txt:/test.txt
      - ./poc-intelligence-view/shells/test.sh:/test.sh
      - ./poc-intelligence-view/logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    command: ["/bin/bash", "-c", "chmod -R 755 /usr/share/nginx/html && nginx -g 'daemon off;'"]  
    network_mode: host
    restart: always
  nginx-common:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4
    container_name: nginx-common
    volumes:
      - ./nginx-common/config/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx-common/shells/test.txt:/test.txt
      - ./nginx-common/shells/test.sh:/test.sh
      - ./nginx-common/logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    network_mode: host
    restart: always      
  ai-doc-poc:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT
    container_name: ai-doc-poc
    volumes:
      - ./ai-doc-poc/logs:/app/logs
      - ./ai-doc-poc/config:/app/config
      - ./ai-doc-poc/shells:/app/shells
    environment:
      - JAVA_OPTS=-Xms512m -Xmx1g
    network_mode: host
    restart: always
  ai-redactor:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18
    container_name: ai-redactor
    volumes:
      - ./ai-redactor/config/application.properties:/app/ai_hegao_plus_app/application.properties
      - ./ai-redactor/logs:/app/log
      - ./ai-redactor/shells:/app/shells
    environment:
      - TZ=Asia/Shanghai
    network_mode: host
    restart: always 
  article-auto-compose-server:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT
    container_name: article-auto-compose-server
    volumes:
      - ./article-auto-compose-server/logs:/app/logs
      - ./article-auto-compose-server/config:/app/config
      - ./article-auto-compose-server/shells:/app/shells
    environment:
      - TZ=Asia/Shanghai
    network_mode: host
    restart: always      
  ai-writer-nostream:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python:v528
    container_name: ai-writer-nostream
    volumes:
      - ./ai-writer-nostream/config/application.properties:/app/temp/web_source_code/backend/application.properties
      - ./ai-writer-nostream/logs:/app/temp/web_source_code/log
      - ./ai-writer-nostream/shells:/app/shells
    environment:
      - TZ=Asia/Shanghai
    network_mode: host
    restart: always
  filebeat-common:
    image: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
    container_name: filebeat-common
    user: root
    volumes:
      - ./filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./filebeat/logs:/var/log/filebeat
      - ./ai-doc-poc/logs:/data/ai-doc-poc/logs
      - ./ai-redactor/logs:/data/ai-redactor/logs
      - ./ai-writer-nostream/logs:/data/ai-writer-nostream/logs
      - ./article-auto-compose-server/logs:/data/article-auto-compose-server/logs
      - ./poc-intelligence-view-write/logs:/data/poc-intelligence-view-write/logs
      - ./poc-intelligence-view/logs:/data/poc-intelligence-view/logs
      - ./nginx-common/logs:/data/nginx-common/logs
    environment:
      - TZ=Asia/Shanghai
      - strict.perms=false
    network_mode: host
    restart: always