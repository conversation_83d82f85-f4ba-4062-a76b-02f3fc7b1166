version: '3'

services:
  qa:
    image: znwd/qa:0.0.1-SNAPSHOT
    container_name: qa
    ports:
      - "8083:8080"
    volumes:
      - ${BASE_DIR:-/data/znwd}/qa/config:/app/config
      - ${BASE_DIR:-/data/znwd}/qa/logs:/app/logs
      - ${BASE_DIR:-/data/znwd}/qa/data:/app/data
    environment:
      - JAVA_OPTS=-Xms512m -Xmx1g
      - TZ=Asia/Shanghai
    restart: always
    networks:
      - jiutianwensi-network

networks:
  jiutianwensi-network:
    external: true 