#!/bin/bash

# 定义变量
USERNAME="wensi"
PASSWORD="HP7!y]ERc9_eiQ-M"  # 在这里直接定义密码

# 检查用户是否存在
if id "$USERNAME" &>/dev/null; then
    echo "用户 $USERNAME 已经存在，跳过创建步骤。"
else
    # 创建用户 wensi
    echo "正在创建用户 $USERNAME ..."
    sudo useradd -m -s /bin/bash $USERNAME || { echo "用户创建失败"; exit 1; }
fi

# 设置密码
echo "正在设置用户密码 ..."
if echo "$USERNAME:$PASSWORD" | sudo chpasswd 2>/dev/null; then
    echo "密码设置成功。"
else
    echo "密码设置失败：可能是密码不符合系统安全策略。"
    echo "请确保密码满足以下要求："
    echo "- 至少8个字符"
    echo "- 不包含用户名"
    echo "- 包含大小写字母、数字和特殊字符"
    exit 1
fi

# 配置免密 sudo
echo "正在配置用户 $USERNAME 免密 sudo ..."
SUDOERS_FILE="/etc/sudoers.d/$USERNAME"

# 写入免密 sudo 配置
echo "$USERNAME ALL=(ALL) NOPASSWD:ALL" | sudo tee "$SUDOERS_FILE" > /dev/null
sudo chmod 440 "$SUDOERS_FILE"  # 确保权限正确

echo "免密 sudo 配置完成。"

# 提示完成
echo "配置完成！"