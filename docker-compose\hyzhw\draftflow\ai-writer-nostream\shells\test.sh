#!/bin/bash
# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色
PORT=8080
# 读取配置文件
config_file="/app/temp/web_source_code/backend/application.properties"
model_url=$(grep "model.url=" $config_file | cut -d'=' -f2)
model_name=$(grep "model-name=" $config_file | cut -d'=' -f2)
api_key=$(grep "api-key=" $config_file | cut -d'=' -f2)

# 测试文本
test_text="请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求1800字以上。"

# ====================== 大模型接口测试 ======================
echo -e "${BLUE}=== 大模型接口测试 ===${NC}"
echo "model_url: $model_url"
echo "model_name: $model_name"
echo "api_key: $api_key"

# 构建大模型请求命令
cmd="curl -v -X POST \"${model_url}\" -H \"Content-Type: application/json\" -H \"Authorization: Bearer ${api_key}\" -d '{\"model\": \"${model_name}\", \"messages\": [{\"role\": \"system\", \"content\": \"你是一个公文写作专家，公文内不要出现\\\"我们\\\"、\\\"我\\\"、\\\"你们\\\"等口语化词汇，也不需要带入主送单位\"}, {\"role\": \"user\", \"content\": \"${test_text}\"}]}'"

# 打印并执行命令
echo -e "${YELLOW}执行命令:${NC}"
echo "$cmd"
start_time=$(date +%s)
eval "$cmd"
end_time=$(date +%s)
echo
echo -e "${GREEN}大模型请求耗时: $((end_time - start_time)) 秒${NC}"

# ====================== 简单请求测试 ======================
echo -e "${BLUE}=== 简单请求测试 ===${NC}"
cmd="curl -v -X POST \"http://127.0.0.1:${PORT}/mubanwriter/v1/service\" -H \"Content-Type: application/json\" -d '{\"text\": \"${test_text}\"}'"

# 打印并执行命令
echo -e "${YELLOW}执行命令:${NC}"
echo "$cmd"
start_time=$(date +%s)
eval "$cmd"
end_time=$(date +%s)
echo
echo -e "${GREEN}简单请求耗时: $((end_time - start_time)) 秒${NC}"

# ====================== 带extension的请求测试 ======================
echo -e "${BLUE}=== 带extension的请求测试 ===${NC}"
cmd="curl -v -X POST \"http://127.0.0.1:${PORT}/mubanwriter/v1/service\" -H \"Content-Type: application/json\" -d '{\"text\": \"${test_text}\", \"extension\": {\"docInfo\": {\"sourceText\": \"关于召开项目进度评审会议的通知\", \"universalType\": \"通知\", \"subTypeName\": \"会议通知\", \"mainDeliveryUnit\": \"各部门负责人\"}, \"fileContent\": {\"modelEssay\": [\"为确保项目按计划推进，解决项目过程中的问题，特召开本次项目进度评审会议。\"]}}}'"

# 打印并执行命令
echo -e "${YELLOW}执行命令:${NC}"
echo "$cmd"
start_time=$(date +%s)
eval "$cmd"
end_time=$(date +%s)
echo -e "${GREEN}带extension的请求耗时: $((end_time - start_time)) 秒${NC}"
echo
echo -e "${GREEN}=== 所有测试完成 ===${NC}"