# !/usr/bin/env python
# -*-coding:utf-8 -*-
import json
import os
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# ENV_PROFILE = os.environ.get("ENV")

MODLE_APPID = "gwywznnw"
MODLE_APPKEY = "6a52abb3f1a55c41159ae9f96a2e09ad"
MODLE_CNAME = "qwen-32b-instruct"
MODEL_URL = "http://192.168.21.2:30000/stream-jwt/CIDC-RP-207/inference-proxy/2185090615607296/aiops-1340745253490184192/qwen-32b-instruct/service/8080/v1/chat/completions"
API_KEY = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3Mzk4MDM2ODIsImtleSI6InpsLTIwMjUwMjE2LTEifQ.vrHTFufptkAyCqzmJIYg9kzvTjpVpuPuiC_EZPwprG8"
LIMIT_TIME = 60


LOCAL_DATA_PATH = "/app/web_source_code/local_data/"
FAISS_DATA_PAHT = "/app/web_source_code/local_data/公文语料库2024-12-24.xlsx"

logger.info("MODLE_APPID === " + str(MODLE_APPID))
logger.info("MODLE_APPKEY === " + str(MODLE_APPKEY))
logger.info("MODLE_CNAME === " + str(MODLE_CNAME))
logger.info("MODEL_URL === " + str(MODEL_URL))
logger.info("LIMIT_TIME === " + str(60))
logger.info("LOCAL_DATA_PATH === " + str(LOCAL_DATA_PATH))
logger.info("FAISS_DATA_PAHT === " + str(FAISS_DATA_PAHT))