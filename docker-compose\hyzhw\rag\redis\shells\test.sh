echo "查看容器状态"
echo "docker ps | grep redis"
docker ps | grep redis
echo "连接redis"
echo "docker exec -it redis redis-cli -a admin123"
docker exec -it redis redis-cli -a admin123  
echo "设置测试数据"
echo "docker exec -it redis redis-cli -a admin123"
docker exec -it redis redis-cli -a admin123  
echo "set test "hello""
echo "获取测试数据"
echo "docker exec -it redis redis-cli -a admin123"
docker exec -it redis redis-cli -a admin123  
echo "get test"
echo "删除测试数据"
echo "docker exec -it redis redis-cli -a admin123"
docker exec -it redis redis-cli -a admin123  
echo "del test"