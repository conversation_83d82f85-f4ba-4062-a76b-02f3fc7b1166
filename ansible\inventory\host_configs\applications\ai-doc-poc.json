{"app_name": "ai-doc-poc", "module_name": "hegao", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "0.0.1-SNAPSHOT", "image_name": "hegao/ai-doc-poc:0.0.1-SNAPSHOT", "container_port": "8080", "host_port": "8093", "app_data_dir": "/app/files", "app_logs_dir": "/app/logs", "app_config_dir": "/app/config", "host_data_dir": "{{base_dir}}/ai-doc-poc/data", "host_logs_dir": "{{base_dir}}/ai-doc-poc/log", "host_config_dir": "{{base_dir}}/ai-doc-poc/config", "restart_script": "{{base_dir}}/ai-doc-poc/restart.sh", "test_script": "{{base_dir}}/ai-doc-poc/test_curl.sh", "runtime": "java17", "env_vars": [{"name": "JAVA_OPTS", "value": "-Xms512m -Xmx1g"}], "external_dependencies": [{"type": "service", "name": "ai-hegao-python", "url": "http://ai-hegao-python:8080"}, {"type": "middleware", "name": "postgresql", "url": "postgresql:5432"}], "test_commands": ["curl -s http://localhost:8093/actuator/health | grep UP"]}