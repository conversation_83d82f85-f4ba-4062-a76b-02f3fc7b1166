{"app_name": "ai-hegao-python", "module_name": "hegao", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "v1", "image_name": "hegao/ai-hegao-python:v1", "container_port": "8080", "host_port": "8097", "app_data_dir": "/app/data", "app_logs_dir": "/app/web_source_code/log", "app_config_dir": "/app/config/application.properties", "host_data_dir": "{{base_dir}}/hegao/ai-hegao-python/data", "host_logs_dir": "{{base_dir}}/hegao/ai-hegao-python/logs", "host_config_dir": "{{base_dir}}/hegao/ai-hegao-python/config", "restart_script": "{{base_dir}}/hegao/ai-hegao-python/restart.sh", "test_script": "{{base_dir}}/hegao/ai-hegao-python/test_curl.sh", "runtime": "python", "env_vars": [{"name": "PYTHONPATH", "value": "/app"}, {"name": "TZ", "value": "Asia/Shanghai"}], "external_dependencies": [{"type": "service", "name": "deepseek-32B", "url": "https://ecloud.10086.cn/api/openapi-icp/inference-api/2185090615607296/aiops-1340745253490184192/deepseek-r1-distill-qwen-32b/service/8080/v1/chat/completions"}], "test_commands": ["curl -s http://localhost:8097/health | grep OK"]}