version: '3'

services:
  rabbitmq:
    image: m.daocloud.io/docker.io/rabbitmq:3.12-management
    container_name: rabbitmq
    ports:
      - "5672:5672"  # AMQP协议端口
      - "15672:15672"  # 管理界面端口
    volumes:
      - ${BASE_DIR:-/data/rabbitmq}/log:/var/log/rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASS:-admin123}
      - TZ=Asia/Shanghai
    restart: always
    networks:
      - jiutianwensi-network
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  jiutianwensi-network:
    external: true 