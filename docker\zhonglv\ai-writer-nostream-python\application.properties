# 大模型相关配置
model.url=http://192.168.21.2:30000/stream-jwt/CIDC-RP-207/inference-proxy/2185090615607296/aiops-1340745253490184192/qwen-32b-instruct/service/8080/v1/chat/completions
model.appid=tyrknosa
model.appKey=5ab43270aecc9f482b79c965ab81d411
model.capabilityname=semantic0000000000000000
api-key=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3Mzk4MDM2ODIsImtleSI6InpsLTIwMjUwMjE2LTEifQ.vrHTFufptkAyCqzmJIYg9kzvTjpVpuPuiC_EZPwprG8
model-name=qwen-32b-instruct

# 文件路径配置
path.sensitiveWords=/app/temp/web_source_code/backend/sensitive_words_all.txt
path.rulesFile=/app/temp/web_source_code/backend/re.json
path.templatesDir=/app/temp/web_source_code/backend/writing_template

# 生成参数配置
generation.maxTokens=1024
generation.temperature=0.2

# 系统配置
system.prompt=你是一个公文写作专家，公文内不要出现“我们”、“我”、“你们”等口语化词汇，也不需要带入主送单位 