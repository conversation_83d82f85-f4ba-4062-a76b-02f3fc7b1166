server {
    listen 32100;
    server_name _;

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    location ~ \.md$ {
        root /usr/share/nginx/html;
        add_header Content-Type "text/plain; charset=utf-8";
    }
    location ~* \.(png|jpg|jpeg|gif|svg)$ {
        root /usr/share/nginx/html;
        add_header Content-Type "";
    }
}