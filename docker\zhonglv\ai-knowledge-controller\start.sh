#!/bin/bash
# aiknowledge服务启动脚本

# 应用日志
LOG_DIR="/app/logs"
mkdir -p $LOG_DIR
APP_NAME=aiknowledge-controller
CONFIG_PROPERTIES_FILE=/app/config/config.properties
LOG_CONFIG_FILE=/app/config/logback-spring.xml

# 启动应用
echo "$(date) - 启动 ${APP_NAME} 服务..." >> $LOG_DIR/startup.log

# 读取配置文件
echo "读取配置文件$CONFIG_PROPERTIES_FILE"
echo "原始配置文件内容："
cat $CONFIG_PROPERTIES_FILE

# 使用grep过滤注释行和空行，然后转换为启动参数
PROPS_ARGS=""
while IFS= read -r line; do
  # 忽略空行和注释行
  if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# ]]; then
    PROPS_ARGS="$PROPS_ARGS --$line"
  fi
done < <(grep -v "^#" $CONFIG_PROPERTIES_FILE | grep -v "^[[:space:]]*$")

echo "转换后的启动参数： $PROPS_ARGS"

# 构建完整的启动命令
STARTUP_CMD="java -jar /app/app.jar \
  --spring.config.location=file:/app/config/ \
  --server.port=8080 \
  --server.servlet.context-path=/ \
  --spring.profiles.active=prod \
  --logging.config=$LOG_CONFIG_FILE"

# 如果有配置参数，添加到命令中
if [ ! -z "$PROPS_ARGS" ]; then
  STARTUP_CMD="$STARTUP_CMD $PROPS_ARGS"
fi

echo "启动命令： $STARTUP_CMD"

# 执行启动命令
eval $STARTUP_CMD

# 获取启动结果
RESULT=$?
if [ $RESULT -eq 0 ]; then
  echo "$(date) - ${APP_NAME} 服务启动成功!" >> $LOG_DIR/startup.log
else
  echo "$(date) - ${APP_NAME} 服务启动失败，退出码: $RESULT" >> $LOG_DIR/startup.log
fi 