{"app_name": "rag-kg-os", "module_name": "znwd", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "v1", "image_name": "znwd/rag-kg-os:v1", "container_port": "8080", "host_port": "8099", "app_data_dir": "/app/kg_opensearch_config/data", "app_logs_dir": "/app/kg_opensearch_config/log", "app_config_dir": "/app/kg_opensearch_config/kg_app/service/configs.py", "host_data_dir": "{{base_dir}}/znwd/rag-kg-os/data", "host_logs_dir": "{{base_dir}}/znwd/logs", "host_config_dir": "{{base_dir}}/znwd/rag-kg-os/config", "restart_script": "{{base_dir}}/znwd/rag-kg-os/restart.sh", "test_script": "{{base_dir}}/znwd/rag-kg-os/test_curl.sh", "runtime": "python", "env_vars": [{"name": "PYTHONPATH", "value": "/app/kg_opensearch_config"}, {"name": "TZ", "value": "Asia/Shanghai"}], "external_dependencies": [{"type": "middleware", "name": "opensearch", "url": "opensearch:9200"}, {"type": "service", "name": "embedding-service-bge-m3", "url": "http://embedding-service-bge-m3:8080"}], "test_commands": ["curl -s http://localhost:8099/health | grep OK"]}