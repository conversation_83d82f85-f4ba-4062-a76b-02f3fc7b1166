#!/bin/bash
echo "开始部署Filebeat"
# 改用7.x版本的Filebeat
IMAGE_VERSION=7.17.14
IMAGE_NAME=docker.elastic.co/beats/filebeat

# Elasticsearch连接信息
ES_HOST="**********"  # Elasticsearch主机地址
ES_PORT="9200"        # Elasticsearch端口
ES_USERNAME="elastic"
ES_PASSWORD="Elastic20250417@#"
KIBANA_HOST="**********"
KIBANA_PORT="5601"

# 检查并安装jq命令，用于解析JSON
if ! command -v jq &> /dev/null; then
    echo "jq命令未安装，正在安装..."
    # 根据系统类型安装jq
    if command -v apt-get &> /dev/null; then
        sudo apt-get update && sudo apt-get install -y jq
    elif command -v yum &> /dev/null; then
        sudo yum install -y jq
    else
        echo "警告: 无法安装jq，请手动安装。某些功能可能无法正常工作。"
    fi
fi

# 创建必要目录结构
echo "创建必要目录结构: mkdir -p /data/filebeat/config /data/filebeat/data /data/filebeat/logs"
mkdir -p /data/filebeat/config /data/filebeat/data /data/filebeat/logs

AI_DOC_POC_LOG_DIR="/data/hegao/ai-doc-poc/logs"  
AI_COMPOSE_POC_LOG_DIR="/data/ai-writer/article-auto-compose-server/logs"
AI_WRITER_NOSTREAM_PYTHON_LOG_DIR="/data/ai-writer/ai-writer-nostream-python/logs"
AI_WRITER_STREAM_PYTHON_LOG_DIR="/data/ai-writer/ai-writer-stream-python/logs"
AI_HEGAO_PYTHON_LOG_DIR="/data/hegao/ai-hegao-python/logs"

# 先停止并删除现有的filebeat容器
echo "停止并删除现有的filebeat容器"
docker stop filebeat 2>/dev/null || true
docker rm -f filebeat 2>/dev/null || true

# 检查日志目录和文件权限
echo "检查日志目录和文件权限："
for logdir in "$AI_DOC_POC_LOG_DIR" "$AI_COMPOSE_POC_LOG_DIR" "$AI_WRITER_NOSTREAM_PYTHON_LOG_DIR" "$AI_WRITER_STREAM_PYTHON_LOG_DIR" "$AI_HEGAO_PYTHON_LOG_DIR"; do
  if [ -d "$logdir" ]; then
    echo "目录 $logdir 存在"
    ls -la $logdir/
    # 确保filebeat可以读取日志文件
    chmod -R o+r $logdir/
    echo "已修改权限，确保日志文件可读"
  else
    echo "警告: 目录 $logdir 不存在，创建目录"
    mkdir -p $logdir
    chmod -R 755 $logdir
    echo "创建测试日志文件用于验证Filebeat配置"
    echo "2024-05-20 12:00:00 测试日志内容" > $logdir/test.log
    chmod 644 $logdir/*.log
  fi
done

echo "清理之前的Filebeat容器和数据"
rm -rf /data/filebeat/data/*
rm -rf /data/filebeat/logs/*

# 创建Filebeat配置文件 - 使用7.x兼容的配置
cat > /data/filebeat/config/filebeat.yml <<EOF
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /data/ai-doc-poc/logs/*.log
    - /data/ai-doc-poc/logs/*.log.*
    - /data/ai-doc-poc/logs/ai-doc/*.log
    - /data/ai-doc-poc/logs/ai-doc/*.log.*
  fields:
    type: ai-doc-poc
  multiline:
    pattern: '^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/article-auto-compose-server/logs/*.log
    - /data/article-auto-compose-server/logs/*.log.*
  fields:
    type: ai-compose-poc
  multiline:
    pattern: '^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/ai-writer-nostream-python/logs/*.log
    - /data/ai-writer-nostream-python/logs/*.log.*
  fields:
    type: ai-writer-nostream-python
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/ai-writer-stream-python/logs/*.log
    - /data/ai-writer-stream-python/logs/*.log.*
  fields:
    type: ai-writer-stream-python
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/ai-hegao-python/logs/*.log
    - /data/ai-hegao-python/logs/*.log.*
  fields:
    type: ai-hegao-python
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 2m
  scan_frequency: 5s
  harvester_limit: 5

# 使用Elasticsearch输出
output.elasticsearch:
  hosts: ["http://${ES_HOST}:${ES_PORT}"]
  username: "${ES_USERNAME}"
  password: "${ES_PASSWORD}"
  indices:
    - index: "ai-doc-poc-%{+yyyy}"
      when.contains:
        fields.type: "ai-doc-poc"
    - index: "ai-compose-poc-%{+yyyy}"
      when.contains:
        fields.type: "ai-compose-poc"
    - index: "ai-writer-nostream-python-%{+yyyy}"
      when.contains:
        fields.type: "ai-writer-nostream-python"
    - index: "ai-writer-stream-python-%{+yyyy}"
      when.contains:
        fields.type: "ai-writer-stream-python"
    - index: "ai-hegao-python-%{+yyyy}"
      when.contains:
        fields.type: "ai-hegao-python"
  # 增加重试和错误处理
  bulk_max_size: 50
  worker: 1
  retry:
    max_retries: 3
    backoff:
      init: 1s
      max: 60s
      factor: 2.0

# 禁用索引模板
setup.template.enabled: false

# 禁用自动索引生命周期管理
setup.ilm.enabled: false

# Kibana配置
setup.kibana.host: "http://${KIBANA_HOST}:${KIBANA_PORT}"

# 日志配置
logging.level: debug
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
EOF
echo "Filebeat配置文件创建完成: /data/filebeat/config/filebeat.yml"

# 检查ES连接
echo "检查ES连接..."
if curl -s -m 5 -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}" | grep -q "You Know"; then
  echo "✅ 成功连接到Elasticsearch"
else
  echo "⚠️ 无法连接到Elasticsearch，请检查连接设置和网络。继续部署..."
fi



# 启动Filebeat容器
echo "启动Filebeat容器"
docker run -d \
  --name=filebeat \
  --user=root \
  -e "ES_HOST=${ES_HOST}" \
  -e "ES_PORT=${ES_PORT}" \
  -e "ES_USERNAME=${ES_USERNAME}" \
  -e "ES_PASSWORD=${ES_PASSWORD}" \
  -e "KIBANA_HOST=${KIBANA_HOST}" \
  -e "KIBANA_PORT=${KIBANA_PORT}" \
  --volume="/data/filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro" \
  --volume="/data/filebeat/data:/usr/share/filebeat/data:rw" \
  --volume="/data/filebeat/logs:/var/log/filebeat:rw" \
  --volume="$AI_DOC_POC_LOG_DIR:/data/ai-doc-poc/logs:ro" \
  --volume="$AI_COMPOSE_POC_LOG_DIR:/data/article-auto-compose-server/logs:ro" \
  --volume="$AI_WRITER_NOSTREAM_PYTHON_LOG_DIR:/data/ai-writer-nostream-python/logs:ro" \
  --volume="$AI_WRITER_STREAM_PYTHON_LOG_DIR:/data/ai-writer-stream-python/logs:ro" \
  --volume="$AI_HEGAO_PYTHON_LOG_DIR:/data/ai-hegao-python/logs:ro" \
  --net=host \
  $IMAGE_NAME:$IMAGE_VERSION \
  filebeat -e --strict.perms=false

# 检查容器是否启动成功
echo "检查容器是否启动成功"
if docker ps | grep -q filebeat; then
  echo "Filebeat容器启动成功"
else
  echo "Filebeat容器启动失败"
  docker logs filebeat
  exit 1
fi

# 查看容器网络设置
echo "查看容器网络设置..."
docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' filebeat
docker inspect -f '{{.HostConfig.NetworkMode}}' filebeat

# 等待Filebeat启动并处理日志
echo "等待10秒，让Filebeat有足够时间处理日志..."
sleep 10

# 查看filebeat日志
echo "-----------------------------------查看Filebeat中ai-hegao-python日志-----------------------------------"
docker logs filebeat --tail 5  | grep -E 'ai-hegao-python'

# 查看filebeat日志
echo "-----------------------------------查看Filebeat中ai-writer-stream-python日志-----------------------------------"
docker logs filebeat --tail 5  | grep -E 'ai-writer-stream-python' 

# 查看filebeat日志
echo "-----------------------------------查看Filebeat中ai-writer-nostream-python日志-----------------------------------"
docker logs filebeat --tail 5  | grep -E 'ai-writer-nostream-python' 

# 查看filebeat日志
echo "-----------------------------------查看Filebeat中ai-compose-poc日志-----------------------------------"
docker logs filebeat --tail 5  | grep -E 'ai-compose-poc'

# 查看filebeat日志
echo "-----------------------------------查看Filebeat中ai-doc-poc日志-----------------------------------"
docker logs filebeat --tail 5  | grep -E 'ai-doc-poc'  

# 查询当前年份
curr_year=$(date +"%Y")

# 检查索引是否创建成功
echo "-----------------------------------查看所有索引-----------------------------------"
curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://${ES_HOST}:${ES_PORT}/_cat/indices?v"

# 检查索引是否创建
echo "##############################检查ai-doc-poc ES索引:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cat/indices?v" | grep -E 'ai-doc-poc'

echo "##############################检查ai-compose-poc ES索引:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cat/indices?v" | grep -E 'ai-compose-poc'

echo "##############################检查ai-writer-nostream-python ES索引:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cat/indices?v" | grep -E 'ai-writer-nostream-python'

echo "##############################检查ai-writer-stream-python ES索引:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cat/indices?v" | grep -E 'ai-writer-stream-python'

echo "##############################检查ai-hegao-python ES索引:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cat/indices?v" | grep -E 'ai-hegao-python'

# 查询ai-doc-poc数据
echo "##############################查询ai-doc-poc数据:######################################"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cat/indices?v" | grep -E 'ai-doc-poc' | awk '{print $3}' | while read index; do
    curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/${index}/_search?q=*&sort=@timestamp:desc&size=1" | grep -E 'ai-doc-poc'
done  

# 查询ai-compose-poc数据
echo "-----------------------------------查询ai-compose-poc数据:-----------------------------------"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cat/indices?v" | grep -E 'ai-compose-poc' | awk '{print $3}' | while read index; do
    curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/${index}/_search?q=*&sort=@timestamp:desc&size=1" | grep -E 'ai-compose-poc'
done

# 查询ai-writer-nostream-python数据
echo "-----------------------------------查询ai-writer-nostream-python数据:-----------------------------------"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cat/indices?v" | grep -E 'ai-writer-nostream-python' | awk '{print $3}' | while read index; do
    curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/${index}/_search?q=*&sort=@timestamp:desc&size=1" | grep -E 'ai-writer-nostream-python'
done

# 查询ai-writer-stream-python数据
echo "-----------------------------------查询ai-writer-stream-python数据:-----------------------------------"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cat/indices?v" | grep -E 'ai-writer-stream-python' | awk '{print $3}' | while read index; do
      curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/${index}/_search?q=*&sort=@timestamp:desc&size=1" | grep -E 'ai-writer-stream-python'
done

# 查询ai-hegao-python数据
echo "-----------------------------------查询ai-hegao-python数据:-----------------------------------"
curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/_cat/indices?v" | grep -E 'ai-hegao-python' | awk '{print $3}' | while read index; do
      curl -s -u "${ES_USERNAME}:${ES_PASSWORD}" "http://${ES_HOST}:${ES_PORT}/${index}/_search?q=*&sort=@timestamp:desc&size=1" | grep -E 'ai-hegao-python'
done  

echo "=== 排错信息 ==="
echo "1. 如果没有看到索引，请检查Filebeat日志: docker logs filebeat"
echo "2. 确认文件挂载是否正确: docker exec filebeat ls -la /data/ai-hegao-python/logs/"
echo "3. 确认日志格式是否匹配multiline配置"
echo "4. 测试ES连接: curl -u ${ES_USERNAME}:${ES_PASSWORD} http://${ES_HOST}:${ES_PORT}"
echo "5. 重启filebeat检查是否生效: docker restart filebeat"
echo "6. 检查filebeat配置文件: docker exec filebeat cat /usr/share/filebeat/filebeat.yml"
echo ""
echo "Filebeat部署完成，将发送日志到 ${ES_HOST}:${ES_PORT}"
