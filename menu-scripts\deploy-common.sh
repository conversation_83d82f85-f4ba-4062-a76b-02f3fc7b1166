#!/bin/bash
# 调用方传入APP_NAME
# tar包的目录结构
# ├── docker-compose.yml
# ├── images
# │   └── image.tar
# ├── app.jar
# ├── config
# │   └── config.json
# │   └── application.properties
# │   └── application-prod.yml
# │   └── logback.xml
# │   └── ...
# ├── logs
# ├── files
# │   └── ...
# ├── restart.sh
# ├── test.sh
set -e
function show_usage {
    echo "用法: $0 <应用名称> <压缩包文件名>"
    echo "选项:"
    echo "  -a, --app        应用名称"
    echo "  -t, --tar        压缩包文件名"
    echo "  -d, --dir        应用基础目录"
    echo "  -h, --help       显示帮助信息"
    exit 1
}
APP_NAME=""
APP_PARENT_DIR=""
TAR_FILE_NAME=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -a|--app)
            APP_NAME="$2"
            shift 2
            ;;
        -t|--tar)
            TAR_FILE_NAME="$2"
            shift 2
            ;;
        -d|--dir)
            APP_PARENT_DIR="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            ;;
    esac
done
# 获取当前脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "本脚本依赖命令: docker, docker-compose, tar, tree, yq"
echo "当前脚本所在目录: $SCRIPT_DIR"
echo "应用名称: $APP_NAME"
echo "应用基础目录: $APP_PARENT_DIR"
echo "压缩包文件名: $TAR_FILE_NAME"
echo "Docker Compose文件: $DOCKER_COMPOSE_FILE"
echo "当前用户: $(whoami)"
echo "当前用户组: $(groups)"
echo "当前用户主目录: $HOME"
echo "当前用户主目录权限: $(ls -ld $HOME)"
echo "当前用户主目录所属组: $(ls -ld $HOME | awk '{print $4}')"
echo "当前用户主目录所属组权限: $(ls -ld $HOME | awk '{print $5}')"
echo "当前用户主目录所属组用户: $(ls -ld $HOME | awk '{print $3}')"
echo "当前用户主目录所属组用户权限: $(ls -ld $HOME | awk '{print $6}')"
echo "当前用户主目录所属组用户组: $(ls -ld $HOME | awk '{print $7}')"

# 判断必要的命令是否存在
echo "判断必要的命令是否存在"
if ! command -v docker &> /dev/null; then
    echo "docker命令不存在"
    if ! sudo yum install -y docker; then
        echo "安装docker失败"
        exit 1
    fi
else
    echo "docker命令存在,检测是否有权限"
    if ! sudo docker ps; then
        echo "docker命令存在,但无权限"
        exit 1
    fi
    # 检测docker是否启动
    if ! sudo docker ps; then
        echo "docker未启动"
        sudo systemctl start docker
    fi
fi
if ! command -v docker-compose &> /dev/null; then
    echo "docker-compose命令不存在"
    if ! sudo yum install -y docker-compose; then
        echo "安装docker-compose失败"
        exit 1
    fi
fi  
if ! command -v tar &> /dev/null; then
    echo "tar命令不存在"
    if ! sudo yum install -y tar; then
        echo "安装tar失败"
        exit 1
    fi
fi
if ! command -v tree &> /dev/null; then
    echo "tree命令不存在：yum install -y tree"
    if ! sudo yum install -y tree; then
        echo "安装tree失败"
        exit 1
    fi
fi
# 判断yq命令是否存在
if ! command -v yq &> /dev/null; then
    echo "yq命令不存在：yum install -y yq"
    if ! sudo yum install -y yq; then
        echo "安装yq失败"
        exit 1
    fi
fi
APP_BASE_DIR="$APP_PARENT_DIR/$APP_NAME"
# 先判断应用基础目录是否存在
if [ ! -d "$APP_BASE_DIR" ]; then
    echo "应用基础目录$APP_BASE_DIR不存在,创建应用基础目录"
    mkdir -p $APP_BASE_DIR
else
    echo "应用基础目录$APP_BASE_DIR存在,清空应用基础目录"
    rm -rf $APP_BASE_DIR/*
fi
echo "应用基础目录: $APP_BASE_DIR"
tree $APP_BASE_DIR
# 将tar文件解压到应用基础目录#
tar -xzvf $TAR_FILE_NAME -C $APP_BASE_DIR
echo "解压后的应用基础目录: $APP_BASE_DIR"
tree $APP_BASE_DIR
echo "设置应用基础目录权限: chmod -R 755 $APP_BASE_DIR"
chmod -R 755 $APP_BASE_DIR
# 在$APP_BASE_DIR中查找docker-compose.yml文件
DOCKER_COMPOSE_FILE=$(find $APP_BASE_DIR -name "docker-compose.yml")
# 判断docker-compose.yml文件是否存在
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
    echo "docker-compose.yml文件$DOCKER_COMPOSE_FILE不存在"
    exit 1
fi
echo "docker-compose.yml文件: $DOCKER_COMPOSE_FILE"
# 读取docker-compose.yml文件中的services.$APP_NAME.image
IMAGE_NAME=$(yq -r ".services.$APP_NAME.image" $DOCKER_COMPOSE_FILE)
echo "docker-compose.yml文件中的services.$APP_NAME.image: $IMAGE_NAME"
if [ -z "$IMAGE_NAME" ]; then
    echo "docker-compose.yml文件$DOCKER_COMPOSE_FILE中没有找到services.$APP_NAME.image"
    exit 1
fi
# 询问 用户是否查看docker-compose.yml的内容
echo "是否查看docker-compose.yml的内容"
read -p "请输入y或n: " VIEW_DOCKER_COMPOSE_FILE
if [ "$VIEW_DOCKER_COMPOSE_FILE" = "y" ]; then
    echo "docker-compose.yml文件内容: "
    cat $DOCKER_COMPOSE_FILE
fi
# 检查jiutianwenshi-network是否存在
if ! docker network ls | grep jiutianwenshi-network; then
    echo "jiutianwenshi-network不存在,创建jiutianwenshi-network"
    docker network create jiutianwenshi-network
fi
# 检查volumes中宿主机目录是否存在
echo "检查volumes中宿主机目录是否存在"
yq -r ".services.$APP_NAME.volumes[]" $DOCKER_COMPOSE_FILE | while read -r volume_config; do
    # 跳过空行
    if [ -z "$volume_config" ]; then
        continue
    fi
    
    echo "处理卷配置: $volume_config"
    
    # 使用:分隔源和目标
    SOURCE=$(echo "$volume_config" | cut -d':' -f1)
    
    # 替换环境变量
    # 例如将 ${BASE_DIR:-/data/hegao}/ai-doc-poc/data 中的 ${BASE_DIR:-/data/hegao} 替换为实际值
    if [[ "$SOURCE" == *'${'* ]]; then
        # 提取变量名和默认值
        VAR_NAME=$(echo "$SOURCE" | sed -n 's/.*\${([^:-]*)[^}]*}.*/\1/p')
        DEFAULT_VALUE=$(echo "$SOURCE" | sed -n 's/.*:-\([^}]*\)}.*/\1/p')
        
        # 如果环境变量已设置，使用它的值；否则使用默认值
        if [ -n "${!VAR_NAME}" ]; then
            ACTUAL_VALUE="${!VAR_NAME}"
        else
            ACTUAL_VALUE="$DEFAULT_VALUE"
        fi
        
        # 替换变量
        SOURCE=$(echo "$SOURCE" | sed "s|\${$VAR_NAME:-[^}]*}|$ACTUAL_VALUE|g")
    fi
    
    # 判断是命名卷还是主机路径
    if [[ "$SOURCE" == *"/"* || "$SOURCE" == "."* || "$SOURCE" == "/"* ]]; then
        echo "发现主机路径: $SOURCE"
        
        # 确保主机路径存在
        if [ ! -d "$SOURCE" ]; then
            echo "创建主机目录: $SOURCE"
            mkdir -p "$SOURCE"
            
            # 设置适当的权限
            chmod 755 "$SOURCE"
        else
            echo "主机目录已存在: $SOURCE"
        fi
    else
        echo "发现命名卷: $SOURCE"
        
        # 检查卷是否存在
        if ! docker volume inspect "$SOURCE" &> /dev/null; then
            echo "创建Docker卷: $SOURCE"
            docker volume create "$SOURCE"
        else
            echo "Docker卷已存在: $SOURCE"
        fi
    fi
done
# 判断镜像是否存在，如果不存在，则从$APP_BASE_DIR的image中load新的镜像
if ! docker images | grep $IMAGE_NAME; then
    echo "镜像$IMAGE_NAME不存在,从$APP_BASE_DIR的images中load新的镜像"
    # 判断$APP_BASE_DIR的images目录是否存在
    if [ ! -d "$APP_BASE_DIR/images" ]; then
        echo "images目录$APP_BASE_DIR/images不存在"
        exit 1
    else
        ls $APP_BASE_DIR/images
    fi
    # 判断image.tar文件是否存在
    if [ ! -f "$APP_BASE_DIR/images/image.tar" ]; then
        echo "image.tar文件$APP_BASE_DIR/images/image.tar不存在"
        exit 1
    else
        # load镜像，并取名为IMAGE_NAME
        echo "load镜像，并取名为IMAGE_NAME：docker load -i $APP_BASE_DIR/images/image.tar  $IMAGE_NAME"
        docker load -i $APP_BASE_DIR/images/image.tar  $IMAGE_NAME
    fi
    
fi

# 删除之前的容器,如果存在
echo "删除之前的容器,如果存在"
if docker ps -a | grep $APP_NAME; then
    echo "删除之前的容器: docker rm -f $APP_NAME"
    docker rm -f $APP_NAME
fi

# 启动容器
echo "启动容器: docker-compose -f $DOCKER_COMPOSE_FILE up -d"
BASE_DIR=$APP_BASE_DIR docker-compose -f $DOCKER_COMPOSE_FILE up -d

echo "等待10秒"
sleep 10
# 检查容器是否启动成功
if ! docker ps | grep $APP_NAME; then
    echo "容器启动失败"
    exit 1
fi
# 查看启动日志
echo "查看启动日志: docker logs -f $APP_NAME"
docker logs  $APP_NAME

# 检查是否存在init.sh脚本
if [ -f "$APP_BASE_DIR/init.sh" ]; then
    echo "存在init.sh脚本"
    $APP_BASE_DIR/init.sh
else
    echo "不存在init.sh脚本"
fi

# 业务测试
# 是否存在测试脚本
if [ ! -f "$APP_BASE_DIR/test.sh" -a ! -f "$APP_BASE_DIR/test_curl.sh" ]; then
    echo "测试脚本不存在"
    exit 1
else
    echo "测试脚本存在"
    if [ -f "$APP_BASE_DIR/test.sh" ]; then
        echo "执行测试脚本: $APP_BASE_DIR/test.sh"
        $APP_BASE_DIR/test.sh
    else
        echo "执行测试脚本: $APP_BASE_DIR/test_curl.sh"
        $APP_BASE_DIR/test_curl.sh
    fi
fi
