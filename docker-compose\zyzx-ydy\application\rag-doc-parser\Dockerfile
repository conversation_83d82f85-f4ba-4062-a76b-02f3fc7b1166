FROM m.daocloud.io/docker.io/continuumio/miniconda3:latest

ARG APP_NAME_IN_CONTAINER
ARG APP_NAME_OUTSIDE

WORKDIR /app
ENV CONDA_PKGS_DIRS=/opt/conda/pkgs
ENV CONDA_ENV=embedding_service

# 安装网络工具和依赖
RUN apt-get update && \
    apt-get install -y curl telnet vim tree && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 配置 conda 使用清华镜像源
RUN conda config --add channels defaults && \
    conda config --add channels conda-forge && \
    conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/ && \
    conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/ && \
    conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/ && \
    conda config --add channels https://repo.anaconda.com/pkgs/main && \
    conda config --add channels https://repo.anaconda.com/pkgs/r && \
    conda config --set show_channel_urls yes

# 复制环境文件
COPY environment.yml .

# 创建并激活 conda 环境
RUN conda env create -f environment.yml && \
    conda clean -afy

# 设置环境变量使 conda 环境生效
ENV PATH /opt/conda/envs/${CONDA_ENV}/bin:$PATH
SHELL ["/bin/bash", "-c"]

# 下载 NLTK 数据
RUN python -m nltk.downloader -d /usr/local/share/nltk_data punkt && \
    python -m nltk.downloader -d /usr/local/share/nltk_data stopwords && \
    python -m nltk.downloader -d /usr/local/share/nltk_data wordnet && \
    python -m nltk.downloader -d /usr/local/share/nltk_data averaged_perceptron_tagger && \
    python -m nltk.downloader -d /usr/local/share/nltk_data maxent_ne_chunker && \
    python -m nltk.downloader -d /usr/local/share/nltk_data words

# 设置 NLTK_DATA 环境变量
ENV NLTK_DATA=/usr/local/share/nltk_data

# 复制项目文件
COPY ${APP_NAME_OUTSIDE} /app/${APP_NAME_IN_CONTAINER}

# 复制并修正shell脚本权限
COPY start.sh /app/shells/start.sh
RUN chmod +x /app/shells/start.sh

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8000

# 设置启动命令
CMD ["bash", "-c", "source activate ${CONDA_ENV} && bash /app/shells/start.sh"] 