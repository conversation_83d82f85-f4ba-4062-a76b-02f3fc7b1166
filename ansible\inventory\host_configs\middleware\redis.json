{"middleware_name": "redis", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "image_name": "m.daocloud.io/docker.io/redis:7.4.1", "port": "6379", "host_port": "6379", "container_port": "6379", "password": "admin123", "host_data_dir": "{{base_dir}}/redis/data", "host_config_dir": "{{base_dir}}/redis/conf", "container_data_dir": "/data", "container_config_dir": "/etc/redis", "restart_script": "{{base_dir}}/redis/restart.sh", "test_script": "{{base_dir}}/redis/test_curl.sh", "start_command": "redis-server /etc/redis/redis.conf", "config": {"bind": "0.0.0.0", "protected-mode": "no", "requirepass": "admin123", "appendonly": "yes"}, "test_commands": ["docker exec -it redis redis-cli -a admin123 -e 'set test \"hello\"'", "docker exec -it redis redis-cli -a admin123 -e 'get test'"]}