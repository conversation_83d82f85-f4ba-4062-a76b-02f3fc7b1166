**阶段一：本地准备与打包 - 详细设计**

**1. 目标**

*   **采集与转换**: 从现有源环境（依据 `九天文思-中铝部署方案.md` 和实际部署情况）安全、完整地获取部署所需的所有资源（镜像、应用文件、配置、现有脚本等）。
*   **标准化**: 对获取的资源进行标准化处理，包括调整为目标目录结构（如 `config/`, `build/`, `data/`）和适配配置文件以符合目标环境要求。
*   **生成**: 根据目标 `规划表.csv` 和标准化模板，生成新的、符合自动化部署框架的 Shell 部署脚本 (`deploy_*.sh`) 和主机专属菜单 (`deploy_menu.sh`)。
*   **打包**: 将处理后的标准化资源（应用文件、适配后的配置、生成的脚本和菜单、可选的镜像 `.tar`）打包成标准化的 `.tar.gz` 压缩包，作为后续自动化部署流程的输入，并生成校验和。

**2. 输入要求**

*   **源系统访问权限**: 具备通过SSH/SCP等方式访问**源系统**（依据 `九天文思-中铝部署方案.md` 确定，如中铝环境服务器）的权限，以便下载文件和导出Docker镜像。需要提供相应的认证凭据。
*   **源部署信息 (`九天文思-中铝部署方案.md`)**: 提供现有部署的详细信息，包括各组件的源IP、宿主机目录（数据、日志、配置、启动脚本）、容器内路径、当前镜像名称等。**此文档是资源采集的主要依据**。
*   **（可选）现有部署脚本 (`deploy-zhonglv/shells/`)**: 参考现有实际使用的部署或启动脚本（如 `restart.sh`, `deploy_app.sh`），以获取关键的启动命令、环境变量或特定逻辑，用于生成新的标准化部署脚本。
*   **目标规划表 (`规划表.csv`)**: 定义**目标环境**的详细配置，包括目标主机映射、组件分配、基础目录、特定配置参数（用于替换模板变量）、目标用户名/密码、部署顺序、依赖关系、**以及镜像来源策略 (`image_source_strategy`)** 等。`image_source_strategy` 可选值为 `original` (默认, 直接使用规划的镜像) 或 `commit_container` (从源环境运行中的容器提交新镜像)。**此表格是配置适配和脚本/菜单生成的主要依据**。
*   **资源清单**: （可从部署方案和规划表推断或单独提供）明确要下载/导出的具体资源列表。
*   **本地准备环境**: 一个具备足够存储空间、安装了必要工具（如 SSH客户端, SCP, Docker客户端, Python 3.8+, `tar`, `gzip`, `md5sum`/`sha256sum`, Jinja2）的工作环境。

**2.1 下载规划表设计 (`download_planning.json`)**

为精确指导资源获取子流程（4.2），建议生成或提供一个独立的"下载规划表"，详细列出需要从源系统采集的每一个资源。

*   **目标**: 提供一个清晰、机器可读的清单，供下载脚本 (`downloader.py`) 使用，以自动化地从源环境采集所有必需的原始资源。
*   **格式**: JSON 格式。
*   **文件名**: `download_planning.json` (建议)
*   **生成**: 此表可在子流程1中，根据解析后的目标规划表和源部署方案自动生成，或手动维护。
*   **字段定义**:

    | 字段名                | 数据类型 | 说明                                                                                                                               | 示例                                               |
    | :------------------ | :------- | :--------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------- |
    | `app_name`          | string   | 应用/组件名称                                                                                                                      | `rbac`, `redis`, `web_assistant`                   |
    | `description`       | string   | 应用/组件的描述信息                                                                                                                | `智能问答权限服务Java端`, `Redis缓存服务`            |
    | `app_type`          | string   | 应用类型，如 `java`, `python`, `middleware`, `frontend`, `embedding_model`                                                         | `java`, `python`, `middleware`                     |
    | `module_name`       | string   | 所属模块名称                                                                                                                       | `znwd`, `middleware`, `frontend`, `embedding`      |
    | `ip_address`        | string   | 源系统上该应用所在主机的IP地址                                                                                                      | `**********`, `********`                          |
    | `app_base_dir`      | string   | 源系统上应用的基础目录                                                                                                             | `/data/znwd/rbac`, `/data/middleware/redis`        |
    | `config_dir`        | string   | 配置文件目录                                                                                                                       | `config`                                           |
    | `log_dir`           | string   | 日志文件目录                                                                                                                       | `logs`                                             |
    | `docker_image`      | string   | Docker镜像名称和标签                                                                                                               | `znwd/rbac:1.0-SNAPSHOT`, `redis:7.4.1`            |
    | `container_name`    | string   | 容器名称                                                                                                                           | `rbac`, `redis`                                    |
    | `source_package_path` | string | 源系统上的应用包路径或镜像路径                                                                                                     | `/data/znwd/rbac/app.jar`, `redis:7.4.1`           |
    | `config_dependencies` | array  | 配置依赖项列表，包含配置文件的依赖关系                                                                                             | 见下方示例                                         |
    | `static_resources`  | array   | 静态资源列表（仅前端服务需要）                                                                                                      | 见下方示例                                         |

*   **`config_dependencies` 字段结构**:

    | 字段名                | 数据类型 | 说明                                                                                                                               | 示例                                               |
    | :------------------ | :------- | :--------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------- |
    | `config_file`       | string   | 配置文件路径                                                                                                                       | `config/application-prod.yml`                      |
    | `key`              | string   | 配置项的键                                                                                                                         | `spring.datasource.url`                           |
    | `service_ref`      | string   | 依赖的服务引用（可选）                                                                                                             | `middleware/postgresql`                           |
    | `value_type`       | string   | 值类型，如 `url`, `string`, `int`, `jdbc_url`, `ip`                                                                               | `url`, `string`, `int`                           |
    | `current_value`    | string   | 当前值                                                                                                                             | `*************************************`           |
    | `template_value`   | string   | 模板值，用于后续替换                                                                                                               | `jdbc:postgresql://{{postgresql.ip}}:{{postgresql.port}}/aidb` |

*   **`static_resources` 字段结构**:

    | 字段名                | 数据类型 | 说明                                                                                                                               | 示例                                               |
    | :------------------ | :------- | :--------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------- |
    | `name`             | string   | 静态资源名称                                                                                                                       | `web_assistant`                                   |
    | `path`             | string   | 静态资源路径                                                                                                                       | `/data/nginx/html/web_assistant`                  |

*   **示例内容**:

    ```json
    [
      {
        "app_name": "rbac",
        "description": "智能问答权限服务Java端",
        "app_type": "java",
        "module_name": "znwd",
        "ip_address": "**********",
        "app_base_dir": "/data/znwd/rbac",
        "config_dir": "config",
        "log_dir": "logs",
        "docker_image": "znwd/rbac:1.0-SNAPSHOT",
        "container_name": "rbac",
        "config_dependencies": [
          {
            "config_file": "config/application-prod.yml",
            "key": "spring.datasource.url",
            "service_ref": "middleware/postgresql",
            "value_type": "jdbc_url",
            "current_value": "*************************************",
            "template_value": "jdbc:postgresql://{{postgresql.ip}}:{{postgresql.port}}/aidb"
          }
        ]
      },
      {
        "app_name": "nginx",
        "description": "Nginx前端服务",
        "app_type": "frontend",
        "module_name": "frontend",
        "ip_address": "**********",
        "app_base_dir": "/data/nginx",
        "config_dir": "config",
        "log_dir": "logs",
        "docker_image": "nginx:1.27.4",
        "container_name": "nginx",
        "static_resources": [
          {
            "name": "web_assistant",
            "path": "/data/nginx/html/web_assistant"
          }
        ]
      }
    ]
    ```

**3. 输出交付物（作为后续阶段的输入）**

*   **标准化的部署包（.tar.gz）**：为每个目标组件（或按需组合）生成的压缩包，包含其运行所需的标准化文件。
    *   **每个应用目录结构如下：**

        ```
        <app_name>/
        ├── app.jar                # Java主程序，仅保留此jar，其余jar删除（如有）
        ├── config/                # 配置文件目录
        ├── files/                 # 其他文件目录（如有）
        ├── image/                 # 该应用的docker镜像（.tar）
        │   └── <app_name>.tar
        ├── logs/                  # 日志目录，仅保留空目录
        ├── restart.sh             # 重启脚本（如有）
        ├── shells/                # 容器内启动脚本
        │   └── start.sh
        ├── data/                  # 数据目录（如有）
        ├── test.py                # Python主程序（如有）
        ├── test_curl.sh           # 测试脚本（如有）
        └── html/                  # 前端静态资源目录（仅前端应用如nginx有）
        ```
    *   **关键约定：**
        - 每个应用的镜像文件放在其自身的 image/ 目录下。
        - logs/ 目录必须为空。
        - Java 应用只保留 app.jar，删除其他 jar 文件。
        - 配置文件参数需根据 download_planning.json 替换。
        - 前端静态资源目录为 html/。

*   **详细子流程设计**

    1. **复制整个工程目录**，保持原有结构。
    2. **为每个应用新建 image/ 目录**，将该应用的 docker 镜像（docker save 导出）放入其中，命名为 <app_name>.tar。
    3. **根据 download_planning.json 替换配置文件参数**，如有模板变量，需渲染为目标值。
    4. **删除所有 logs 目录下的日志文件，仅保留空 logs 目录。**
    5. **Java 应用中，仅保留 app.jar，删除其他 jar 文件。**
    6. **再次确认 logs 目录下无日志文件。**
    7. **前端应用（如 nginx）静态资源目录为 html/，将 nginx/html 下所有内容复制到 html/。**

*   **其他交付物：**
    - 镜像文件（已包含于各自 image/ 目录）
    - 处理后的配置文件（config/）
    - 生成的部署脚本（shells/、restart.sh等）
    - 校验和文件（.md5 或 .sha256）
    - 部署清单（Manifest）
    - 更新的规划表

**4. 详细子流程设计（全Shell脚本实现，按本机IP过滤）**

1. **环境设置、信息解析与采集计划**
    - 操作员手动或通过Shell脚本启动整个流程（如 main.sh）。
    - 解析目标规划表（如 规划表.csv）和源部署方案（如 部署方案.md），可用awk/grep/sed等Shell工具提取关键信息。
    - 生成/校验下载规划表（download_planning.json），如需自动生成可用jq、awk等工具。

2. **资源采集**
    - 读取download_planning.json，用jq等工具解析。
    - **遍历download_planning.json，仅处理ip为本机的应用，跳过其他主机的应用。**
    - **复制本机上的整个工程目录**（cp -r），保持原有结构。
    - 对于每个本机应用：
        - 导出容器镜像（docker save），存放到其image/目录，如rbac/image/rbac.tar。
        - 收集容器内启动脚本、辅助脚本到shells/。
        - 收集配置文件到config/。
        - 收集前端静态资源到html/（如nginx/html）。
    - **不再使用scp/rsync等远程访问命令。**

3. **标准化、配置适配与清理**
    - 用sed/awk/grep等工具批量替换配置文件参数（根据download_planning.json）。
    - 用rm -f删除所有logs/目录下的日志文件，仅保留空logs/目录（find . -type d -name logs -exec rm -rf {}/\* ;）。
    - Java应用只保留app.jar，用find+rm删除其他jar文件（find . -name '*.jar' ! -name 'app.jar' -delete）。
    - 前端应用将nginx/html下所有内容复制到html/目录（cp -r nginx/html/* nginx/html/）。
    - 检查每个应用的image/目录下有对应镜像文件。

4. **打包与校验**
    - 用tar czf按应用目录分别打包（如tar czf rbac.tar.gz rbac/）。
    - 用md5sum或sha256sum生成校验和文件。
    - 用shell脚本生成部署清单（Manifest），如ls *.tar.gz | xargs -I{} md5sum {} > manifest.txt。

**流程简述（Shell实现，按本机IP过滤）**

```
操作员
  ↓
main.sh（主流程，调用各子脚本）
  ↓
解析规划表/部署方案（awk/sed/grep/jq等）
  ↓
生成/校验 download_planning.json（jq/awk等）
  ↓
downloader.sh（遍历download_planning.json，仅处理本机ip应用，导出镜像、收集文件）
  ↓
standardize.sh（配置替换、日志清理、jar清理、前端静态资源整理，sed/find/rm/cp等）
  ↓
packager.sh（按应用目录打包，生成校验和、Manifest，tar/md5sum/sha256sum）
  ↓
输出交付物
```

**5. 错误处理与日志**

*   **错误处理**: 每个子流程都需要健壮的错误处理，特别是在资源获取和脚本生成阶段。
*   **日志记录**: 详细记录采集、转换、生成和打包的每一步，包括源路径、目标路径、使用的参数、遇到的错误等。

**6. 建议实现技术**

保持推荐 **Python** 作为主要的编排语言，利用其库进行文件操作、远程连接、模板渲染等。脚本可以模块化为 `preprocess.py` (主流程), `lib/parser.py`, `lib/downloader.py`, `lib/generator.py`, `lib/packager.py`。 