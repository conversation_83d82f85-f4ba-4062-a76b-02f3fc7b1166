#!/bin/bash

set -e

HARBOR_VERSION="v2.10.0"
HARBOR_HOSTNAME="**********"   # 修改为你的服务器IP或域名
HARBOR_ADMIN_PASSWORD="Harbor12345"  # Harbor admin密码
CERT_DIR="/data/cert"
HARBOR_DIR="/data/build/harbor"

echo "1. 停止 Harbor 服务..."
cd $HARBOR_DIR
if [ -f "docker-compose.yml" ]; then
  docker-compose down
fi

echo "2. 删除 Harbor 配置和证书..."
cd ..
rm -rf $HARBOR_DIR
rm -rf $CERT_DIR

echo "3. 重新生成带 SAN 的自签名证书..."
mkdir -p $CERT_DIR
cat > $CERT_DIR/san.cnf <<EOF
[req]
default_bits = 4096
prompt = no
default_md = sha256
req_extensions = req_ext
distinguished_name = dn

[dn]
CN = ${HARBOR_HOSTNAME}

[req_ext]
subjectAltName = @alt_names

[alt_names]
IP.1 = ${HARBOR_HOSTNAME}
DNS.1 = ${HARBOR_HOSTNAME}
EOF

openssl req -x509 -nodes -days 3650 -newkey rsa:4096 \
  -keyout $CERT_DIR/server.key \
  -out $CERT_DIR/server.crt \
  -config $CERT_DIR/san.cnf \
  -extensions req_ext

echo "4. 解压 Harbor 安装包..."
if [ ! -f harbor-online-installer-${HARBOR_VERSION}.tgz ]; then
  wget -c https://github.com/goharbor/harbor/releases/download/${HARBOR_VERSION}/harbor-online-installer-${HARBOR_VERSION}.tgz
fi
tar xvf harbor-online-installer-${HARBOR_VERSION}.tgz

echo "5. 生成 harbor.yml 配置..."
cd harbor
pwd
cp harbor.yml.tmpl harbor.yml
sed -i "s/^hostname:.*/hostname: ${HARBOR_HOSTNAME}/" harbor.yml
sed -i "s/^  harbor_admin_password:.*/  harbor_admin_password: ${HARBOR_ADMIN_PASSWORD}/" harbor.yml
sed -i '/^# http:/,/^#$/s/^/#/' harbor.yml
sed -i '/^# https:/,/^#$/d' harbor.yml
cat >> harbor.yml <<EOF

https:
  port: 443
  certificate: $CERT_DIR/server.crt
  private_key: $CERT_DIR/server.key
EOF

#·echo "6. 修改 docker-compose.yml 的 nginx 容器名..."
#sed -i 's/container_name: nginx/container_name: harbor-nginx/g' docker-compose.yml

echo "7. 安装 Harbor..."
./install.sh

echo "8. 安装完成！"
echo "请用浏览器访问：https://${HARBOR_HOSTNAME}"
echo "默认用户名：admin"
echo "默认密码：${HARBOR_ADMIN_PASSWORD}"
echo "如用自签名证书推送/拉取镜像，请将 $CERT_DIR/server.crt 拷贝到各客户端 /etc/docker/certs.d/${HARBOR_HOSTNAME}/ca.crt 并重启docker"