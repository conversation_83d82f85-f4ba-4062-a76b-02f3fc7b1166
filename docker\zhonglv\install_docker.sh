#!/bin/bash

# 定义变量
DOCKER_DIR="/data/docker"
DOCKER_STORAGE="/var/lib/docker"

# 安装 Docker CE
echo "正在安装 Docker CE..."
sudo yum install -y docker

# 创建目标目录并设置权限
echo "正在创建目标目录 $DOCKER_DIR 并设置权限..."
sudo mkdir -p $DOCKER_DIR
sudo chown -R root:root $DOCKER_DIR

# 如果原目录存在，则备份并创建软连接
if [ -d "$DOCKER_STORAGE" ]; then
    echo "检测到原目录 $DOCKER_STORAGE 存在，正在备份..."
    sudo mv $DOCKER_STORAGE ${DOCKER_STORAGE}.bak
fi

echo "正在创建软连接..."
sudo ln -s $DOCKER_DIR $DOCKER_STORAGE

# 启动 Docker 服务并设置开机自启
echo "正在启动 Docker 服务..."
sudo systemctl start docker
sudo systemctl enable docker

# 验证 Docker 是否安装成功
echo "验证 Docker 是否安装成功..."
docker_version=$(docker --version)
if [ $? -eq 0 ]; then
    echo "Docker 安装成功！版本信息：$docker_version"
else
    echo "Docker 安装失败，请检查错误日志。"
    exit 1
fi



# 重启 Docker 服务以应用配置
echo "正在重启 Docker 服务..."
sudo systemctl restart docker

echo "安装完成！Docker 数据目录已软连接到 $DOCKER_DIR。"