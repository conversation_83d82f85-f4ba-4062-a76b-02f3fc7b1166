#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查root权限
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请使用root权限运行此脚本${NC}"
    exit 1
fi

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker未安装，请先安装Docker${NC}"
    exit 1
fi

# 检查是否已安装QEMU
if docker info | grep -q "qemu"; then
    echo -e "${GREEN}QEMU已安装${NC}"
    exit 0
fi

echo -e "${YELLOW}开始安装QEMU支持...${NC}"

# 安装qemu-user-static
if [ -f /etc/redhat-release ]; then
    # CentOS/RHEL
    echo -e "${YELLOW}检测到CentOS/RHEL系统${NC}"
    yum install -y qemu-user-static
elif [ -f /etc/debian_version ]; then
    # Debian/Ubuntu
    echo -e "${YELLOW}检测到Debian/Ubuntu系统${NC}"
    apt-get update
    apt-get install -y qemu-user-static
else
    echo -e "${RED}不支持的系统类型，请手动安装qemu-user-static${NC}"
    exit 1
fi

# 安装Docker的QEMU支持
echo -e "${YELLOW}正在配置Docker的QEMU支持...${NC}"
docker run --rm --privileged multiarch/qemu-user-static --reset -p yes

echo -e "${GREEN}QEMU安装完成${NC}"
echo -e "${YELLOW}现在您可以运行ARM架构的Docker镜像了${NC}"
echo -e "${YELLOW}注意：使用QEMU运行ARM镜像的性能会比原生运行慢一些${NC}" 