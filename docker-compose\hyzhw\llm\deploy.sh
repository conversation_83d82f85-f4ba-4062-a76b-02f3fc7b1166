#!/bin/bash
# LLM 目录一键部署脚本
CURRENT_DIR=$(cd $(dirname $0); pwd)
echo "当前目录: $CURRENT_DIR"

# 设置权限
chmod +x $CURRENT_DIR/deepseek70b/shells/*.sh

# 设置日志目录权限
find $CURRENT_DIR -type d -name "logs" -exec chmod 777 {} \;
find $CURRENT_DIR -type d -name "init-logs" -exec chmod 777 {} \;

# 检查 docker-compose.yml 是否存在
if [ ! -f "$CURRENT_DIR/docker-compose.yml" ]; then
    echo "docker-compose.yml文件不存在"
    exit 1
fi
# 检查 docker-compose 命令
if ! command -v docker-compose &> /dev/null; then
    echo "docker-compose命令不存在，尝试安装..."
    if command -v yum &> /dev/null; then
        yum install -y docker-compose
    elif command -v apt-get &> /dev/null; then
        apt-get install -y docker-compose
    else
        echo "请手动安装 docker-compose"
        exit 1
    fi
fi

# 创建网络
if ! docker network ls | grep -q jiutianwensi-network; then
    echo "docker network create jiutianwensi-network"
    docker network create jiutianwensi-network
fi

# 停止所有容器
echo "停止所有容器"
docker-compose -f $CURRENT_DIR/docker-compose.yml down -v
docker system prune -f

# 启动服务
echo "启动 deepseek70b 和 filebeat 服务"
docker-compose -f $CURRENT_DIR/docker-compose.yml up -d

echo "所有服务已启动！"
docker-compose ps

echo "进入容器命令示例："
echo "docker exec -it deepseek70b-server bash"
echo "docker exec -it filebeat-deepseek70b bash" 