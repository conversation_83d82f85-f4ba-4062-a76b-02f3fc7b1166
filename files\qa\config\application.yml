server:
  port: 8888
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
  data:
    redis:
      password: 
      timeout: 2000
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1
      database: 2

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true

  # Actuator 配置
  actuator:
    endpoints:
      web:
        exposure:
          include: "*"  # 暴露所有端点
    endpoint:
      health:
        show-details: always  # 显示健康检查的详细信息

jasypt:
  encryptor:
    algorithm: PBEWITHHMACSHA512ANDAES_256
    password: test123456
  iv-generator-classname: org.jasypt.iv.RandomIvGenerator