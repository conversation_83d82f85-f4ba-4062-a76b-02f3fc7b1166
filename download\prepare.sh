#!/bin/bash

set -e

# 脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

log() {
  echo -e "\033[33m[PREP]\033[0m $1"
}

# 获取本机IP（仅取第一个非127的IPv4）
LOCAL_IP=$(hostname -I | awk '{print $1}')
log "本机IP: $LOCAL_IP"

# 确保download_planning.json存在
PLANNING_FILE="$SCRIPT_DIR/download_planning.json"
if [ ! -f "$PLANNING_FILE" ]; then
  log "错误: 未找到规划文件 $PLANNING_FILE"
  exit 1
fi

# 解析download_planning.json，筛选本机应用
log "筛选本机应用..."
jq -c --arg ip "$LOCAL_IP" '.[] | select(.ip_address == $ip)' "$PLANNING_FILE" > local_apps.json

# 如果没有找到匹配的应用，尝试使用127.0.0.1作为备用
if [ ! -s local_apps.json ]; then
  log "未找到IP为 $LOCAL_IP 的应用，尝试使用127.0.0.1..."
  jq -c '.[] | select(.ip_address == "127.0.0.1")' "$PLANNING_FILE" > local_apps.json
fi

# 如果仍然没有找到匹配的应用，将所有应用包含进来（开发环境）
if [ ! -s local_apps.json ]; then
  log "本地测试环境，包含所有应用..."
  jq -c '.[]' "$PLANNING_FILE" > local_apps.json
fi

# 检查生成的local_apps.json
if [ ! -s local_apps.json ]; then
  log "警告: 生成的local_apps.json为空，没有找到任何应用"
  # 为了测试，创建一个包含所有应用的local_apps.json
  echo "[" > local_apps.json
  jq -c '.[]' "$PLANNING_FILE" | while read -r line; do
    echo "$line," >> local_apps.json
  done
  # 移除最后一个逗号并添加结束括号
  sed -i '$ s/,$//' local_apps.json
  echo "]" >> local_apps.json
  log "为测试目的已创建包含所有应用的local_apps.json"
else
  # 在文件开头添加 [ 并在末尾添加 ]，使其成为有效的JSON数组
  tmp_file=$(mktemp)
  echo "[" > "$tmp_file"
  cat local_apps.json >> "$tmp_file"
  # 移除可能存在的尾部逗号
  sed -i '$ s/,$//' "$tmp_file"
  echo "]" >> "$tmp_file"
  mv "$tmp_file" local_apps.json
  log "已生成local_apps.json，包含 $(jq '. | length' local_apps.json) 个应用"
fi

# 输出找到的应用列表
log "找到以下应用:"
jq -r '.[].app_name' local_apps.json | while read -r app_name; do
  log "- $app_name"
done

log "准备阶段完成" 