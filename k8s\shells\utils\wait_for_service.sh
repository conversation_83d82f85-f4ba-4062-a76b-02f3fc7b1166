#!/bin/bash

# 等待服务启动
# 参数: $1 - 服务名称, $2 - 资源类型, $3 - 命名空间, $4 - 超时时间(秒)
# 返回: IS_RUNNING=true/false
function wait_for_service() {
    local service_name=$1
    local resource_type=$2
    local namespace=$3
    local timeout=${4:-20}  # 默认等待20秒
    
    # 如果资源类型不是带有Pod的类型，则直接返回
    if [[ "$resource_type" != "deployment" && "$resource_type" != "statefulset" && "$resource_type" != "daemonset" ]]; then
        IS_RUNNING=true
        return
    fi
    
    echo "等待服务 $service_name 启动，超时时间 $timeout 秒..."
    wait_time=0
    IS_RUNNING=false
    
    while [ $wait_time -lt $timeout ]; do
        # 检查是否有 Running 状态的 Pod
        if kubectl get pods -n $namespace | grep $service_name | grep "Running" &>/dev/null; then
            IS_RUNNING=true
            echo "服务已成功启动！"
            return
        fi
        
        # 检查是否有失败状态的 Pod
        if kubectl get pods -n $namespace | grep $service_name | grep -E 'Error|CrashLoopBackOff|ImagePullBackOff' &>/dev/null; then
            echo "服务启动失败!"
            kubectl get pods -n $namespace | grep $service_name
            return
        fi
        
        echo "等待中... ${wait_time}s/$timeout"
        sleep 2
        wait_time=$((wait_time + 2))
    done
    
    echo "服务 $service_name 在 $timeout 秒内未达到 Running 状态"
    echo "当前 Pod 状态:"
    kubectl get pods -n $namespace | grep $service_name
}

# 导出函数
export -f wait_for_service 