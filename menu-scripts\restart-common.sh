#!/bin/bash
# 获取当前脚本所在目录就是应用基础目录

APP_BASE_DIR=$(dirname $0)
# 获取应用名称
APP_NAME=$(basename $APP_BASE_DIR)
echo "本脚本依赖命令: docker, docker-compose, tar, tree, yq"
echo "当前脚本所在目录: $APP_BASE_DIR"
echo "应用名称: $APP_NAME"
echo "当前用户: $(whoami)"
echo "当前用户组: $(groups)"
echo "当前用户主目录: $HOME"
echo "当前用户主目录权限: $(ls -ld $HOME)"
echo "当前用户主目录所属组: $(ls -ld $HOME | awk '{print $4}')"
echo "当前用户主目录所属组权限: $(ls -ld $HOME | awk '{print $5}')"
echo "当前用户主目录所属组用户: $(ls -ld $HOME | awk '{print $3}')"
echo "当前用户主目录所属组用户权限: $(ls -ld $HOME | awk '{print $6}')"
echo "当前用户主目录所属组用户组: $(ls -ld $HOME | awk '{print $7}')"

# 判断必要的命令是否存在
echo "判断必要的命令是否存在"
if ! command -v docker &> /dev/null; then
    echo "docker命令不存在"
    if ! sudo yum install -y docker; then
        echo "安装docker失败"
        exit 1
    fi
else
    echo "docker命令存在,检测是否有权限"
    if ! sudo docker ps; then
        echo "docker命令存在,但无权限"
        exit 1
    fi
    # 检测docker是否启动
    if ! sudo docker ps; then
        echo "docker未启动"
        sudo systemctl start docker
    fi
fi
if ! command -v docker-compose &> /dev/null; then
    echo "docker-compose命令不存在"
    if ! sudo yum install -y docker-compose; then
        echo "安装docker-compose失败"
        exit 1
    fi
fi  
if ! command -v tree &> /dev/null; then
    echo "tree命令不存在：yum install -y tree"
    if ! sudo yum install -y tree; then
        echo "安装tree失败"
        exit 1
    fi
fi
# 判断yq命令是否存在
if ! command -v yq &> /dev/null; then
    echo "yq命令不存在：yum install -y yq"
    if ! sudo yum install -y yq; then
        echo "安装yq失败"
        exit 1
    fi
fi

tree $APP_BASE_DIR
# 将tar文件解压到应用基础目录#
echo "设置应用基础目录权限: chmod -R 755 $APP_BASE_DIR"
chmod -R 755 $APP_BASE_DIR
# 在$APP_BASE_DIR中查找docker-compose.yml文件
DOCKER_COMPOSE_FILE=$(find $APP_BASE_DIR -name "docker-compose.yml")
# 判断docker-compose.yml文件是否存在
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
    echo "docker-compose.yml文件$DOCKER_COMPOSE_FILE不存在"
    exit 1
fi
echo "docker-compose.yml文件: $DOCKER_COMPOSE_FILE"
# 获取Image名称 
IMAGE_NAME=$(yq -r ".services.${APP_NAME}.image" $DOCKER_COMPOSE_FILE)
echo "docker-compose.yml文件中的services.$APP_NAME.image: $IMAGE_NAME"
if [ -z "$IMAGE_NAME" ]; then
    echo "docker-compose.yml文件$DOCKER_COMPOSE_FILE中没有找到services.$APP_NAME.image"
    exit 1
fi

# 询问 用户是否查看docker-compose.yml的内容
echo "是否查看docker-compose.yml的内容"
read -p "请输入y或n: " VIEW_DOCKER_COMPOSE_FILE
if [ "$VIEW_DOCKER_COMPOSE_FILE" = "y" ]; then
    echo "docker-compose.yml文件内容: "
    cat $DOCKER_COMPOSE_FILE
fi
# 检查jiutianwenshi-network是否存在
if ! docker network ls | grep jiutianwenshi-network; then
    echo "jiutianwenshi-network不存在,创建jiutianwenshi-network"
    docker network create jiutianwenshi-network
fi

# 询问用户是删除容器还是重启容器
echo "是否删除容器再启动"
read -p "请输入y或n: " DELETE_CONTAINER
if [ "$DELETE_CONTAINER" = "y" ]; then
    echo "删除容器"
    docker rm -f $APP_NAME
    # 启动容器
    echo "启动容器: docker-compose -f $DOCKER_COMPOSE_FILE up -d"
    BASE_DIR=$APP_BASE_DIR docker-compose -f $DOCKER_COMPOSE_FILE up -d
else
    echo "重启容器：docker-compose -f $DOCKER_COMPOSE_FILE restart $APP_NAME"
    BASE_DIR=$APP_BASE_DIR docker-compose -f $DOCKER_COMPOSE_FILE restart $APP_NAME
fi

echo "等待10秒"
sleep 10
# 检查容器是否启动成功
if ! docker ps | grep $APP_NAME; then
    echo "容器启动失败"
    exit 1
fi
# 查看启动日志
echo "查看启动日志: docker logs -f $APP_NAME"
docker logs  $APP_NAME

# 业务测试
# 是否存在测试脚本
if [ ! -f "$APP_BASE_DIR/test.sh" -a ! -f "$APP_BASE_DIR/test_curl.sh" ]; then
    echo "测试脚本不存在"
    exit 1
else
    echo "测试脚本存在"
    if [ -f "$APP_BASE_DIR/test.sh" ]; then
        echo "执行测试脚本: $APP_BASE_DIR/test.sh"
        $APP_BASE_DIR/test.sh
    else
        echo "执行测试脚本: $APP_BASE_DIR/test_curl.sh"
        $APP_BASE_DIR/test_curl.sh
    fi
fi
