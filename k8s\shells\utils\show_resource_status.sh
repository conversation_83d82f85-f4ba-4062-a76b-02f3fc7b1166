#!/bin/bash

# 显示服务状态
# 参数: $1 - 服务名称, $2 - 资源类型, $3 - 命名空间
function show_resource_status() {
    local service_name=$1
    local resource_type=$2
    local namespace=$3
    
    echo "====== $service_name 服务当前状态 ======"
    case $resource_type in
        "deployment")
            echo "查看 Deployment 状态:"
            kubectl get deployment $service_name -n $namespace -o wide
            echo "查看相关 Pod 状态:"
            kubectl get pods -n $namespace -l app=$service_name
            ;;
        "statefulset")
            echo "查看 StatefulSet 状态:"
            kubectl get statefulset $service_name -n $namespace -o wide
            echo "查看相关 Pod 状态:"
            kubectl get pods -n $namespace -l app=$service_name
            ;;
        "daemonset")
            echo "查看 DaemonSet 状态:"
            kubectl get daemonset $service_name -n $namespace -o wide
            echo "查看相关 Pod 状态:"
            kubectl get pods -n $namespace -l app=$service_name
            ;;
        "service")
            echo "查看 Service 状态:"
            kubectl get service $service_name -n $namespace -o wide
            ;;
        "configmap")
            echo "查看 ConfigMap 信息:"
            kubectl get configmap $service_name -n $namespace -o yaml
            ;;
        "secret")
            echo "查看 Secret 信息(不显示具体内容):"
            kubectl get secret $service_name -n $namespace
            ;;
        "pvc")
            echo "查看 PersistentVolumeClaim 状态:"
            kubectl get pvc $service_name -n $namespace
            ;;
    esac
    
    # 检查资源是否有问题
    has_issues=false
    
    # 对于 StatefulSet、Deployment 和 DaemonSet，检查 READY 状态
    if [[ "$resource_type" == "deployment" || "$resource_type" == "statefulset" || "$resource_type" == "daemonset" ]]; then
        # 获取 ready 状态
        ready_status=$(kubectl get $resource_type $service_name -n $namespace -o jsonpath='{.status.readyReplicas}')
        desired_replicas=$(kubectl get $resource_type $service_name -n $namespace -o jsonpath='{.spec.replicas}')
        
        # 如果 ready_status 为空或者为 0
        if [ -z "$ready_status" ] || [ "$ready_status" -eq 0 ]; then
            has_issues=true
            echo "警告: $service_name 没有就绪的副本!"
        elif [ "$ready_status" -lt "$desired_replicas" ]; then
            has_issues=true
            echo "警告: $service_name 只有 $ready_status/$desired_replicas 个副本就绪!"
        fi
        
        # 检查是否有异常的 Pod
        if kubectl get pods -n $namespace -l app=$service_name | grep -E 'Error|CrashLoopBackOff|ImagePullBackOff|ContainerCreating|Pending' &>/dev/null; then
            has_issues=true
            echo "警告: 发现异常状态的 Pod:"
            kubectl get pods -n $namespace -l app=$service_name | grep -E 'Error|CrashLoopBackOff|ImagePullBackOff|ContainerCreating|Pending'
        fi
    fi
    
    # 如果是带有 Pod 的资源，尝试显示日志
    if [[ "$resource_type" == "deployment" || "$resource_type" == "statefulset" || "$resource_type" == "daemonset" ]]; then
        local pod_name=$(kubectl get pods -n $namespace -l app=$service_name | grep -v NAME | head -1 | awk '{print $1}')
        if [ -n "$pod_name" ]; then
            echo "是否查看 Pod 日志? (y/n): "
            read VIEW_POD_LOGS
            if [ "$VIEW_POD_LOGS" = "y" ]; then
                # 调用view_pod_logs函数查看日志
                view_pod_logs "$pod_name" "$namespace"
            fi
        fi
    fi
    
    echo "====== 状态显示完成 ======"
    
    # 如果发现问题，询问是否需要诊断
    if [ "$has_issues" = "true" ]; then
        read -p "检测到服务可能存在问题，是否需要深入诊断? (y/n): " NEED_DIAGNOSE
        if [ "$NEED_DIAGNOSE" = "y" ]; then
            diagnose_resource_issues $service_name $resource_type $namespace
        fi
    fi
}

# 导出函数
export -f show_resource_status 