#!/bin/bash

set -e

APPS_JSON="$1"
OUT_DIR="$2"
APP_DIR="$3"  # 新增：应用目录所在位置

log() {
  echo -e "\033[35m[PKG]\033[0m $1"
}

if [ ! -f "$APPS_JSON" ]; then
  log "未找到 $APPS_JSON"
  exit 1
fi

if [ -z "$OUT_DIR" ]; then
  OUT_DIR="."
fi

if [ -z "$APP_DIR" ]; then
  APP_DIR="."  # 默认当前目录
fi

# 确保输出目录存在
OUT_DIR=$(realpath "$OUT_DIR")
APP_DIR=$(realpath "$APP_DIR")
mkdir -p "$OUT_DIR"
log "输出目录: $OUT_DIR"
log "应用目录: $APP_DIR"

MANIFEST="$OUT_DIR/manifest.txt"
> "$MANIFEST"
log "清单文件: $MANIFEST"

# 输出当前目录和目标目录下文件列表，便于调试
log "当前目录: $(pwd)"
log "应用目录文件: $(ls -la $APP_DIR)"
log "输出目录文件: $(ls -la $OUT_DIR)"

jq -c '.' "$APPS_JSON" | while read -r app; do
  app_name=$(echo "$app" | jq -r '.app_name')
  if [ -d "$APP_DIR/$app_name" ]; then
    tarball="$OUT_DIR/${app_name}.tar.gz"
    log "打包 $APP_DIR/$app_name -> $tarball"
    tar -C "$APP_DIR" -czf "$tarball" "$app_name"
    md5sum "$tarball" | tee -a "$MANIFEST"
    log "已生成: $tarball ($(du -h "$tarball" | cut -f1))"
  else
    log "警告: 未找到目录 $APP_DIR/$app_name，跳过打包"
  fi
done

log "打包与校验完成，清单见 $MANIFEST"
log "输出目录内容: $(ls -la $OUT_DIR)" 