#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# ====================== API测试命令 ======================
echo -e "${BLUE}=== 文档生成API测试 ===${NC}"

# 简单请求 - 只需text字段
# 打印请求
echo "curl -X POST \
  \"http://127.0.0.1:8096/mubanwriter/v1/service\" \
  -H \"Content-Type: application/json\" \
  -d '{
  "text": "请以《关于召开项目进度评审会议的通知》为题生成一篇通知类型的公文，子类型为会议通知。主送单位为：各部门负责人。内容包括会议时间：2024年5月15日上午10:00，会议地点：公司三楼会议室，参会人员：各部门负责人及项目组成员。会议议程：1.项目进度汇报；2.问题讨论；3.下一阶段计划制定。请各相关人员准时参加。字数要求1000字左右。"
}'"

# 执行请求
curl -X POST \
  "http://127.0.0.1:8096/mubanwriter/v1/service" \
  -H "Content-Type: application/json" \
  -d '{
  "text": "请以《关于召开项目进度评审会议的通知》为题生成一篇通知类型的公文，子类型为会议通知。主送单位为：各部门负责人。内容包括会议时间：2024年5月15日上午10:00，会议地点：公司三楼会议室，参会人员：各部门负责人及项目组成员。会议议程：1.项目进度汇报；2.问题讨论；3.下一阶段计划制定。请各相关人员准时参加。字数要求1000字左右。"
}'
echo -e "${GREEN}=== 文档生成API测试结束 ===${NC}"  

# 带extension的请求
echo -e "${BLUE}=== 带extension的请求测试 ===${NC}"
# 打印请求
echo "curl -X POST \
  \"http://127.0.0.1:8096/mubanwriter/v1/service\" \
  -H \"Content-Type: application/json\" \
  -d '{
  "text": "请以《关于召开项目进度评审会议的通知》为题生成一篇通知类型的公文，子类型为会议通知。主送单位为：各部门负责人。内容包括会议时间：2024年5月15日上午10:00，会议地点：公司三楼会议室，参会人员：各部门负责人及项目组成员。会议议程：1.项目进度汇报；2.问题讨论；3.下一阶段计划制定。请各相关人员准时参加。字数要求1000字左右。",
  "extension": {
    "docInfo": {
      "sourceText": "关于召开项目进度评审会议的通知",
      "universalType": "通知",
      "subTypeName": "会议通知",
      "mainDeliveryUnit": "各部门负责人"
    },
    "fileContent": {
      "modelEssay": ["为确保项目按计划推进，解决项目过程中的问题，特召开本次项目进度评审会议。"]
    }
  }
  }'"

# 执行请求
curl -X POST \
  "http://127.0.0.1:8096/mubanwriter/v1/service" \
  -H "Content-Type: application/json" \
  -d '{
  "text": "请以《关于召开项目进度评审会议的通知》为题生成一篇通知类型的公文，子类型为会议通知。主送单位为：各部门负责人。内容包括会议时间：2024年5月15日上午10:00，会议地点：公司三楼会议室，参会人员：各部门负责人及项目组成员。会议议程：1.项目进度汇报；2.问题讨论；3.下一阶段计划制定。请各相关人员准时参加。字数要求1000字左右。",
  "extension": {
    "docInfo": {
      "sourceText": "关于召开项目进度评审会议的通知",
      "universalType": "通知",
      "subTypeName": "会议通知",
      "mainDeliveryUnit": "各部门负责人"
    },  
    "fileContent": {
      "modelEssay": ["为确保项目按计划推进，解决项目过程中的问题，特召开本次项目进度评审会议。"]
    }
  }
  }'
echo -e "${GREEN}=== 带extension的请求测试结束 ===${NC}"


