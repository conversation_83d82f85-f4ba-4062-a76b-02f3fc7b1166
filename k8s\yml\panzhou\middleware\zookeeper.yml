apiVersion: apps/v1
kind: Deployment
metadata:
  name: zookeeper
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
    spec:
      containers:
      - name: zookeeper
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/zookeeper:3.9.3
        ports:
        - containerPort: 2181
        env:
        - name: TZ
          value: Asia/Shanghai
        volumeMounts:
        - name: zk-data
          mountPath: /data
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      volumes:
      - name: zk-data
        hostPath:
          path: /data/zookeeper/data
---
apiVersion: v1
kind: Service
metadata:
  name: zookeeper
  namespace: oa-llm
spec:
  ports:
  - port: 2181
    targetPort: 2181
  selector:
    app: zookeeper
