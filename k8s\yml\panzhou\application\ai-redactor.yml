apiVersion: v1
kind: Service
metadata:
  labels:
    cmcc-gitops-file-name: ai-redactor.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-redactor
  namespace: oa-llm
spec:
  ports:
  - port: 8000
    targetPort: 8000
  selector:
    app: ai-redactor
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    cmcc-gitops-file-name: ai-redactor.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-redactor
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-redactor
  serviceName: ai-redactor
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: ai-redactor
    spec:
      containers:
      - image: artifactory.dep.devops.cmit.cloud:20101/native_common/ai_hegao_plus:250518-1530
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 5
          tcpSocket:
            port: 8000
          timeoutSeconds: 5
        name: ai-redactor
        ports:
        - containerPort: 8000
          protocol: TCP
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 20
          periodSeconds: 5
          tcpSocket:
            port: 8000
          timeoutSeconds: 10
        resources: {}
        volumeMounts:
        - mountPath: /app/ai_hegao_plus/ai_hegao_plus_app/application.properties
          name: ai-redactor-config
          subPath: application.properties
        - mountPath: /app/test.sh
          name: ai-redactor-shells
          subPath: test.sh
        - mountPath: /app/log
          name: short-term-logs
      - args:
        - -c
        - /opt/filebeat/filebeat.yml
        - -e
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/filebeat:7.17.14
        imagePullPolicy: Always
        name: filebeat
        resources: {}
        terminationMessagePath: /var/log/err.log
        volumeMounts:
        - mountPath: /opt/filebeat/filebeat.yml
          name: ai-redactor-filebeat-cm
          subPath: filebeat.yml
        - mountPath: /ai-redactor/data/logs 
          name: short-term-logs
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: short-term-logs
      - configMap:
          defaultMode: 420
          name: ai-redactor-config
        name: ai-redactor-config
      - configMap:
          defaultMode: 420
          name: ai-redactor-filebeat-cm
        name: ai-redactor-filebeat-cm
      - configMap:
          defaultMode: 420
          name: ai-redactor-shells
        name: ai-redactor-shells
  updateStrategy: {}
status:
  replicas: 0
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-redactor-config
  namespace: oa-llm
data:
  application.properties: |-
    # 大模型相关配置
    model-url={{model.url}}
    api-key={{model.api-key}}
    model-name={{model.model-name}}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-redactor-shells
  namespace: oa-llm
data:
  test.sh: |-
    echo "测试curl"
    echo "curl -X POST "http://localhost:8000/spellcheck/oa" \
    -H "Content-Type: application/json" \
    -d '{"content":"请输入你的测试文本。","type_list":[],"user_id":"123"}'"
    curl -X POST "http://localhost:8000/spellcheck/oa" \
      -H "Content-Type: application/json" \
      -d '{"content":"请输入你的测试文本。","type_list":[],"user_id":"123"}'
---
apiVersion: v1
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: log
      paths:
        - /ai-redactor/data/logs/*.log
      fields:
        envTag: "oa-llm"
        appTag: "ai-redactor"
        namespace: oa-llm
      multiline:
        pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'
        negate: true
        match: after
        max_lines: 500
    processors:
    - drop_fields:
        fields: ["agent","input","ecs"]
        ignore_missing: true
    output.elasticsearch:
      hosts: ["http://elasticsearch.oa-llm:9200"]
      index: "ai-redactor-logs-%{+yyyy.MM.dd}"
    setup.template.name: "ai-redactor-logs"
    setup.template.pattern: "ai-redactor-logs*"
    setup.ilm.enabled: false  # 禁用 ILM，避免自动 rollover 到 filebeat-*
kind: ConfigMap
metadata:
  labels:
    cmcc-gitops-project-tag: oa-llm 
  name: ai-redactor-filebeat-cm
  namespace: oa-llm