
#!/bin/bash
# 部署根目录设置
BASE_DIR="/data/MinIO"
mkdir -p ${BASE_DIR}/{data,logs,config} && chmod -R 777 ${BASE_DIR}  # 创建三级目录结构‌:ml-citation{ref="1,3" data="citationList"}

# 获取最新镜像（DaoCloud 加速源）
LATEST_TAG="RELEASE.2023-04-28T18-11-17Z"  # 2025年4月最新稳定版本‌:ml-citation{ref="6,7" data="citationList"}
docker pull m.daocloud.io/docker.io/minio/minio:${LATEST_TAG}

# 启动容器
docker run -itd --name minio-server  --hostname minio -e MINIO_ROOT_USER="admin" -e MINIO_ROOT_PASSWORD="MiniO@2025"  -v ${BASE_DIR}/data:/data   -v ${BASE_DIR}/logs:/var/log/minio  -p 9000:9000  -p 9001:9001  m.daocloud.io/docker.io/minio/minio:${LATEST_TAG} server /data --console-address ":9001"

echo "MinIO 部署完成！"
echo "访问地址：http://localhost:9001 (默认用户 admin:MiniO@2025)"
echo "配置文件：${BASE_DIR}/config"
echo "日志目录：${BASE_DIR}/logs"
echo "数据目录：${BASE_DIR}/data"

# 测试minio是否正常运行
echo "docker exec -it minio-server mc alias set myminio http://localhost:9000 admin MiniO@2025"
docker exec -it minio-server mc alias set myminio http://localhost:9000 admin MiniO@2025

# 用curl测试minio是否正常运行
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常""
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常"

# 用curl测试minio是否正常运行
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常""
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常"

# 用curl创建一个测试桶
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test | grep -q '"status":"success"' && echo "MinIO 创建桶成功" || echo "MinIO 创建桶失败""
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test | grep -q '"status":"success"' && echo "MinIO 创建桶成功" || echo "MinIO 创建桶失败"

# 用curl测试minio是否正常运行
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常""
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常"

# 上传一个测试文件
# 创建测试文件
echo "创建测试文件"
echo "测试内容" > test.txt

# 上传文件
echo "上传文件"
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X PUT -T test.txt"
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X PUT -T test.txt

# 下载文件
echo "下载文件"
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X GET -o test_downloaded.txt"
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X GET -o test_downloaded.txt

# 删除文件
echo "删除文件"
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X DELETE"
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X DELETE

