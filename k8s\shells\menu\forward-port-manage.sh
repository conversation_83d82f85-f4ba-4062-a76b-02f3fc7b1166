#!/bin/bash
# forward-port-manage.sh - 管理kubectl port-forward进程
# 支持查看、选择性删除和一次性删除所有端口转发

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 恢复默认颜色

# 获取脚本所在目录
CURRENT_DIR=$(cd "$(dirname "$0")" && pwd)
# 获取shells目录
SHELLS_DIR=$(dirname "${CURRENT_DIR}")

# 获取K8S目录 - 优先使用传入的参数
if [ ! -z "$1" ]; then
    K8S_DIR="$1"
    echo -e "${GREEN}使用传入的K8S目录: ${K8S_DIR}${NC}"
else
    # 如果未传入参数，尝试使用环境变量
    if [ ! -z "$K8S_DIR" ]; then
        echo -e "${GREEN}使用环境变量K8S_DIR: ${K8S_DIR}${NC}"
    else
        # 如果环境变量也未设置，则计算路径
        K8S_DIR=$(dirname "${SHELLS_DIR}")
        echo -e "${YELLOW}计算K8S目录: ${K8S_DIR}${NC}"
    fi
fi

echo -e "${BLUE}当前目录: ${CURRENT_DIR}${NC}"
echo -e "${BLUE}Shells目录: ${SHELLS_DIR}${NC}"
echo -e "${BLUE}K8s根目录: ${K8S_DIR}${NC}"

# 工具函数目录
UTILS_DIR="${SHELLS_DIR}/utils"

# 加载端口转发工具函数
if [ -f "${UTILS_DIR}/port_forward_utils.sh" ]; then
    source "${UTILS_DIR}/port_forward_utils.sh"
else
    echo -e "${RED}错误: 找不到端口转发工具函数 (${UTILS_DIR}/port_forward_utils.sh)${NC}"
    echo -e "${YELLOW}请确保该文件存在${NC}"
    exit 1
fi

# 清屏函数
clear_screen() {
    clear
}

# 显示标题
show_title() {
    echo -e "${BLUE}===============================================${NC}"
    echo -e "${BLUE}          Port-Forward 管理工具 v1.0          ${NC}"
    echo -e "${BLUE}===============================================${NC}"
    echo
}

# 查找所有端口转发进程
find_port_forward_processes() {
    # 以结构化方式获取所有port-forward进程
    PROCESSES=$(ps aux | grep "kubectl port-forward" | grep -v "grep" | awk '{print $2, $11, $12, $13, $14, $15, $16}')
    
    if [ -z "$PROCESSES" ]; then
        echo -e "${YELLOW}当前没有运行中的port-forward进程${NC}"
        return 1
    fi
    
    return 0
}

# 查看所有端口转发进程
list_port_forward_processes() {
    clear_screen
    show_title
    echo -e "${GREEN}当前运行中的Port-Forward进程:${NC}"
    echo
    
    if ! find_port_forward_processes; then
        echo
        echo -e "${YELLOW}按任意键返回主菜单...${NC}"
        read -n 1
        return
    fi
    
    # 显示表头
    echo -e "${BLUE}PID\t本地端口\t目标服务\t\t命名空间${NC}"
    echo -e "${BLUE}----------------------------------------------------${NC}"
    
    # 为每个进程显示详细信息
    local i=1
    while IFS= read -r process; do
        # 解析进程信息
        local pid=$(echo $process | awk '{print $1}')
        local cmd=$(echo $process | awk '{for(i=2;i<=NF;i++) printf "%s ", $i}')
        
        # 提取信息
        local port=$(echo $cmd | grep -oP '\d+(?=:)' | head -1)
        local service=$(echo $cmd | grep -oP 'svc/\K[^ ]+' | head -1)
        local namespace=$(echo $cmd | grep -oP -- '-n\s+\K[^ ]+' | head -1)
        
        if [ -z "$namespace" ]; then
            namespace=$(echo $cmd | grep -oP -- '--namespace\s+\K[^ ]+' | head -1)
        fi
        
        if [ -z "$namespace" ]; then
            namespace="default"
        fi
        
        if [ -n "$pid" ] && [ -n "$port" ] && [ -n "$service" ]; then
            echo -e "${GREEN}$i)${NC} ${pid}\t${port}\t\t${service}\t\t${namespace}"
            PIDS[$i]=$pid
            i=$((i+1))
        fi
    done <<< "$PROCESSES"
    
    echo
    echo -e "${YELLOW}按任意键返回主菜单...${NC}"
    read -n 1
}

# 删除单个端口转发进程
delete_single_process() {
    clear_screen
    show_title
    echo -e "${GREEN}选择要删除的Port-Forward进程:${NC}"
    echo
    
    if ! find_port_forward_processes; then
        echo
        echo -e "${YELLOW}按任意键返回主菜单...${NC}"
        read -n 1
        return
    fi
    
    # 显示表头
    echo -e "${BLUE}序号\tPID\t本地端口\t目标服务\t\t命名空间${NC}"
    echo -e "${BLUE}------------------------------------------------------------${NC}"
    
    # 为每个进程显示详细信息
    local i=1
    declare -a PIDS=()
    
    while IFS= read -r process; do
        # 解析进程信息
        local pid=$(echo $process | awk '{print $1}')
        local cmd=$(echo $process | awk '{for(i=2;i<=NF;i++) printf "%s ", $i}')
        
        # 提取信息
        local port=$(echo $cmd | grep -oP '\d+(?=:)' | head -1)
        local service=$(echo $cmd | grep -oP 'svc/\K[^ ]+' | head -1)
        local namespace=$(echo $cmd | grep -oP -- '-n\s+\K[^ ]+' | head -1)
        
        if [ -z "$namespace" ]; then
            namespace=$(echo $cmd | grep -oP -- '--namespace\s+\K[^ ]+' | head -1)
        fi
        
        if [ -z "$namespace" ]; then
            namespace="default"
        fi
        
        if [ -n "$pid" ] && [ -n "$port" ] && [ -n "$service" ]; then
            echo -e "${GREEN}$i)${NC} ${pid}\t${port}\t\t${service}\t\t${namespace}"
            PIDS[$i]=$pid
            i=$((i+1))
        fi
    done <<< "$PROCESSES"
    
    echo
    echo -e "${YELLOW}输入要删除的进程序号 (0返回): ${NC}"
    read choice
    
    if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -lt "$i" ]; then
        local selected_pid=${PIDS[$choice]}
        
        echo -e "${RED}正在终止进程 PID: $selected_pid...${NC}"
        kill -9 $selected_pid 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}成功终止进程!${NC}"
        else
            echo -e "${RED}终止进程失败!${NC}"
        fi
        
        echo
        echo -e "${YELLOW}按任意键继续...${NC}"
        read -n 1
    elif [ "$choice" == "0" ]; then
        return
    else
        echo -e "${RED}无效选择!${NC}"
        sleep 1
    fi
}

# 删除所有端口转发进程
delete_all_processes() {
    clear_screen
    show_title
    echo -e "${RED}删除所有Port-Forward进程${NC}"
    echo
    
    if ! find_port_forward_processes; then
        echo
        echo -e "${YELLOW}按任意键返回主菜单...${NC}"
        read -n 1
        return
    fi
    
    echo -e "${YELLOW}确认要终止所有kubectl port-forward进程吗? (yes/no): ${NC}"
    read confirm
    
    if [ "$confirm" = "yes" ]; then
        echo -e "${RED}正在终止所有kubectl port-forward进程...${NC}"
        pkill -f "kubectl port-forward" 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}成功终止所有进程!${NC}"
        else
            echo -e "${RED}终止进程失败或没有找到进程!${NC}"
        fi
    else
        echo -e "${YELLOW}操作已取消${NC}"
    fi
    
    echo
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -n 1
}

# 启动端口转发
start_port_forward() {
    clear_screen
    show_title
    
    # 使用配置文件启动所有端口转发
    CONFIG_FILE="${SHELLS_DIR}/config.json"
    
    # 检查配置文件是否存在
    if [ ! -f "$CONFIG_FILE" ]; then
        echo -e "${RED}错误: 配置文件不存在 ${CONFIG_FILE}${NC}"
        echo
        echo -e "${YELLOW}按任意键返回...${NC}"
        read -n 1
        return
    fi
    
    # YAML文件的基础路径
    YAML_BASE_PATH="${K8S_DIR}/yml"
    echo -e "${BLUE}尝试YAML路径: ${YAML_BASE_PATH}${NC}"
    
    # 检查YAML目录是否存在
    if [ ! -d "$YAML_BASE_PATH" ]; then
        echo -e "${YELLOW}警告: YAML目录不存在 ${YAML_BASE_PATH}${NC}"
        
        # 尝试其他可能的路径
        local possible_paths=(
            "${K8S_DIR}/yml"
            "/opt/jtws-init/k8s/yml"
            "${SHELLS_DIR}/../yml"
        )
        
        local found_path=""
        for path in "${possible_paths[@]}"; do
            echo -e "${BLUE}尝试路径: ${path}${NC}"
            if [ -d "$path" ]; then
                YAML_BASE_PATH="$path"
                found_path="$path"
                echo -e "${GREEN}找到YAML目录: ${YAML_BASE_PATH}${NC}"
                break
            fi
        done
        
        if [ -z "$found_path" ]; then
            echo -e "${YELLOW}请输入正确的YAML目录路径:${NC}"
            read -p "> " custom_yaml_path
            
            if [ -n "$custom_yaml_path" ] && [ -d "$custom_yaml_path" ]; then
                YAML_BASE_PATH="$custom_yaml_path"
                echo -e "${GREEN}使用自定义YAML路径: ${YAML_BASE_PATH}${NC}"
            else
                echo -e "${RED}提供的路径无效，操作取消${NC}"
                echo
                echo -e "${YELLOW}按任意键返回...${NC}"
                read -n 1
                return
            fi
        fi
    fi
    
    echo -e "${BLUE}YAML基础路径: ${YAML_BASE_PATH}${NC}"
    echo -e "${GREEN}正在启动端口转发...${NC}"
    echo "start_all_port_forwards_from_config ${CONFIG_FILE} oa-llm ${YAML_BASE_PATH}"
    start_all_port_forwards_from_config "$CONFIG_FILE" "oa-llm" "$YAML_BASE_PATH"
    echo "start_all_port_forwards_from_config 完成"
    echo
    echo -e "${YELLOW}按任意键返回...${NC}"
    read -n 1
}

# 主菜单
main_menu() {
    while true; do
        clear_screen
        show_title
        
        echo -e "${GREEN}1.${NC} 查看所有端口转发进程"
        echo -e "${GREEN}2.${NC} 删除单个端口转发进程"
        echo -e "${GREEN}3.${NC} 删除所有端口转发进程"
        echo -e "${GREEN}4.${NC} 启动新的端口转发"
        echo -e "${GREEN}0.${NC} 退出"
        echo
        echo -e "${YELLOW}请选择操作: ${NC}"
        read choice
        
        case $choice in
            1)
                list_port_forward_processes
                ;;
            2)
                delete_single_process
                ;;
            3)
                delete_all_processes
                ;;
            4)
                start_port_forward
                ;;
            0)
                clear_screen
                echo -e "${GREEN}感谢使用端口转发管理工具!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择!${NC}"
                sleep 1
                ;;
        esac
    done
}

# 启动主菜单
main_menu 