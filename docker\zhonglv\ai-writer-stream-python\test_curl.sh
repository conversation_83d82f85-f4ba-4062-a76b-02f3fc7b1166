#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# ====================== API测试命令 ======================
echo -e "${BLUE}=== API测试命令 ===${NC}"
echo "以下是基于test_api.py生成的CURL测试命令 (SSE流式输出):"
PORT=8095
# 设置测试参数
TEST_IP="localhost"
TEST_PORT=$PORT
APPID="ai-docwg"
APPKEY="074dc956d74099408e78a889663ac777"
TEST_URL="http://${TEST_IP}:${TEST_PORT}/Rag/ChatllmSSE"

echo "健康检查API访问地址: http://${TEST_IP}:${TEST_PORT}/health"
curl -X GET "http://${TEST_IP}:${TEST_PORT}/health" 

# 生成CURL测试命令 - 添加SSE支持参数
echo -e "${YELLOW}测试命令1: 生成通知类公文 (流式输出)${NC}"
echo '命令示例已省略，直接执行实际命令'


# 实际执行的命令
curl -X POST \
  "${TEST_URL}" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -H "appKey: ${APPKEY}" \
  --no-buffer \
  -d '{
  "accessMode": "STANDALONE",
  "composeUnitId": "qwenstream",
  "handlerId": "<EMAIL>",
  "text": "请以《关于召开xx会议通知1》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为xxxxxx，集团公司决定召开xx会议。现将有关事项通知如下。会议名称为：xx会议。会议时间为：2024年9月6日上午9:15。会场信息为：主会场设在xx，分会场设在xx。参会人员为：主会场参会人员和分会场参会人员。会议议程和参会要求请详细说明。联系人：xxx，电话：xxx。字数要求1800字以上。",
  "sid": "5af44c88-5d04-4fe1-9ae6-cd43b547cdd0",
  "domainCode": "XXJSNEW",
  "sysCode": "XXJSNEW",
  "sysName": "IT公司",
  "aiDocBizType": "COMPOSE",
  "extension": {
    "docInfo": {
      "sourceText": "关于召开xx会议通知1",
      "universalType": "通知",
      "subTypeName": "会议（活动）类通知",
      "mainDeliveryUnit": "请输入主送单位"
    }
  }
}'

echo
echo -e "${GREEN}注意事项:${NC}"
echo -e "${GREEN}1. ChatllmSSE为流式输出，上面的命令使用--no-buffer参数实时显示数据${NC}"
echo -e "${GREEN}2. 如果无法正常接收流式数据，也可尝试添加 -N 参数 (--no-buffer -N)${NC}"
echo -e "${GREEN}3. 如果API服务地址有变化，请修改以上命令中的IP和端口${NC}"
echo -e "${GREEN}4. 完整的API认证请参考test_api.py中的Http_Param类实现${NC}"

# 提供一个处理SSE数据的简单方法
echo
echo -e "${BLUE}=== 流式数据处理示例 ===${NC}"
echo "如需提取SSE流式数据中的具体字段，可使用如下命令:"
echo
echo 'curl -X POST \
  "'"${TEST_URL}"'" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -H "appKey: '"${APPKEY}"'" \
  --no-buffer \
  -d "{\"text\": \"请以《测试通知》为题生成一篇简短通知\", \"sid\": \"test-id\"}" | while read -r line; do
    if [[ $line == data:* ]]; then
      content="${line#data:}"
      if [ ! -z "$content" ] && [[ "$content" != "[DONE]" ]]; then
        echo "$content" | jq -r ".data.answerText"
      fi
    fi
  done'

echo -e "${GREEN}上述命令会实时提取并显示生成的文本内容${NC}"