apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/nginx:1.27.4
        ports:
        - containerPort: 80
        - containerPort: 8092
        volumeMounts:
        - name: nginx-conf
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        - name: nginx-html
          mountPath: /usr/share/nginx/html
        - name: nginx-log
          mountPath: /var/log/nginx
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      volumes:
      - name: nginx-conf
        hostPath:
          path: /data/nginx/conf
      - name: nginx-html
        hostPath:
          path: /data/nginx/html
      - name: nginx-log
        hostPath:
          path: /data/nginx/log
---
apiVersion: v1
kind: Service
metadata:
  name: nginx
  namespace: oa-llm
spec:
  ports:
  - port: 80
    targetPort: 80
  - port: 8092
    targetPort: 8092
  selector:
    app: nginx
