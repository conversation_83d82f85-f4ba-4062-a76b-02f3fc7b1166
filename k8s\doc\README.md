# OA-LLM 服务部署与运维手册

## 目录
- [一、系统架构](#一系统架构)
  - [1.1 系统架构图](#11-系统架构图)
  - [1.2 核稿服务调用图](#12-核稿服务调用图)
  - [1.3 拟稿服务调用图](#13-拟稿服务调用图)
- [二、核心目录说明](#二核心目录说明)
  - [2.1 目录结构](#21-目录结构)
  - [2.2 核心目录说明](#22-核心目录说明)
  - [2.3 配置文件体系](#23-配置文件体系)
  - [2.4 典型工作流程](#24-典型工作流程)
- [三、部署手册](#三部署手册)
  - [3.1 使用菜单进行部署](#31-使用菜单进行部署)
  - [3.2 配置文件说明](#32-配置文件说明)
- [四、部署步骤](#四部署步骤)
  - [3.1 准备配置文件](#31-准备配置文件)
  - [3.2 下载镜像](#32-下载镜像)
  - [3.3 替换配置文件](#33-替换配置文件)
- [五、运维手册](#五运维手册)
  - [5.1 服务管理](#51-服务管理)
  - [5.2 日志管理](#52-日志管理)
  - [5.3 调试功能](#53-调试功能)
- [六、接口测试](#六接口测试)
  - [6.1 进入容器测试](#61-进入容器测试)
  - [6.2 模型测试](#62-模型测试)
- [七、服务台账](#七服务台账)
  - [7.1 核稿系统服务](#71-核稿系统服务)
  - [7.2 拟稿系统服务](#72-拟稿系统服务)
  - [7.3 中间件服务](#73-中间件服务)
  - [7.4 日志收集](#74-日志收集)
- [八、访问指南](#八访问指南)
  - [8.1 系统访问地址](#81-系统访问地址)
  - [8.2 访问说明](#82-访问说明)
- [九、常见问题](#九常见问题)
  - [9.1 前端页面无法访问](#91-前端页面无法访问)
- [十、注意事项](#十注意事项)
  - [10.1 部署前检查](#101-部署前检查)
  - [10.2 运维建议](#102-运维建议)
  - [10.3 故障处理](#103-故障处理)

## 一、系统架构
### 1.1 系统架构图  
![系统架构](./images/架构图-部署架构图.png)

系统架构说明
系统采用Kubernetes容器编排平台进行部署，整体架构分为以下几个主要部分：

1. 接入层
- 通过Nginx反向代理统一管理外部访问
- 支持端口模式和域名模式两种访问方式
- 提供统一的访问入口和负载均衡

2. 应用服务层
- 核稿系统服务
  - ai-redactor: Python服务，提供文本纠错功能
  - ai-doc-poc: Java服务，提供文档处理功能
  - poc-intelligence-view: 前端服务，提供用户界面
- 拟稿系统服务
  - ai-writer-nostream: 提供文章生成功能
  - article-auto-compose-server: 提供文章自动组合功能
  - poc-intelligence-view-write: 拟稿系统前端服务

3. 中间件层
- Elasticsearch: 提供全文检索和日志存储功能
- Kibana: 提供日志可视化和分析功能
- Filebeat: 负责日志收集和传输

### 1.2 核稿服务调用图
![核稿服务调用图](./images/架构图-核稿服务调用图.png)
核稿服务调用流程说明：

1. 用户访问流程
- 用户通过浏览器访问前端服务(poc-intelligence-view)
- 前端服务提供Web界面，处理用户交互
- 用户提交待核稿文档到前端服务

2. 核心服务调用
- 前端服务调用ai-redactor服务进行文本纠错
- ai-redactor服务调用大模型API进行智能纠错
- 纠错结果返回给前端服务展示

3. 数据存储
- 文档处理服务(ai-doc-poc)负责文档的存储和管理
- 使用Elasticsearch存储文档索引和检索数据
- 通过Kibana提供日志可视化和分析功能

4. 日志收集
- Filebeat负责收集各服务的运行日志
- 日志统一发送到Elasticsearch存储
- 通过Kibana进行日志查询和分析

5. 服务间通信
- 服务间通过Kubernetes Service进行内部通信
- 使用HTTP/HTTPS协议进行API调用
- 通过Nginx反向代理统一管理外部访问


### 1.3 拟稿服务调用图
![拟稿服务调用图](./images/架构图-核稿服务调用图.png)
拟稿服务调用流程说明：

1. 用户访问流程
- 用户通过浏览器访问拟稿系统前端(poc-intelligence-view-write)
- 前端服务提供智能写作界面，处理用户交互
- 用户提交写作需求到前端服务

2. 核心服务调用
- 前端服务调用ai-writer-nostream服务进行文章生成
- ai-writer-nostream服务调用大模型API进行智能写作
- 生成的文章返回给前端服务展示

3. 文章组合服务
- article-auto-compose-server负责文章的自动组合和优化

4. 数据存储
- 使用Elasticsearch存储文章索引和检索数据
- 通过Kibana提供日志可视化和分析功能
- 支持文章历史版本管理和检索

5. 日志收集
- Filebeat负责收集各服务的运行日志
- 日志统一发送到Elasticsearch存储
- 通过Kibana进行日志查询和分析

6. 服务间通信
- 服务间通过Kubernetes Service进行内部通信
- 使用HTTP/HTTPS协议进行API调用
- 通过Nginx反向代理统一管理外部访问

## 二、核心目录说明

### 2.1 目录结构
```
k8s/
├── yml/ # Kubernetes资源配置目录
│ ├── template/ # 配置模板文件
│ └── work/ # 生成的实际部署文件
├── shells/ # 部署运维脚本
│ ├── k8s-menu.sh # 主控制菜单脚本
│ ├── create_config_file.sh # 配置文件生成脚本
│ └── download-images.sh # 镜像下载脚本
├── test-script/ # 测试相关脚本
└── README.md # 本说明文档
```

### 2.2 核心目录说明

#### 2.2.1 yml目录
- **template/**：存放所有K8s资源配置模板文件
  - 文件命名规则：`<服务名>.yml`
  - 使用`{{}}`包裹变量占位符，如`{{target-image-repo-url}}`
- **work/**：自动生成的最终部署文件
  - 由`replace-ymls.sh`脚本生成
  - 包含实际替换后的资源配置文件

#### 2.2.2 shells目录
| 脚本文件                  | 功能说明                             |
|--------------------------|------------------------------------|
| k8s-menu.sh              | 主控制台菜单，提供部署运维功能入口      |
| create_config_file.sh    | 交互式创建/更新config.json配置文件    |
| download-images.sh       | 批量下载所有服务镜像                  |
| replace-ymls.sh          | 替换YAML模板中的变量生成部署文件       |

#### 2.2.3 test-script目录
- 包含服务健康检查脚本
- 接口测试用例脚本
- 压力测试脚本
- 日志分析工具

### 2.3 配置文件体系
config-template.json # 配置模板
config.json # 实际使用的配置文件（由菜单生成）
├── 镜像仓库配置
├── 服务端口映射
├── 存储配置
└── 代理配置

### 2.4 典型工作流程
1. 通过`k8s-menu.sh`选择"准备配置文件"生成config.json
2. 使用`download-images.sh`下载所需镜像
3. 运行`replace-ymls.sh`生成最终部署文件
4. 使用`kubectl apply -f yml/work/`进行部署

## 三、部署手册

### 3.1 部署流程
![部署流程](./images/部署流程.png)

部署流程详细说明：

1. **配置阶段**
   - 配置文件生成
     - 基于 config-template.json 生成 config.json ./shells/create_config_file.sh
   - YAML 模板处理
     - 替换模板中的变量占位符
     - 生成最终的部署文件
     - 验证 YAML 文件格式
2. **镜像准备**
   - 从源仓库下载所需镜像 ./shells/download-images.sh
     镜像地址：https://officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/
     在config-template.json的source-image-repo-url字段配置
   - 推送镜像到目标仓库 ./shells/push-images.sh
     目标仓库地址：在config-template.json的target-image-repo-url字段配置
   - 验证镜像完整性
3. **部署阶段**
   - 中间件服务部署
     - 部署 Elasticsearch
     - 配置 Kibana 服务
     - 设置日志收集
   - 应用服务部署
     - 部署核稿系统服务
       - ai-redactor 服务
       - ai-doc-poc 服务
       - poc-intelligence-view 服务
     - 部署拟稿系统服务
       - ai-writer-nostream 服务
       - article-auto-compose-server 服务
       - poc-intelligence-view-write 服务
   - Nginx 代理部署
     - 在config-template.json的nginx-config.mode字段配置
     - 如果nginx-config.mode字段配置为port，则使用nginx-config.proxy-port字段配置的端口
     - 如果nginx-config.mode字段配置为server-name，则使用nginx-config.service-name-suffix字段配置的后缀

4. **验证阶段**
   - 服务状态检查
     - 检查 Pod 运行状态
     - 验证服务健康检查
     - 确认服务就绪状态
   - 访问验证
     - 测试服务访问
     - 验证端口转发
     - 检查域名解析
   - 功能测试
     - 测试核心业务功能
     - 验证接口调用
     - 检查数据流转

### 3.2 使用菜单进行部署
将k8s目录拷贝到目标服务器，执行菜单脚本
#### 3.2.1 启动菜单
```bash
# 进入k8s目录
cd oa-llm-ops/k8s

# 执行菜单脚本
./shells/k8s-menu.sh
```

#### 3.2.2 部署步骤
1. **准备配置文件**
   - 选择菜单选项 `1`
   - 系统会自动创建配置文件
   - 如果配置文件已存在，可以选择是否重新创建

2. **下载镜像**
   - 选择菜单选项 `2`
   - 系统会根据配置文件下载所需的镜像
   - 确保网络连接正常

3. **替换配置文件**
   - 选择菜单选项 `3`
   - 系统会根据配置文件替换 YAML 文件中的变量

4. **部署服务**
   - 选择菜单选项 `4`
   - 系统会部署 Nginx 反向代理
   - 选择菜单选项 `4` 进入部署子菜单
   - 按照提示选择要部署的服务

### 3.3 配置文件说明

#### 3.3.1 基础配置字段
- `cpu-arch`: CPU架构，支持 arm64 等
- `target-image-repo-url`: 目标镜像仓库地址
- `target-image-repo-user`: 目标镜像仓库用户名
- `target-image-repo-password`: 目标镜像仓库密码
- `target-image-namespace`: 目标镜像仓库命名空间
- `source-image-repo-url`: 源镜像仓库地址
- `source-image-repo-user`: 源镜像仓库用户名
- `source-image-repo-password`: 源镜像仓库密码
- `source-image-namespace`: 源镜像仓库命名空间

#### 3.3.2 存储配置
- `storage-class`: 目标集群的存储类名称
- `server`: 存储服务器地址
- `path`: 存储路径
- `enabled`: 是否启用存储

#### 3.3.3 代理配置
- `proxy-model`: 代理模式，支持 nginx/forward
- `proxy-port`: 每个服务的代理端口
- `nginx-config.mode`: NGINX代理模式，支持 port/server-name
- `nginx-config.service-name-suffix`: 服务名称后缀
- `nginx-config.image-url`: NGINX镜像地址

#### 3.3.4 模型配置
- `model.url`: 模型服务地址
- `model.api-key`: 模型服务API密钥
- `model-name`: 模型名称

#### 3.3.5 服务配置
每个服务包含以下配置：
- `source-images-url`: 源镜像地址
- `target-images-url`: 目标镜像地址
- `yml-file-name`: YAML文件名
- `description`: 服务描述
- `service-name`: 服务名称
- `type`: 服务类型（application/middleware）
- `proxy-port`: 代理端口

#### 3.3.6 服务列表
1. **中间件服务**
   - Elasticsearch
     - 端口：32000
     - 类型：middleware
     - 功能：日志存储和检索
   - Kibana
     - 端口：32001
     - 类型：middleware
     - 功能：日志可视化

2. **前端服务**
   - 拟稿前端 (poc-intelligence-view-write)
     - 端口：32002
     - 类型：application
     - 功能：智能拟稿系统界面
   - 核稿前端 (poc-intelligence-view)
     - 端口：32003
     - 类型：application
     - 功能：智能核稿系统界面

3. **后端服务**
   - 核稿Java端 (ai-doc-poc)
     - 端口：32004
     - 类型：application
     - 功能：文档检查服务
   - 核稿Python端 (ai-redactor)
     - 端口：32005
     - 类型：application
     - 功能：文本纠错服务
   - 拟稿Java端 (article-auto-compose-server)
     - 端口：32006
     - 类型：application
     - 功能：文章生成服务
   - 拟稿Python端 (ai-writer-nostream)
     - 端口：32007
     - 类型：application
     - 功能：文本生成服务

4. **系统入口**
   - Nginx (jtws)
     - 类型：application
     - 代理：禁用
     - 功能：统一入口和反向代理

#### 3.3.7 镜像配置
- Filebeat镜像
  - 源：`{{source-image-repo-url}}/{{source-image-namespace}}/filebeat:{{cpu-arch}}-7.17.14`
  - 目标：`{{target-image-repo-url}}/{{target-image-namespace}}/filebeat:{{cpu-arch}}-7.17.14`
  - 功能：日志收集

- Nginx镜像
  - 源：`{{source-image-repo-url}}/{{source-image-namespace}}/nginx:{{cpu-arch}}-1.27.4`
  - 目标：`{{target-image-repo-url}}/{{target-image-namespace}}/nginx:{{cpu-arch}}-1.27.4`
  - 功能：反向代理和静态资源服务

## 四、部署步骤

### 4.1 准备配置文件

**功能说明：**
- 通过菜单选项 `1`，自动生成或重新创建部署所需的 config.json 配置文件。
- 支持交互式填写或覆盖已有配置。

**如何配置：**
- 进入主菜单，选择 `1. 准备配置文件`。
- 按照提示填写镜像仓库、服务端口、模型参数等关键信息。
- 如果 config.json 已存在，可选择是否覆盖。

**脚本功能：**
- 检查 config.json 是否存在。
- 调用 create_config_file.sh 脚本，生成标准化的配置文件。
- 支持参数校验和默认值提示。

**最终效果：**
- 在 shells 目录下生成 config.json，内容结构与 config-template.json 一致，后续所有操作均依赖此文件。
- 配置完成后，系统会提示"配置文件创建成功"或"跳过配置文件创建"。
```
================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

1. 准备配置文件
2. 下载镜像
3. 替换配置文件
--------------------------------
4. 服务部署菜单
5. 查看所有服务状态
6. 管理特定服务
7. 显示访问指南
8. 清理资源
9. 管理调试Pod
10. 管理端口转发
0. 退出

请选择操作 [0-10]:
```

### 4.2 下载镜像

**功能说明：**
- 通过菜单选项 `2`，自动拉取 config.json 中定义的所有服务和中间件镜像。
- 支持多架构、多仓库镜像拉取。

**如何配置：**
- 确保 config.json 已正确配置镜像仓库地址、用户名、密码、镜像列表等。
- 进入主菜单，选择 `2. 下载镜像`。
- 系统会自动根据配置文件批量拉取镜像。

**脚本功能：**
- 检查 config.json 是否存在。
- 调用 download-images.sh 脚本，循环拉取所有服务和中间件镜像。
- 支持失败重试和错误提示。

**最终效果：**
- 镜像全部下载到本地或目标仓库，便于后续部署。
- 下载完成后，系统会提示"镜像下载完成"或"镜像下载失败"。
```
================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

开始下载镜像...
登录源镜像仓库
WARNING! Using --password via the CLI is insecure. Use --password-stdin.
WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
Configure a credential helper to remove this warning. See
https://docs.docker.com/engine/reference/commandline/login/#credentials-store

Login Succeeded
下载[middleware:elasticsearch]镜像: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14 -> 10.7.202.240:8080/native_common/elasticsearch:arm64-7.17.14
arm64-7.17.14: Pulling from jtws/elasticsearch
Digest: sha256:d0f7cf9064bbc124a765cfc4cae4897cca33eccdd2f00d6bb4e633fa7793a414
Status: Image is up to date for officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
下载[middleware:kibana]镜像: officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14 -> 10.7.202.240:8080/native_common/kibana:arm64-7.17.14
arm64-7.17.14: Pulling from jtws/kibana
......
```

### 4.3 替换配置文件

**功能说明：**
- 通过菜单选项 `3`，自动将 config.json 中的变量替换到所有 YAML 模板文件，生成实际部署用的 YAML 文件。
- 支持批量替换、变量校验。

**如何配置：**
- 确保 config.json 已正确配置所有变量。
- 进入主菜单，选择 `3. 替换配置文件`。
- 系统会自动处理所有模板文件，生成实际部署文件。

**脚本功能：**
- 检查 config.json 是否存在。
- 调用 replace-ymls.sh 脚本，遍历所有 yml/template 目录下的 YAML 文件，按变量批量替换。
- 支持替换校验和错误提示。

**最终效果：**
- 在 yml/work 目录下生成所有实际部署用的 YAML 文件，内容已根据 config.json 自动填充。
- 替换完成后，系统会提示"配置文件替换完成"或"配置文件替换失败"。
```
================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

开始替换配置文件...
application  middleware  storage
处理文件: /opt/jtws-init/k8s/yml/work/middleware/elasticsearch-nostorage.yml
替换 {{elasticsearch.image-url}} -> 10.7.202.240:8080/native_common/elasticsearch:arm64-7.17.14
替换 {{filebeat.image-url}} -> 10.7.202.240:8080/native_common/filebeat:arm64-7.17.14
替换storage-config中的所有{{字段}}
........
```

## 五、运维手册

### 5.1 菜单运维

#### 5.1.1 服务管理

#### 5.1.1.1 查看服务状态
```
===============================================
         K8s服务管理控制台 v1.6
===============================================
K8s根目录：/opt/jtws-init/k8s
-----------------------------------------------
1. 准备配置文件
2. 下载镜像
3. 替换配置文件
-----------------------------------------------
4. 服务部署菜单
5. 查看所有服务状态
6. 管理特定服务
7. 显示访问指南
8. 清理资源
9. 管理调试Pod
10. 管理端口转发
-----------------------------------------------
0. 退出

请选择操作 [0-10]:
```

- 选择菜单选项 `5` 查看服务状态
- 显示所有服务的运行状态
- 包括 Pod 状态和服务详情
- 显示 Nginx 代理状态

服务状态显示效果：
![服务状态](./images/菜单_所有服务状态.png)

#### 5.1.1.2 管理特定服务
```
================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

选择要管理的服务

可用服务:
1. ai-doc-poc-0
2. ai-redactor-0
3. ai-writer-nostream-0
4. article-auto-compose-server-0
5. elasticsearch-86b4b8c6f6-jj5lm
6. kibana-58fbd75bf9-5hxp5
7. nginx-proxy-764dd4c69c-j52ws
8. poc-intelligence-view-0
9. poc-intelligence-view-write-0

选择服务编号 [1-9] (或输入 0 返回):

================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

当前管理服务: ai-redactor-0

1. 查看服务状态
2. 查看Pod日志
3. 进入容器
4. 诊断服务问题
5. 重启服务
6. 检测资源类型
7. 调试服务容器
8. 删除服务
9. 返回主菜单
0. 退出

请选择操作 [0-9]: 2

================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

进入服务 ai-redactor-0 容器

Pod ai-redactor-0 包含多个容器：
[0] ai-redactor
[1] filebeat
请选择要进入的容器序号:0

```


#### 5.1.2 日志管理

##### 5.1.2.1 菜单查看日志
日志查看界面示例：
```
================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

当前管理服务: ai-redactor-0

1. 查看服务状态
2. 查看Pod日志
3. 进入容器
4. 诊断服务问题
5. 重启服务
6. 检测资源类型
7. 调试服务容器
8. 删除服务
9. 返回主菜单
0. 退出

请选择操作 [0-9]:
2

================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

查看服务 ai-redactor-0 日志

Pod ai-redactor-0 包含多个容器：
[0] ai-redactor
[1] filebeat
请选择要查看日志的容器序号: 0
正在查看容器 ai-redactor 的日志，按 Ctrl+C 可退出日志查看...
最近的10行日志：
正在持续监控日志（按Ctrl+C退出监控）...
```
##### ******* Kibana查看日志
1. 登录Kibana：`http://<节点IP>:32001/`
2. 在菜单栏选择Discover
3. 在左侧选择日志类型，如：`ai-redactor`
4. 在右侧选择时间范围，如：`最近1小时`
5. 在右侧查询框输入查询条件，按照KQL语法查询，KQL语法参考：https://www.elastic.co/guide/en/kibana/current/kuery-query.html
6. 点击查询按钮，即可看到日志
 
##### ******* 调试 Pod
```
================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

调试服务 ai-redactor-0 容器

请选择操作:
1. 进入调试模式 (修改容器命令以保持运行并进入shell)
2. 删除资源 (删除Pod或控制器)
0. 返回

请选择 [0-2]:

```
会将容器强制启动起来，但业务服务不会起来，可以在容器中启动服务的脚本，各个服务在容器中的启动脚本如台账中所示


#### 5.1.4 端口转发

本系统的端口转发功能由 Nginx 反向代理实现，所有配置和自动化由 proxy-port-nginx.sh 脚本完成。

**功能说明：**
- 支持两种代理模式：
  - 端口模式（port）：每个服务分配独立 NodePort，外部通过 http://节点IP:端口 访问。
  - 域名模式（server-name）：所有服务共用一个端口，通过不同二级域名区分，需配置 hosts 或 DNS。
- 端口、服务名、代理模式等均在 config-template.json 的 replace-config 和 proxy-config 字段中配置。

**配置示例：**
```json
{
  "replace-config": [
    { "service-name": "ai-redactor", "proxy-port": "32005", ... }
  ],
  "proxy-config": {
    "proxy-model": "nginx",
    "nginx-config": {
      "mode": "port", // 或 "server-name"
      ...
    }
  }
}
```

**自动化流程：**
1. 运行菜单"服务部署菜单" → "部署/更新Nginx反向代理"时，自动：
```
================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

1. 准备配置文件
2. 下载镜像
3. 替换配置文件
--------------------------------
4. 服务部署菜单
5. 查看所有服务状态
6. 管理特定服务
7. 显示访问指南
8. 清理资源
9. 管理调试Pod
10. 管理端口转发
0. 退出

请选择操作 [0-10]:
4
================================================
             服务部署子菜单 v1.0
================================================

1. 部署所有服务
2. 选择单个服务部署
3. 部署/更新Nginx反向代理
4. 一键部署所有服务并配置Nginx
9. 返回主菜单
0. 退出

请选择操作 [0-9]:
3
```
- 解析 config.json，生成 Nginx 主配置和每个服务的反向代理配置（conf.d/*.conf）。
- 创建/更新 Nginx 的 ConfigMap、Deployment、Service。
- 端口模式下，为每个服务创建独立 NodePort Service；域名模式下，创建单一 NodePort Service。
- 自动清理旧配置，重建 Nginx 相关 K8s 资源，确保端口转发和代理配置实时生效。

**端口与服务映射关系：**
- 端口模式下，config.json 里 proxy-port 字段即为外部访问端口。
  - 例如：
    - ai-redactor: http://<节点IP>:32005
    - ai-doc-poc:  http://<节点IP>:32004
- 域名模式下，需配置 hosts，如：
    <节点IP> ai-redactor.oa-llm.jtws.com
    <节点IP> ai-doc-poc.oa-llm.jtws.com

**常用操作流程：**
1. 在 config-template.json 配置好各服务的 proxy-port 和代理模式。
2. 通过菜单选择"服务部署菜单" → "部署/更新Nginx反向代理"，自动生成并应用端口转发配置。
3. 端口模式下，直接通过 http://节点IP:端口 访问服务。
4. 域名模式下，配置 hosts 或 DNS 后，通过 http://服务名.域名后缀:端口 访问服务。

**注意事项：**
- 端口号不可冲突，需在 config.json 中合理分配。
- 每次修改端口或服务配置后，需重新运行"部署/更新Nginx反向代理"。
- 端口模式适合内网直连，域名模式适合统一入口和多服务场景。

端口转发菜单及操作示例：
```
================================================
           K8s服务管理控制台 v1.6
================================================
K8s根目录: /opt/jtws-init/k8s

1. 准备配置文件
2. 下载镜像
3. 替换配置文件
--------------------------------
4. 服务部署菜单
5. 查看所有服务状态
6. 管理特定服务
7. 显示访问指南
8. 清理资源
9. 管理调试Pod
10. 管理端口转发
0. 退出

请选择操作 [0-10]:
4
================================================
             服务部署子菜单 v1.0
================================================

1. 部署所有服务
2. 选择单个服务部署
3. 部署/更新Nginx反向代理
4. 一键部署所有服务并配置Nginx
9. 返回主菜单
0. 退出

请选择操作 [0-9]:
3
```
### 5.2 Lens运维
Lens是K8s的图形化管理工具，可以查看K8s的资源、日志、调试Pod等。
#### 5.2.1 部署Lens
1.从https://k8slens.dev/下载Lens的安装包，解压后运行即可。
2.按照提示登录即可。可申请一个github账号，使用github账号登录。
#### 5.2.2 下载kubeconfig文件
1.在K8s的marster节点执行以下命令，下载kube_config文件：`/root/.kube/config`
2.将kube_config文件复制到本地，并命名为`kube_config.yaml`
3.在Lens中导入kube_config.yaml文件，即可连接到K8s集群。如下图所示
![Lens连接K8s](./images/Lens连接K8s.png)

## 六、接口测试

### 6.1 进入容器测试

#### 6.1.1 核稿服务(ai-redactor)测试
1. 进入容器：
```bash
# 进入容器
kubectl exec -it ai-redactor-0 -n oa-llm -c ai-redactor -- /bin/bash
```

2. 执行测试脚本：
```bash
# 执行测试脚本
sh /app/test.sh
```

测试脚本会执行以下测试：
- 大模型接口测试：测试与大模型的连接
- 本服务接口测试：测试核稿服务的文本纠错功能

#### 6.1.2 拟稿服务(ai-writer-nostream)测试
1. 进入容器：
```bash
# 进入容器
kubectl exec -it ai-writer-nostream-0 -n oa-llm -c ai-writer-nostream -- /bin/bash
```

2. 执行测试脚本：
```bash
# 执行测试脚本
sh /app/shells/test.sh
```

测试脚本会执行以下测试：
- 大模型接口测试：测试与大模型的连接
- 简单请求测试：测试基本的文章生成功能
- 带extension的请求测试：测试带扩展信息的文章生成功能

### 6.2 模型测试
测试模型是否可用
```bash
curl http://<模型IP>:<模型端口>/v1/chat/completions \
-H "Content-Type: application/json" \
-H 'Authorization: Bearer <API-KEY>' \
-d '{ 
  "model": "<模型名称>",
  "messages": [{"role": "user", "content": "请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求```1800```字以上。"}]
}'
```

## 七、服务台账

#### 7.1 核稿系统服务

##### 7.1.1 核稿Python服务(ai-redactor)
- 服务类型：Python服务
- 容器名称：ai-redactor
- 配置文件：
  - 路径：`/app/ai_hegao_plus_app/application.properties`
  - 内容：大模型URL、API密钥、模型名称等配置
- 日志路径：`/app/log`
- 测试脚本：
  - 路径：`/app/test.sh`
  - 功能：测试大模型连接和文本纠错功能
- 启动方式：容器启动时自动执行
- 服务端口：8000
- 健康检查：
  - 就绪探针：TCP 8000端口
  - 存活探针：TCP 8000端口

##### 7.1.2 核稿Java服务(ai-doc-poc)
- 服务类型：Java服务
- 容器名称：ai-doc-poc
- 配置文件：
  - 路径：`/app/config/application-prod.yml`
  - 内容：服务端口、上下文路径、大模型配置等
- 日志路径：`/app/logs`
- 启动脚本：
  - 路径：`/app/shells/start.sh`
  - 功能：启动Java应用，加载配置文件
- 服务端口：8080
- 上下文路径：`/ai-doc-poc`
- 健康检查：
  - 就绪探针：TCP 8080端口
  - 存活探针：TCP 8080端口

##### 7.1.3 核稿前端服务(poc-intelligence-view)
- 服务类型：前端服务
- 容器名称：poc-intelligence-view
- 配置文件：
  - 路径：`/etc/nginx/nginx.conf`
  - 内容：Nginx配置，包含静态资源路径、代理配置等
- 日志路径：`/var/log/nginx/`
- 测试脚本：
  - 路径：`/test.sh`
  - 功能：测试文档检查服务的直连和代理访问
- 启动方式：容器启动时自动执行
- 服务端口：80
- 访问路径：`/poc-intelligence-view/`

#### 7.2 拟稿系统服务

##### 7.2.1 拟稿Python服务(ai-writer-nostream)
- 服务类型：Python服务
- 容器名称：ai-writer-nostream
- 配置文件：
  - 路径：`/app/temp/web_source_code/backend/application.properties`
  - 内容：大模型配置、文件路径配置、生成参数等
- 日志路径：`/app/temp/web_source_code/log`
- 测试脚本：
  - 路径：`/app/shells/test.sh`
  - 功能：测试大模型连接和文章生成功能
- 启动脚本：
  - 路径：`/app/shells/start.sh`
  - 功能：启动Python应用
- 服务端口：8080
- 健康检查：
  - 就绪探针：TCP 8080端口
  - 存活探针：TCP 8080端口

##### 7.2.2 拟稿Java服务(article-auto-compose-server)
- 服务类型：Java服务
- 容器名称：article-auto-compose-server
- 配置文件：
  - 路径：`/app/config/application-prod.yml`
  - 内容：服务端口、上下文路径、大模型配置等
- 日志路径：`/app/logs`
- 启动脚本：
  - 路径：`/app/shells/start.sh`
  - 功能：启动Java应用，加载配置文件
- 服务端口：8080
- 上下文路径：`/ai-compose-poc`
- 健康检查：
  - 就绪探针：TCP 8080端口
  - 存活探针：TCP 8080端口

##### 7.2.3 拟稿前端服务(poc-intelligence-view-write)
- 服务类型：前端服务
- 容器名称：poc-intelligence-view-write
- 配置文件：
  - 路径：`/etc/nginx/nginx.conf`
  - 内容：Nginx配置，包含静态资源路径、代理配置等
- 日志路径：`/var/log/nginx/`
- 测试脚本：
  - 路径：`/test.sh`
  - 功能：测试智能写作服务的直连和代理访问
- 启动方式：容器启动时自动执行
- 服务端口：80
- 访问路径：`/poc-intelligence-view-write/intelligent-writing`

#### 7.3 中间件服务

##### 7.3.1 Elasticsearch服务
- 服务类型：中间件
- 容器名称：elasticsearch
- 配置文件：通过ConfigMap配置
- 日志路径：通过Filebeat收集
- 服务端口：9200(HTTP)、9300(TCP)
- 健康检查：内置健康检查

##### 7.3.2 Kibana服务
- 服务类型：中间件
- 容器名称：kibana
- 配置文件：通过ConfigMap配置
- 日志路径：通过Filebeat收集
- 服务端口：5601
- 健康检查：内置健康检查

#### 7.4 日志收集

所有服务都配置了Filebeat进行日志收集：
- 日志收集配置：`/opt/filebeat/filebeat.yml`
- 日志输出：Elasticsearch
- 索引格式：`{服务名}-logs-{日期}`
- 日志保留：通过Elasticsearch的索引管理策略控制

## 八、访问指南

### 8.1 系统访问地址
1. **核稿系统**
   - 前端地址：`http://<节点IP>:32003/poc-intelligence-view/`
   - 后端服务：
     - 文档检查：`http://<节点IP>:32004/ai-doc-poc`
     - 文本纠错：`http://<节点IP>:32005`

2. **拟稿系统**
   - 前端地址：`http://<节点IP>:32002/poc-intelligence-view-write/intelligent-writing`
   - 后端服务：
     - 文章生成：`http://<节点IP>:32006/ai-compose-poc`
     - 文本生成：`http://<节点IP>:32007`

3. **中间件服务**
   - Elasticsearch：`http://<节点IP>:32000`
   - Kibana：`http://<节点IP>:32001`

### 8.2 访问说明
1. **直接访问**
   - 使用节点IP和对应端口直接访问各服务
   - 适用于内网环境或已配置网络访问的环境

2. **代理访问**
   - 通过Nginx反向代理访问服务
   - 支持域名模式和端口模式
   - 域名模式需要配置hosts或DNS

3. **安全访问**
   - 建议通过VPN或专线访问
   - 注意保护API密钥等敏感信息
   - 定期更新访问凭证
## 九、常见问题
### 9.1 前端页面无法访问
1.检查对应前端服务是否正常运行
2.进入容器，用curl命令检测访问静态资源是否正常
如：核稿系统前端页面无法访问，则进入`poc-intelligence-view`容器，用curl命令检测访问静态资源是否正常
```bash
#进入容器
sh k8s-menu.sh
#选择<管理特定服务>
#选择<进入容器>
#选择<进入poc-intelligence-view容器>
#用curl命令检测访问静态资源是否正常
curl http://localhost/poc-intelligence-view/
#如果报错403，则赋予权限
chmod -R 755 /usr/share/nginx/html
```

## 十、注意事项

### 10.1 部署前检查
1. 确保配置文件正确
2. 检查镜像是否可用
3. 确认网络连接正常
4. 验证资源是否充足

### 10.2 运维建议
1. 定期检查服务状态
2. 及时查看错误日志
3. 保持配置文件备份
4. 谨慎执行删除操作

### 10.3 故障处理
1. 优先查看服务日志
2. 使用诊断功能分析问题
3. 必要时重启服务
4. 保持操作记录
