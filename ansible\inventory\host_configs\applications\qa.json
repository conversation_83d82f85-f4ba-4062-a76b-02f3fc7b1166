{"app_name": "qa", "module_name": "znwd", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "0.0.1-SNAPSHOT", "image_name": "znwd/qa:0.0.1-SNAPSHOT", "container_port": "8080", "host_port": "8083", "app_data_dir": "/app/files", "app_logs_dir": "/app/logs", "app_config_dir": "/app/config", "host_data_dir": "{{base_dir}}/znwd/qa/data", "host_logs_dir": "{{base_dir}}/znwd/qa/log", "host_config_dir": "{{base_dir}}/znwd/qa/config", "restart_script": "{{base_dir}}/znwd/qa/restart.sh", "test_script": "{{base_dir}}/znwd/qa/test_curl.sh", "runtime": "java17", "env_vars": [{"name": "JAVA_OPTS", "value": "-Xms512m -Xmx1g"}], "external_dependencies": [{"type": "service", "name": "aiknowledge-controller", "url": "http://aiknowledge-controller:8080"}, {"type": "service", "name": "rbac", "url": "http://rbac:8080"}, {"type": "middleware", "name": "elasticsearch", "url": "http://elasticsearch:9200"}], "test_commands": ["curl -s http://localhost:8083/actuator/health | grep UP"]}