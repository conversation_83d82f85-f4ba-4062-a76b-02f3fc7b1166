#!/bin/bash
# 部署根目录设置
echo "部署根目录设置"
DEPLOY_DIR="/data/postgresql"
echo "删除数据"
rm -rf ${DEPLOY_DIR}/data/*
echo "创建三级目录结构"
mkdir -p ${DEPLOY_DIR}/{data,logs,config} && chmod -R 777 ${DEPLOY_DIR}  # 创建三级目录结构:ml-citation{ref="3,7" data="citationList"}
echo "设置权限"
chmod -R 755 ${DEPLOY_DIR}
echo "获取最新镜像（DaoCloud 加速源）"
LATEST_TAG="12.18"  # 2025年4月官方最新稳定版本:ml-citation{ref="3,7" data="citationList"}
echo "删除容器"
docker rm -f postgres-server
# 启动容器
echo "启动容器"
docker run -itd  --name postgres-server \
 --hostname pg-node  -e POSTGRES_USER="admin"  \
 -e POSTGRES_PASSWORD="PgSQL@2025" \
 -e PGDATA=/var/lib/postgresql/data/pgdata \
 -v ${DEPLOY_DIR}/data:/var/lib/postgresql/data \
 -v ${DEPLOY_DIR}/logs:/var/log/postgresql \
 -v ${DEPLOY_DIR}/config:/etc/postgresql \
 -p 5432:5432  \
 m.daocloud.io/docker.io/postgres:${LATEST_TAG}  -c "log_destination=stderr"  -c "logging_collector=on"
echo "启动成功"
echo "查看容器状态"
docker ps | grep postgres-server
echo "查看日志"
docker logs -f postgres-server
echo "查看容器信息"
docker inspect postgres-server
echo "查看容器端口"
docker port postgres-server
echo "查看容器版本"
docker exec -it postgres-server psql -V
echo "查看容器配置"
docker exec -it postgres-server cat /etc/postgresql/postgresql.conf

# postgresql发送测试数据
echo "postgresql发送测试数据"
echo "docker exec -it postgres-server psql -U admin -d postgres -c \"INSERT INTO test (name, age) VALUES ('test', 18)\""
docker exec -it postgres-server psql -U admin -d postgres -c "INSERT INTO test (name, age) VALUES ('test', 18)"
echo "查看postgresql的索引"
echo "docker exec -it postgres-server psql -U admin -d postgres -c \"SELECT * FROM test\""
docker exec -it postgres-server psql -U admin -d postgres -c "SELECT * FROM test"
echo "删除postgresql的索引"
echo "docker exec -it postgres-server psql -U admin -d postgres -c \"DELETE FROM test\""
docker exec -it postgres-server psql -U admin -d postgres -c "DELETE FROM test"

