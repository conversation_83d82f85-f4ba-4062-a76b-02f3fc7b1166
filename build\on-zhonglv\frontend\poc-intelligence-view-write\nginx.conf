events {
    worker_connections  1000;
}

error_log  /var/log/nginx/error.log debug;

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    log_format  main  '$remote_addr:$remote_port - $remote_user [$time_local] '
                          '"$request_method $request_uri $server_protocol" '
                          '$status $body_bytes_sent '
                          '"$http_referer" "$http_user_agent" "$upstream_addr" ';#"$request_body"
    access_log  /var/log/nginx/access.log main;
 
   

    limit_conn_zone $server_name zone=auth_conn:20m;
    #limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
    limit_req_zone $server_name zone=auth_req:20m rate=1r/s; #确定每个请求发起后的冷却周期


    server {
        listen       80;
        server_name  localhost;

        # 开启gzip压缩
        gzip on;
        gzip_min_length 1k;
        gzip_comp_level 6;
        gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
        gzip_vary on;
        gzip_disable "MSIE [1-6]\.";

        # 静态资源缓存设置
        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
            
            # 缓存设置
            expires 7d;
        }

        location = /poc-intelligence-view-write {   
            return 301 /poc-intelligence-view-write/;
        }

        location ^~ /poc-intelligence-view-write/ {
            alias /usr/share/nginx/html/poc-intelligence-view-write/;
            index index.html;
            try_files $uri $uri/ /poc-intelligence-view-write/index.html;
        }

    }

}
