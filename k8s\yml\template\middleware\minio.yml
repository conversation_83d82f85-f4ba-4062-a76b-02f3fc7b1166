apiVersion: apps/v1
kind: Deployment
metadata:
  name: minio
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: minio
  template:
    metadata:
      labels:
        app: minio
    spec:
      containers:
      - name: minio
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/minio:RELEASE.2023-04-28T18-11-17Z
        args:
        - server
        - /data
        - --console-address
        - ":9001"
        ports:
        - containerPort: 9000
        - containerPort: 9001
        env:
        - name: MINIO_ROOT_USER
          value: "admin"
        - name: MINIO_ROOT_PASSWORD
          value: "MiniO@2025"
        volumeMounts:
        - name: minio-data
          mountPath: /data
        - name: minio-logs
          mountPath: /var/log/minio
      volumes:
      - name: minio-data
        persistentVolumeClaim:
          claimName: minio-data-pvc
      - name: minio-logs
        persistentVolumeClaim:
          claimName: minio-logs-pvc
      imagePullSecrets:
      - name: oa-llm-imagepullsecret    
---
apiVersion: v1
kind: Service
metadata:
  name: minio
  namespace: oa-llm
spec:
  ports:
  - port: 9000
    targetPort: 9000
    name: api
  - port: 9001
    targetPort: 9001
    name: console
  selector:
    app: minio
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: minio-data-pvc
  namespace: oa-llm
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: local-path
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: minio-logs-pvc
  namespace: oa-llm
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
  storageClassName: local-path 