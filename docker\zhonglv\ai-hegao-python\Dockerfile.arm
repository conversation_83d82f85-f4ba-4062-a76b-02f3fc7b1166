FROM m.daocloud.io/docker.io/arm64v8/python:3.10-slim

WORKDIR /app

# 安装网络工具和编译依赖
RUN apt-get update && \
    apt-get install -y curl telnet vim tree build-essential liblzma-dev \
    cmake libboost-all-dev zlib1g-dev libbz2-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 配置pip使用国内镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com

# 复制依赖文件并安装依赖
COPY requirements.txt .

# 修改 requirements.txt 中的 CUDA 相关依赖
RUN sed -i 's/nvidia-cublas-cu11.*//g' requirements.txt && \
    sed -i 's/torch.*//g' requirements.txt && \
    sed -i 's/torchvision.*//g' requirements.txt

# 安装 uv
RUN pip install uv

# 安装依赖
RUN uv pip install --index-url https://mirrors.aliyun.com/pypi/simple/ --prerelease=allow --system -r requirements.txt

# 复制项目文件
COPY app /app

# 确保日志目录存在
RUN mkdir -p app/log

# 复制并修正shell脚本权限
COPY start.sh /app/shells/start.sh
RUN chmod +x /app/shells/start.sh

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8080

# 设置启动命令
CMD ["/bin/bash", "/app/shells/start.sh"] 