apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-config
  namespace: oa-llm
data:
  kibana.yml: |
    server.name: kibana
    server.host: "0.0.0.0"
    elasticsearch.hosts: ["http://elasticsearch.oa-llm:9200"]
    elasticsearch.username: "elastic"
    elasticsearch.password: "elastic@123"
    i18n.locale: "zh-CN"
---
apiVersion: v1
kind: Service
metadata:
  name: kibana
  namespace: oa-llm
  labels:
    app: kibana
spec:
  ports:
  - port: 5601
    targetPort: 5601
    name: http
  type: ClusterIP
  selector:
    app: kibana
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: oa-llm
  labels:
    app: kibana
spec:
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
    spec:
      containers:
      - name: kibana
        image: {{kibana.image-url}}
        resources:
          limits:
            cpu: 2000m
            memory: 1Gi
          requests:
            cpu: 1000m
            memory: 512Mi
        ports:
        - containerPort: 5601
          name: http
        volumeMounts:
        - name: kibana-data
          mountPath: /usr/share/kibana/data
        - name: kibana-config
          mountPath: /usr/share/kibana/config/kibana.yml
          subPath: kibana.yml
      volumes:
      - name: kibana-data
        emptyDir: {}
      - name: kibana-config
        configMap:
          name: kibana-config
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
