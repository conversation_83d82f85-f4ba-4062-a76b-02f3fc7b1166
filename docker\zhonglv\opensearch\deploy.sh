#!/bin/bash
set -e

# 配置参数
OPENSEARCH_VERSION="2.9.0"  # 2023年10月最新稳定版
BASE_DIR="/data/opensearch"
NETWORK_NAME="opensearch-net"
IMAGE_URL="public.ecr.aws/opensearchproject/opensearch:${OPENSEARCH_VERSION}"


# 创建目录结构
mkdir -p ${BASE_DIR}/{config,data,logs}
chown -R 1000:1000 ${BASE_DIR}  # 确保容器用户权限

# 创建 Docker 私有网络
docker network inspect ${NETWORK_NAME} >/dev/null 2>&1 || \
docker network create ${NETWORK_NAME}

# 生成默认配置文件
docker run -d --name opensearch-temp ${IMAGE_URL}
docker cp opensearch-temp:/usr/share/opensearch/config/ ${BASE_DIR}/config/
docker rm -f opensearch-temp
#docker rm -f opensearch
# 启动 OpenSearch 容器
docker run -d --name opensearch \
  --network ${NETWORK_NAME} \
  -p 9200:9200 \
  -p 9600:9600 \
  -e "discovery.type=single-node" \
  -e "OPENSEARCH_JVM_HEAP=2g" \
  -e "OPENSEARCH_INITIAL_ADMIN_PASSWORD=QmffyH1zxSWE5Nke" \
  -v ${BASE_DIR}/data:/usr/share/opensearch/data \
  -v ${BASE_DIR}/logs:/usr/share/opensearch/logs \
  -v ${BASE_DIR}/config:/usr/share/opensearch/config \
  --ulimit memlock=-1:-1 \
  ${IMAGE_URL}

echo "OpenSearch 部署完成！"
echo "访问地址：https://localhost:9200 (用户 admin:QmffyH1zxSWE5Nke)"
echo "数据目录：${BASE_DIR}/data"
echo "日志目录：${BASE_DIR}/logs"
echo "配置文件：${BASE_DIR}/config"

# 用curl测试opensearch是否正常运行
echo "curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:9200/ | grep -q '"status":"success"' && echo "OpenSearch 运行正常" || echo "OpenSearch 运行异常""
curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:9200/ | grep -q '"status":"success"' && echo "OpenSearch 运行正常" || echo "OpenSearch 运行异常"

# 用curl测试opensearch创建测试索引test
echo "curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:9200/test | grep -q '"status":"success"' && echo "OpenSearch 创建索引成功" || echo "OpenSearch 创建索引失败""
curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:9200/test | grep -q '"status":"success"' && echo "OpenSearch 创建索引成功" || echo "OpenSearch 创建索引失败"

# opensearch发送测试数据
echo "curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:9200/test/_doc/1 -X POST -H 'Content-Type: application/json' -d '{\"name\":\"test\",\"age\":18}'"
curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:9200/test/_doc/1 -X POST -H 'Content-Type: application/json' -d '{\"name\":\"test\",\"age\":18}'

# 查看opensearch的索引的数据  
echo "curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:9200/test/_search?q=*&size=1"
curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:9200/test/_search?q=*&size=1

# 删除opensearch的索引及数据
echo "curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:9200/test/_doc/1 -X DELETE"
curl -s -u admin:QmffyH1zxSWE5Nke https://localhost:9200/test/_doc/1 -X DELETE





