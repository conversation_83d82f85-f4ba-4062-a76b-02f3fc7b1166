#!/bin/bash
IMAGE_REPO="officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn"
# 在本地构建镜像，并将镜像推送到私有仓库
#IMAGE_REPO="cis-hub-beijing-zs1.cmecloud.cn"
#IMAGE_REPO="10.1.4.121"
#NAMESPACE="jiutianwensi"
NAMESPACE="jtws"
#HARBOR_USERNAME="admin"
#HARBOR_PASSWORD="Harbor12345"
HARBOR_USERNAME="zglyjt"
HARBOR_PASSWORD="Znwd0415@"
APP_NAME="poc-intelligence-view-write"
echo "登录镜像仓库:docker login --username=$HARBOR_USERNAME -p$HARBOR_PASSWORD $IMAGE_REPO"
docker login --username=$HARBOR_USERNAME -p$HARBOR_PASSWORD $IMAGE_REPO
# 设置构建目录
BUILD_DIR="/data/build"
#获取当前的主机是arm64还是x86_64
ARCH=$(uname -m)
if [ "$ARCH" = "arm64" -o "$ARCH" = "aarch64" ]; then
    echo "当前主机是arm64架构"
    IMAGE_TAG="arm64-1.0.0"
else
    echo "当前主机是x86_64架构"
    IMAGE_TAG="x86_64-1.0.0"
fi
# 设置镜像名称和标签
IMAGE_NAME="$IMAGE_REPO/$NAMESPACE/$APP_NAME"
echo "镜像名称: $IMAGE_NAME:$IMAGE_TAG"

# 直接构建业务镜像并推送到私有仓库
# 判断镜像是否存在
if docker images | grep -q $IMAGE_NAME; then
    echo "镜像已存在，删除镜像:docker rmi -f $IMAGE_NAME:$IMAGE_TAG"
    docker rmi -f $IMAGE_NAME:$IMAGE_TAG
fi  
# 删除none的镜像
echo "删除none的镜像:docker rmi $(docker images | grep '<none>' | awk '{print $3}')"
docker rmi -f $(docker images | grep '<none>' | awk '{print $3}')

echo "构建 $APP_NAME 镜像..."
echo "docker   build -t ${IMAGE_NAME}:${IMAGE_TAG} ."
docker  build -t ${IMAGE_NAME}:${IMAGE_TAG}  --build-arg APP_NAME=$APP_NAME .
# 询问一下是否需要启动容器
read -p "是否需要启动容器？(y/n): " START_CONTAINER
if [ "$START_CONTAINER" = "y" ]; then
    echo "启动容器..."
    ./start.sh  ${APP_NAME} ${IMAGE_NAME} ${IMAGE_TAG}
fi
# 删除容器
echo "删除容器:docker rm -f ${APP_NAME}"
docker rm -f ${APP_NAME}

echo "推送到私有仓库..."
# 询问一下是否需要推送镜像  
read -p "是否需要推送镜像？(y/n): " PUSH_IMAGE
if [ "$PUSH_IMAGE" = "y" ]; then
    echo "推送镜像..."
    echo "docker push ${IMAGE_NAME}:${IMAGE_TAG}"
    docker push ${IMAGE_NAME}:${IMAGE_TAG}
fi
echo "全部构建完成！" 