#!/bin/bash

# 递归修复脚本文件行尾符号问题的脚本
# 将Windows格式的CRLF（\r\n）转换为Unix格式的LF（\n）

# 默认为当前目录
TARGET_DIR=${1:-.}

echo "开始修复文件格式问题..."
echo "目标目录: $TARGET_DIR"

# 安装dos2unix (如果需要)
install_dos2unix() {
    echo "尝试安装dos2unix..."
    if command -v apt-get > /dev/null 2>&1; then
        sudo apt-get update && sudo apt-get install -y dos2unix
    elif command -v yum > /dev/null 2>&1; then
        sudo yum install -y dos2unix
    elif command -v dnf > /dev/null 2>&1; then
        sudo dnf install -y dos2unix
    else
        echo "无法自动安装dos2unix，将使用sed命令代替"
        return 1
    fi
    return 0
}

# 计数器
FIXED_FILES=0
FIXED_WITH_DOS2UNIX=0
FIXED_WITH_SED=0

# 修复文件函数
fix_file() {
    local file=$1
    local method=$2
    
    if [ "$method" = "dos2unix" ]; then
        dos2unix "$file" > /dev/null 2>&1
        FIXED_WITH_DOS2UNIX=$((FIXED_WITH_DOS2UNIX + 1))
    else
        sed -i 's/\r$//' "$file"
        FIXED_WITH_SED=$((FIXED_WITH_SED + 1))
    fi
    
    # 如果是脚本文件，设置可执行权限
    if [[ "$file" == *.sh ]] || [[ "$file" == *.bash ]] || [[ "$file" == *.pl ]] || [[ "$file" == *.py ]]; then
        chmod +x "$file"
    fi
    
    FIXED_FILES=$((FIXED_FILES + 1))
    echo "已修复: $file"
}

# 检查文件是否包含\r字符
check_file() {
    local file=$1
    if file "$file" | grep -q "CRLF"; then
        return 0  # 文件包含CRLF
    elif grep -q $'\r' "$file" > /dev/null 2>&1; then
        return 0  # 文件包含\r
    else
        return 1  # 文件不需要修复
    fi
}

# 主处理逻辑
main() {
    local method="sed"
    
    # 检查是否安装了dos2unix
    if command -v dos2unix > /dev/null 2>&1; then
        echo "找到dos2unix，将优先使用"
        method="dos2unix"
    else
        echo "未找到dos2unix，尝试安装..."
        if install_dos2unix; then
            echo "dos2unix安装成功，将使用dos2unix"
            method="dos2unix"
        else
            echo "将使用sed命令处理文件"
        fi
    fi
    
    # 文件类型，以空格分隔
    FILE_TYPES="sh py rb pl php js json yaml yml xml conf md txt ini cfg"
    
    # 构建find命令的文件类型参数
    FIND_PARAMS=""
    for ext in $FILE_TYPES; do
        FIND_PARAMS="$FIND_PARAMS -o -name \"*.$ext\""
    done
    
    # 去掉第一个 -o
    FIND_PARAMS=$(echo "$FIND_PARAMS" | sed 's/^\ -o\ //')
    
    # 查找所有可能包含行尾问题的文件
    echo "查找文件中..."
    FILES_TO_CHECK=$(eval "find \"$TARGET_DIR\" -type f \( $FIND_PARAMS \) -not -path \"*/\.*\"")
    
    TOTAL_FILES=$(echo "$FILES_TO_CHECK" | wc -l)
    echo "找到 $TOTAL_FILES 个文件需要检查"
    
    # 处理每个文件
    for file in $FILES_TO_CHECK; do
        if check_file "$file"; then
            fix_file "$file" "$method"
        fi
    done
}

# 执行主函数
main

# 显示统计信息
echo "====== 处理完成 ======"
echo "检查的目录: $TARGET_DIR"
echo "总修复文件数: $FIXED_FILES"
if [ $FIXED_WITH_DOS2UNIX -gt 0 ]; then
    echo "使用dos2unix修复: $FIXED_WITH_DOS2UNIX"
fi
if [ $FIXED_WITH_SED -gt 0 ]; then
    echo "使用sed修复: $FIXED_WITH_SED"
fi
echo "文件格式修复完成" 