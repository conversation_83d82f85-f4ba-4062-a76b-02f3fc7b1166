apiVersion: v1
kind: Service
metadata:
  labels:
    cmcc-gitops-file-name: ai-writer-nostream.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-writer-nostream
  namespace: oa-llm
spec:
  ports:
  - port: 8080
    targetPort: 8080
  selector:
    app: ai-writer-nostream
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    cmcc-gitops-file-name: ai-writer-nostream.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-writer-nostream
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-writer-nostream
  serviceName: ai-writer-nostream
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: ai-writer-nostream
    spec:
      containers:
      - image: artifactory.dep.devops.cmit.cloud:20101/native_common/ai-writer-nostream-python:250518-1529
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        name: ai-writer-nostream
        ports:
        - containerPort: 8080
          protocol: TCP
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 20
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 10
        resources: {}
        volumeMounts:
        - mountPath: /app/temp/web_source_code/backend/application.properties
          name: ai-writer-nostream-config
          subPath: application.properties
        - mountPath: /app/shells
          name: ai-writer-nostream-shells
        - mountPath: /app/temp/web_source_code/log
          name: short-term-logs
      - args:
        - -c
        - /opt/filebeat/filebeat.yml
        - -e
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/filebeat:7.17.14
        imagePullPolicy: Always
        name: filebeat
        resources: {}
        terminationMessagePath: /var/log/err.log
        volumeMounts:
        - mountPath: /opt/filebeat/filebeat.yml
          name: ai-writer-nostream-filebeat-cm
          subPath: filebeat.yml
        - mountPath: /ai-writer-nostream/data/logs 
          name: short-term-logs
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: short-term-logs
      - configMap:
          defaultMode: 420
          name: ai-writer-nostream-config
        name: ai-writer-nostream-config
      - configMap:
          defaultMode: 420
          name: ai-writer-nostream-filebeat-cm
        name: ai-writer-nostream-filebeat-cm
      - configMap:
          defaultMode: 420
          name: ai-writer-nostream-shells
        name: ai-writer-nostream-shells
  updateStrategy: {}
status:
  replicas: 0
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-writer-nostream-config
  namespace: oa-llm
data:
  application.properties: |-
    # 大模型相关配置
    model.url={{model.url}}
    model.appid=tyrknosa
    model.appKey=5ab43270aecc9f482b79c965ab81d411
    model.capabilityname=semantic0000000000000000
    api-key={{api-key}}
    model-name={{model-name}}
    # 文件路径配置
    path.sensitiveWords=/app/temp/web_source_code/backend/sensitive_words_all.txt
    path.rulesFile=/app/temp/web_source_code/backend/re.json
    path.templatesDir=/app/temp/web_source_code/backend/writing_template
    # 生成参数配置
    generation.maxTokens=1024
    generation.temperature=0.2

    # 系统配置
    system.prompt=你是一个公文写作专家，公文内不要出现“我们”、“我”、“你们”等口语化词汇，也不需要带入主送单位   
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-writer-nostream-shells
  namespace: oa-llm
data:
  start.sh: |-
    #!/bin/bash
    echo "启动文章自动生成服务"
    echo "当前目录结构"
    ls -la /app
    echo "python /app/temp/web_source_code/manage.py runserver 0.0.0.0:8080"
    python /app/temp/web_source_code/manage.py runserver 0.0.0.0:8080
    echo "启动文章自动生成服务完成"
    sh /app/shells/test.sh
  test.sh: |-
    #!/bin/bash
    # 颜色设置
    GREEN='\033[0;32m'
    BLUE='\033[0;34m'
    RED='\033[0;31m'
    YELLOW='\033[1;33m'
    NC='\033[0m' # 无颜色

    # ====================== API测试命令 ======================
    echo -e "${BLUE}=== 文档生成API测试 ===${NC}"

    # 简单请求 - 只需text字段
    # 打印请求
    echo "curl -X POST \
      \"http://127.0.0.1:8080/mubanwriter/v1/service\" \
      -H \"Content-Type: application/json\" \
      -d '{
      "text": "请以《关于召开项目进度评审会议的通知》为题生成一篇通知类型的公文，子类型为会议通知。主送单位为：各部门负责人。内容包括会议时间：2024年5月15日上午10:00，会议地点：公司三楼会议室，参会人员：各部门负责人及项目组成员。会议议程：1.项目进度汇报；2.问题讨论；3.下一阶段计划制定。请各相关人员准时参加。字数要求1000字左右。"
    }'"

    # 执行请求
    curl -X POST \
      "http://127.0.0.1:8080/mubanwriter/v1/service" \
      -H "Content-Type: application/json" \
      -d '{
      "text": "请以《关于召开项目进度评审会议的通知》为题生成一篇通知类型的公文，子类型为会议通知。主送单位为：各部门负责人。内容包括会议时间：2024年5月15日上午10:00，会议地点：公司三楼会议室，参会人员：各部门负责人及项目组成员。会议议程：1.项目进度汇报；2.问题讨论；3.下一阶段计划制定。请各相关人员准时参加。字数要求1000字左右。"
    }'
    echo -e "${GREEN}=== 文档生成API测试结束 ===${NC}"  

    # 带extension的请求
    echo -e "${BLUE}=== 带extension的请求测试 ===${NC}"
    # 打印请求
    echo "curl -X POST \
      \"http://127.0.0.1:8080/mubanwriter/v1/service\" \
      -H \"Content-Type: application/json\" \
      -d '{
      "text": "请以《关于召开项目进度评审会议的通知》为题生成一篇通知类型的公文，子类型为会议通知。主送单位为：各部门负责人。内容包括会议时间：2024年5月15日上午10:00，会议地点：公司三楼会议室，参会人员：各部门负责人及项目组成员。会议议程：1.项目进度汇报；2.问题讨论；3.下一阶段计划制定。请各相关人员准时参加。字数要求1000字左右。",
      "extension": {
        "docInfo": {
          "sourceText": "关于召开项目进度评审会议的通知",
          "universalType": "通知",
          "subTypeName": "会议通知",
          "mainDeliveryUnit": "各部门负责人"
        },
        "fileContent": {
          "modelEssay": ["为确保项目按计划推进，解决项目过程中的问题，特召开本次项目进度评审会议。"]
        }
      }
      }'"

    # 执行请求
    curl -X POST \
      "http://127.0.0.1:8080/mubanwriter/v1/service" \
      -H "Content-Type: application/json" \
      -d '{
      "text": "请以《关于召开项目进度评审会议的通知》为题生成一篇通知类型的公文，子类型为会议通知。主送单位为：各部门负责人。内容包括会议时间：2024年5月15日上午10:00，会议地点：公司三楼会议室，参会人员：各部门负责人及项目组成员。会议议程：1.项目进度汇报；2.问题讨论；3.下一阶段计划制定。请各相关人员准时参加。字数要求1000字左右。",
      "extension": {
        "docInfo": {
          "sourceText": "关于召开项目进度评审会议的通知",
          "universalType": "通知",
          "subTypeName": "会议通知",
          "mainDeliveryUnit": "各部门负责人"
        },  
        "fileContent": {
          "modelEssay": ["为确保项目按计划推进，解决项目过程中的问题，特召开本次项目进度评审会议。"]
        }
      }
      }'
    echo -e "${GREEN}=== 带extension的请求测试结束 ===${NC}"
---
apiVersion: v1
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: log
      paths:
        - /ai-writer-nostream/data/logs/*.log
      fields:
        envTag: "oa-llm"
        appTag: "ai-writer-nostream"
        namespace: oa-llm
      multiline:
        pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'
        negate: true
        match: after
        max_lines: 500
    processors:
    - drop_fields:
        fields: ["agent","input","ecs"]
        ignore_missing: true
    output.elasticsearch:
      hosts: ["http://elasticsearch.oa-llm:9200"]
      index: "ai-writer-nostream-logs-%{+yyyy.MM.dd}"
    setup.template.name: "ai-writer-nostream-logs"
    setup.template.pattern: "ai-writer-nostream-logs*"
    setup.ilm.enabled: false  # 禁用 ILM，避免自动 rollover 到 filebeat-*
kind: ConfigMap
metadata:
  labels:
    cmcc-gitops-project-tag: oa-llm 
  name: ai-writer-nostream-filebeat-cm
  namespace: oa-llm