cat > /data/filebeat/config/filebeat-rag-qa-online.yml <<EOF
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /data/znwd/rag-qa-online/logs/*
  tags: ["rag-qa-online"]
  
  # 添加多行处理配置，正确处理rag-qa-online服务的特殊日志格式
  multiline:
    # 匹配以[开头的日期时间格式的行作为新日志的开始
    pattern: '^\[\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 1000
    timeout: 5s
  
  # 添加处理器以解析JSON格式
  processors:
    - decode_json_fields:
        fields: ["message"]
        process_array: false
        max_depth: 3
        target: ""
        overwrite_keys: true
        when:
          contains:
            message: "{"

output.elasticsearch:
  hosts: ["http://**********:9200"]
  username: "elastic"
  password: "Elastic20250417@#"
  index: "rag-qa-online-%{+yyyy}"

# 禁用索引模板，解决索引名称修改的问题
setup.template.enabled: false

# 或者也可以设置模板信息
# setup.template.name: "rag-qa-online"
# setup.template.pattern: "rag-qa-online-*"

# 禁用ILM
setup.ilm.enabled: false

logging.level: debug
EOF
echo "删除之前的容器"
docker rm -f filebeat-rag-qa-online
echo "docker run -d --name filebeat-rag-qa-online --user=root --volume="/data/filebeat/config/filebeat-rag-qa-online.yml:/usr/share/filebeat/filebeat.yml:ro" --volume="/data/znwd/rag-qa-online/logs:/data/znwd/rag-qa-online/logs:ro" docker.elastic.co/beats/filebeat:7.17.14 filebeat -e --strict.perms=false"
docker run -d --name filebeat-rag-qa-online \
  --user=root \
  --volume="/data/filebeat/config/filebeat-rag-qa-online.yml:/usr/share/filebeat/filebeat.yml:ro" \
  --volume="/data/znwd/rag-qa-online/logs:/data/znwd/rag-qa-online/logs:ro" \
  docker.elastic.co/beats/filebeat:7.17.14 \
  filebeat -e --strict.perms=false

ES_USERNAME="elastic"
ES_PASSWORD="Elastic20250417@#"
current_year=$(date +%Y)
echo "等待30秒，让Filebeat有足够时间处理日志..."
sleep 30

echo "验证索引"
echo "查看所有索引："
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/_cat/indices/rag-qa-online*?v"

echo "rag-qa-online索引:curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET \"http://**********:9200/rag-qa-online-$current_year/_search?q=message:*&size=1\""
curl -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://**********:9200/rag-qa-online-$current_year/_search?q=message:*&size=1"