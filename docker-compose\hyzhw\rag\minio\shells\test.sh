# bin/bash
echo "测试minio是否正常运行"
# 测试minio是否正常运行
echo "docker exec -it minio-server mc alias set myminio http://localhost:9000 admin MiniO@2025"
docker exec -it minio-server mc alias set myminio http://localhost:9000 admin MiniO@2025

# 用curl测试minio是否正常运行
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常""
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常"

# 用curl测试minio是否正常运行
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常""
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常"

# 用curl创建一个测试桶
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test | grep -q '"status":"success"' && echo "MinIO 创建桶成功" || echo "MinIO 创建桶失败""
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test | grep -q '"status":"success"' && echo "MinIO 创建桶成功" || echo "MinIO 创建桶失败"

# 用curl测试minio是否正常运行
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常""
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/health/ready | grep -q '"status":"success"' && echo "MinIO 运行正常" || echo "MinIO 运行异常"

# 上传一个测试文件
# 创建测试文件
echo "创建测试文件"
echo "测试内容" > test.txt

# 上传文件
echo "上传文件"
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X PUT -T test.txt"
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X PUT -T test.txt

# 下载文件
echo "下载文件"
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X GET -o test_downloaded.txt"
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X GET -o test_downloaded.txt

# 删除文件
echo "删除文件"
echo "curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X DELETE"
curl -s -u admin:MiniO@2025 http://localhost:9000/minio/v2/buckets/test/objects/test.txt -X DELETE

