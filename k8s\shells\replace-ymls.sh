#bin/bash
# 获取当前目录
CURRENT_DIR=$(pwd)
# 获取父目录
PARENT_DIR=$(dirname ${CURRENT_DIR})
# 读取config.json文件
CONFIG_FILE="${CURRENT_DIR}/config.json"
if [ ! -f ${CONFIG_FILE} ]; then
    echo "${CONFIG_FILE}文件不存在"
    exit 1
fi
TEMPLATE_YML_DIR="${PARENT_DIR}/yml/template"
if [ ! -d ${TEMPLATE_YML_DIR} ]; then
    echo "${TEMPLATE_YML_DIR}目录不存在"
    exit 1
fi
# 将TEMPLATE_YML_DIR下的所有yml文件复制到YML_DIR下
YML_DIR="${PARENT_DIR}/yml/work"
if [ ! -d ${YML_DIR} ]; then
    echo "${YML_DIR}目录不存在"
    mkdir -p ${YML_DIR}
fi
# 将TEMPLATE_YML_DIR下的所有yml文件复制到YML_DIR下
cp -r ${TEMPLATE_YML_DIR}/* ${YML_DIR}
ls ${YML_DIR}

# 获取filebeat的image-url
FILEBEAT_TARGET_IMAGE_URL=$(jq -r '.["filebeat-config"]["target-images-url"]' ${CONFIG_FILE})
# 转义特殊字符，以便在sed中使用
FILEBEAT_TARGET_IMAGE_URL_ESCAPED=$(echo "${FILEBEAT_TARGET_IMAGE_URL}" | sed 's/[\/&]/\\&/g')
# 获取storage-class
STORAGE_CLASS=$(jq -r '.["storage-config"]["storage-class"]' ${CONFIG_FILE})
# 遍历replace-config中的每一个节点
REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})
# 获取model相关参数
MODEL_URL=$(jq -r '.["model-config"]["url"]' ${CONFIG_FILE})
MODEL_API_KEY=$(jq -r '.["model-config"]["api-key"]' ${CONFIG_FILE})
MODEL_NAME=$(jq -r '.["model-config"]["model-name"]' ${CONFIG_FILE})

for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
    # 获取source-images-url
    SOURCE_IMAGES_URL=$(jq -r ".[\"replace-config\"][$i][\"source-images-url\"]" ${CONFIG_FILE})
    # 获取target-images-url
    TARGET_IMAGES_URL=$(jq -r ".[\"replace-config\"][$i][\"target-images-url\"]" ${CONFIG_FILE})
    # 转义特殊字符，以便在sed中使用
    TARGET_IMAGES_URL_ESCAPED=$(echo "${TARGET_IMAGES_URL}" | sed 's/[\/&]/\\&/g')
    # 获取yml-file-name
    YML_FILE_NAME=$(jq -r ".[\"replace-config\"][$i][\"yml-file-name\"]" ${CONFIG_FILE})
    # 获取description
    DESCRIPTION=$(jq -r ".[\"replace-config\"][$i][\"description\"]" ${CONFIG_FILE})
    # 获取type
    TYPE=$(jq -r ".[\"replace-config\"][$i][\"type\"]" ${CONFIG_FILE})
    # 获取service-name
    SERVICE_NAME=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
    # 获取YML的绝对路径
    YML_FILE_PATH="${YML_DIR}/${TYPE}/${YML_FILE_NAME}"
    if [ ! -f ${YML_FILE_PATH} ]; then
        echo "${YML_FILE_PATH}文件不存在"
        continue
    fi
    
    echo "处理文件: ${YML_FILE_PATH}"
    echo "替换 {{${SERVICE_NAME}.image-url}} -> ${TARGET_IMAGES_URL}"
    
    # 读取YML文件
    YML_CONTENT=$(cat ${YML_FILE_PATH})
    
    # 使用双引号分隔符避免sed中的特殊字符问题
    sed -i "s#{{${SERVICE_NAME}.image-url}}#${TARGET_IMAGES_URL}#g" ${YML_FILE_PATH}
    
    # 替换YML文件中的{{filebeat.image-url}}
    echo "替换 {{filebeat.image-url}} -> ${FILEBEAT_TARGET_IMAGE_URL}"
    sed -i "s#{{filebeat.image-url}}#${FILEBEAT_TARGET_IMAGE_URL}#g" ${YML_FILE_PATH}
    #如果存在{{storage-class-name}}，则替换
    if [[ ${YML_CONTENT} =~ "{{storage-class-name}}" ]]; then
        echo "替换 {{storage-class-name}} -> ${STORAGE_CLASS}"
        sed -i "s#{{storage-class-name}}#${STORAGE_CLASS}#g" ${YML_FILE_PATH}
    fi
    # 如果存在{{server}}和{{path}}，则替换
    if [[ ${YML_CONTENT} =~ "{{server}}" && ${YML_CONTENT} =~ "{{path}}" ]]; then

        # 获取storage-config中的server和path
        SERVER=$(jq -r '.["storage-config"]["server"]' ${CONFIG_FILE})
        NFS_PATH=$(jq -r '.["storage-config"]["path"]' ${CONFIG_FILE})
         # 添加调试信息
        echo "YML_DIR: ${YML_DIR}"
        echo "SERVER: ${SERVER}"
        echo "NFS_PATH: ${NFS_PATH}"
        echo "替换 {{server}} -> ${SERVER}"
        echo "替换 {{path}} -> ${NFS_PATH}"
        sed -i "s#{{server}}#${SERVER}#g" ${YML_FILE_PATH}
        sed -i "s#{{path}}#${NFS_PATH}#g" ${YML_FILE_PATH}
    fi
    # 如果存在{{model.url}}，则替换
    if [[ ${YML_CONTENT} =~ "{{model.url}}" ]]; then
        echo "替换 {{model.url}} -> ${MODEL_URL}"
        sed -i "s#{{model.url}}#${MODEL_URL}#g" ${YML_FILE_PATH}
    fi
    # 如果存在{{model.api-key}}，则替换
    if [[ ${YML_CONTENT} =~ "{{model.api-key}}" ]]; then
        echo "替换 {{model.api-key}} -> ${MODEL_API_KEY}"
        sed -i "s#{{model.api-key}}#${MODEL_API_KEY}#g" ${YML_FILE_PATH}
    fi
    # 如果存在{{model.model-name}}，则替换
    if [[ ${YML_CONTENT} =~ "{{model.model-name}}" ]]; then
        echo "替换 {{model.model-name}} -> ${MODEL_NAME}"
        sed -i "s#{{model.model-name}}#${MODEL_NAME}#g" ${YML_FILE_PATH}
    fi

    echo "替换storage-config中的所有{{字段}}"




    echo "文件替换完成: ${YML_FILE_PATH}"
done

# 替换storage-config中的所有{{字段}}
STORAGE_CLASS=$(jq -r '.["storage-config"]["storage-class"]' ${CONFIG_FILE})
SERVER=$(jq -r '.["storage-config"]["server"]' ${CONFIG_FILE})
NFS_PATH=$(jq -r '.["storage-config"]["path"]' ${CONFIG_FILE})

# 添加调试信息
echo "YML_DIR: ${YML_DIR}"
echo "SERVER: ${SERVER}"
echo "NFS_PATH: ${NFS_PATH}"

# 替换storage-config中的所有{{字段}}
STORAGE_CLASS=${STORAGE_CLASS//\{\{server\}\}/$SERVER}
STORAGE_CLASS=${STORAGE_CLASS//\{\{path\}\}/$NFS_PATH}
NFS_STORAGE_CLASS_FILE="${YML_DIR}/storage/nfs-storage-class.yml"

# 添加调试信息
echo "NFS_STORAGE_CLASS_FILE: ${NFS_STORAGE_CLASS_FILE}"
ls -l ${NFS_STORAGE_CLASS_FILE}

echo "替换nfs-storage-class_file[${NFS_STORAGE_CLASS_FILE}]中的所有{{字段}}"
# 替换nfs-storage-class_file中的所有{{字段}}
sed -i "s#{{server}}#${SERVER}#g" ${NFS_STORAGE_CLASS_FILE}
sed -i "s#{{path}}#${NFS_PATH}#g" ${NFS_STORAGE_CLASS_FILE}

echo "所有YML文件处理完成"