apiVersion: v1
kind: Service
metadata:
  labels:
    cmcc-gitops-file-name: ai-writer-nostream.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-writer-nostream
  namespace: oa-llm
spec:
  ports:
  - port: 8080
    targetPort: 8080
  selector:
    app: ai-writer-nostream
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    cmcc-gitops-file-name: ai-writer-nostream.yaml
    cmcc-gitops-project-tag: oallm
  name: ai-writer-nostream
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-writer-nostream
  serviceName: ai-writer-nostream
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: ai-writer-nostream
    spec:
      containers:
      - image: {{ai-writer-nostream.image-url}}
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 5
        name: ai-writer-nostream
        ports:
        - containerPort: 8080
          protocol: TCP
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 20
          periodSeconds: 5
          tcpSocket:
            port: 8080
          timeoutSeconds: 10
        resources: {}
        volumeMounts:
        - mountPath: /app/temp/web_source_code/backend/application.properties
          name: ai-writer-nostream-config
          subPath: application.properties
        - mountPath: /app/shells
          name: ai-writer-nostream-shells
        - mountPath: /app/temp/web_source_code/log
          name: short-term-logs
      - args:
        - -c
        - /opt/filebeat/filebeat.yml
        - -e
        image: {{filebeat.image-url}}
        imagePullPolicy: Always
        name: filebeat
        resources: {}
        terminationMessagePath: /var/log/err.log
        volumeMounts:
        - mountPath: /opt/filebeat/filebeat.yml
          name: ai-writer-nostream-filebeat-cm
          subPath: filebeat.yml
        - mountPath: /ai-writer-nostream/data/logs 
          name: short-term-logs
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: short-term-logs
      - configMap:
          defaultMode: 420
          name: ai-writer-nostream-config
        name: ai-writer-nostream-config
      - configMap:
          defaultMode: 420
          name: ai-writer-nostream-filebeat-cm
        name: ai-writer-nostream-filebeat-cm
      - configMap:
          defaultMode: 420
          name: ai-writer-nostream-shells
        name: ai-writer-nostream-shells
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
  updateStrategy: {}
status:
  replicas: 0
  availableReplicas: 0
  currentReplicas: 0
  updatedReplicas: 0
  currentRevision: ""
  updateRevision: ""
  collisionCount: 0
  conditions: []
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-writer-nostream-config
  namespace: oa-llm
data:
  application.properties: |-
    # 大模型相关配置
    model.url={{model.url}}
    model.appid=tyrknosa
    model.appKey=5ab43270aecc9f482b79c965ab81d411
    model.capabilityname=semantic0000000000000000
    api-key={{model.api-key}}
    model-name={{model.model-name}}
    # 文件路径配置
    path.sensitiveWords=/app/temp/web_source_code/backend/sensitive_words_all.txt
    path.rulesFile=/app/temp/web_source_code/backend/re.json
    path.templatesDir=/app/temp/web_source_code/backend/writing_template
    # 生成参数配置
    generation.maxTokens=1024
    generation.temperature=0.2

    # 系统配置
    system.prompt=你是一个公文写作专家，公文内不要出现"我们"、"我"、"你们"等口语化词汇，也不需要带入主送单位   
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-writer-nostream-shells
  namespace: oa-llm
data:
  start.sh: |-
    #!/bin/bash
    echo "启动文章自动生成服务"
    echo "当前目录结构"
    ls -la /app
    echo "python /app/temp/web_source_code/manage.py runserver 0.0.0.0:8080"
    python /app/temp/web_source_code/manage.py runserver 0.0.0.0:8080
    echo "启动文章自动生成服务完成"
    sh /app/shells/test.sh
  test.sh: |-
    #!/bin/bash
    # 颜色设置
    GREEN='\033[0;32m'
    BLUE='\033[0;34m'
    RED='\033[0;31m'
    YELLOW='\033[1;33m'
    NC='\033[0m' # 无颜色
    
    # 读取配置文件
    config_file="/app/temp/web_source_code/backend/application.properties"
    model_url=$(grep "model.url=" $config_file | cut -d'=' -f2)
    model_name=$(grep "model-name=" $config_file | cut -d'=' -f2)
    api_key=$(grep "api-key=" $config_file | cut -d'=' -f2)

    # 测试文本
    test_text="请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求1800字以上。"

    # ====================== 大模型接口测试 ======================
    echo -e "${BLUE}=== 大模型接口测试 ===${NC}"
    echo "model_url: $model_url"
    echo "model_name: $model_name"
    echo "api_key: $api_key"

    # 构建大模型请求命令
    cmd="curl -v -X POST \"${model_url}\" -H \"Content-Type: application/json\" -H \"Authorization: Bearer ${api_key}\" -d '{\"model\": \"${model_name}\", \"messages\": [{\"role\": \"system\", \"content\": \"你是一个公文写作专家，公文内不要出现\\\"我们\\\"、\\\"我\\\"、\\\"你们\\\"等口语化词汇，也不需要带入主送单位\"}, {\"role\": \"user\", \"content\": \"${test_text}\"}]}'"

    # 打印并执行命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}大模型请求耗时: $((end_time - start_time)) 秒${NC}"

    # ====================== 简单请求测试 ======================
    echo -e "${BLUE}=== 简单请求测试 ===${NC}"
    cmd="curl -v -X POST \"http://127.0.0.1:8080/mubanwriter/v1/service\" -H \"Content-Type: application/json\" -d '{\"text\": \"${test_text}\"}'"

    # 打印并执行命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}简单请求耗时: $((end_time - start_time)) 秒${NC}"

    # ====================== 带extension的请求测试 ======================
    echo -e "${BLUE}=== 带extension的请求测试 ===${NC}"
    cmd="curl -v -X POST \"http://127.0.0.1:8080/mubanwriter/v1/service\" -H \"Content-Type: application/json\" -d '{\"text\": \"${test_text}\", \"extension\": {\"docInfo\": {\"sourceText\": \"关于召开项目进度评审会议的通知\", \"universalType\": \"通知\", \"subTypeName\": \"会议通知\", \"mainDeliveryUnit\": \"各部门负责人\"}, \"fileContent\": {\"modelEssay\": [\"为确保项目按计划推进，解决项目过程中的问题，特召开本次项目进度评审会议。\"]}}}'"

    # 打印并执行命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo -e "${GREEN}带extension的请求耗时: $((end_time - start_time)) 秒${NC}"
    echo
    echo -e "${GREEN}=== 所有测试完成 ===${NC}"
---
apiVersion: v1
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: log
      paths:
        - /ai-writer-nostream/data/logs/*.log
      fields:
        envTag: "oa-llm"
        appTag: "ai-writer-nostream"
        namespace: oa-llm
      multiline:
        pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'
        negate: true
        match: after
        max_lines: 500
    processors:
    - drop_fields:
        fields: ["agent","input","ecs"]
        ignore_missing: true
    output.elasticsearch:
      hosts: ["http://elasticsearch.oa-llm:9200"]
      index: "ai-writer-nostream-logs-%{+yyyy.MM.dd}"
    setup.template.name: "ai-writer-nostream-logs"
    setup.template.pattern: "ai-writer-nostream-logs*"
    setup.ilm.enabled: false  # 禁用 ILM，避免自动 rollover 到 filebeat-*
kind: ConfigMap
metadata:
  labels:
    cmcc-gitops-project-tag: oa-llm 
  name: ai-writer-nostream-filebeat-cm
  namespace: oa-llm