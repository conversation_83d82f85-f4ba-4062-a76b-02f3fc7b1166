filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /data/ai-doc-poc/logs/*.log
    - /data/ai-doc-poc/logs/*.log.*
    - /data/ai-doc-poc/logs/ai-doc/*.log
    - /data/ai-doc-poc/logs/ai-doc/*.log.*
  fields:
    type: ai-doc-poc
  multiline:
    pattern: '^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/article-auto-compose-server/logs/*.log
    - /data/article-auto-compose-server/logs/*.log.*
  fields:
    type: ai-compose-poc
  multiline:
    pattern: '^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/ai-writer-nostream/logs/*.log
    - /data/ai-writer-nostream/logs/*.log.*
  fields:
    type: ai-writer-nostream
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/ai-writer-stream/logs/*.log
    - /data/ai-writer-stream/logs/*.log.*
  fields:
    type: ai-writer-stream
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 5m
  scan_frequency: 5s

- type: log
  enabled: true
  paths:
    - /data/ai-redactor/logs/*.log
    - /data/ai-redactor/logs/*.log.*
  fields:
    type: ai-redactor
  multiline:
    pattern: '^\[\d{4}-\d{2}-\d{2}|^\d{4}-\d{2}-\d{2}'
    negate: true
    match: after
    max_lines: 500
  ignore_older: 0
  close_inactive: 2m
  scan_frequency: 5s
  harvester_limit: 5
- type: log
  enabled: true
  paths:
    - /data/poc-intelligence-view-write/logs/*.log
    - /data/poc-intelligence-view-write/logs/*.log.*
  fields:
    type: poc-intelligence-view-write
  ignore_older: 0
  close_inactive: 2m
  scan_frequency: 5s
  harvester_limit: 5
- type: log
  enabled: true
  paths:
    - /data/poc-intelligence-view/logs/*.log
    - /data/poc-intelligence-view/logs/*.log.*
  fields:  
    type: poc-intelligence-view
  ignore_older: 0
  close_inactive: 2m
  scan_frequency: 5s
  harvester_limit: 5
# 使用Elasticsearch输出
output.elasticsearch:
  hosts: ["http://elasticsearch:9200"]
  username: "elastic"
  password: "elastic@123"
  indices:
    - index: "ai-doc-poc-%{+yyyy}"
      when.contains:
        fields.type: "ai-doc-poc"
    - index: "ai-compose-poc-%{+yyyy}"
      when.contains:
        fields.type: "ai-compose-poc"
    - index: "ai-writer-nostream-%{+yyyy}"
      when.contains:
        fields.type: "ai-writer-nostream"
    - index: "ai-writer-stream-%{+yyyy}"
      when.contains:
        fields.type: "ai-writer-stream"
    - index: "ai-redactor-%{+yyyy}"
      when.contains:
        fields.type: "ai-redactor"
    - index: "poc-intelligence-view-write-%{+yyyy}"
      when.contains:
        fields.type: "poc-intelligence-view-write"
    - index: "poc-intelligence-view-%{+yyyy}"
      when.contains:
        fields.type: "poc-intelligence-view"
  # 增加重试和错误处理
  bulk_max_size: 50
  worker: 1
  retry:
    max_retries: 3
    backoff:
      init: 1s
      max: 60s
      factor: 2.0

# 禁用索引模板
setup.template.enabled: false

# 禁用自动索引生命周期管理
setup.ilm.enabled: false

# Kibana配置
setup.kibana.host: "http://kibana:5601"

# 日志配置
logging.level: debug
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644