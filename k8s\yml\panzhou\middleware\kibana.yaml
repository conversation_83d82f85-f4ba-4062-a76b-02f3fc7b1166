apiVersion: v1
kind: Service
metadata:
  name: kibana
  namespace: oa-llm
  labels:
    app: kibana
spec:
  ports:
  - port: 5601
  type: ClusterIP
  selector:
    app: kibana
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: oa-llm
  labels:
    app: kibana
spec:
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
    spec:
      containers:
      - name: kibana
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/kibana:7.17.14
        resources:
          limits:
            cpu: 2000m
          requests:
            cpu: 1000m
        env:
        - name: ELASTICSEARCH_HOSTS
          value: http://elasticsearch.oa-llm:9200
        ports:
        - containerPort: 5601
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
