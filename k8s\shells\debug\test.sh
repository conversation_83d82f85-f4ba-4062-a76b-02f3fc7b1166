kubectl get pod poc-intelligence-view-debug-pod -n oa-llm
kubectl get pods -n oa-llm

# 进入ai-doc-poc-0的容器
kubectl exec -it ai-doc-poc-0 -n oa-llm -- /bin/bash

# 进入ai-doc-poc-0的容器
kubectl exec -it ai-doc-poc-0 -n oa-llm -- /bin/bash

# 查看容器的ip
kubectl get pod ai-doc-poc-0 -n oa-llm -o jsonpath='{.status.podIP}'

./debug_pod.sh poc-intelligence-view-0 oa-llm

# 将kibana服务代理到本机20003端口
kubectl port-forward svc/kibana 20002:5601 -n oa-llm --address=0.0.0.0
# 访问kibana服务
curl http://127.0.0.1:20002/

# 查看kibana服务的日志
kubectl logs -f kibana-54598c5d4f-9k6qc -n oa-llm

# 代理elasticsearch服务
kubectl port-forward svc/elasticsearch 20001:9200 -n oa-llm --address=0.0.0.0
# 访问elasticsearch服务
curl http://127.0.0.1:20001/

# 进入nginx-0的容器
kubectl exec -it nginx-0 -n oa-llm -- /bin/bash

# 进入nginx-0的容器
kubectl exec -it nginx-0 -n oa-llm -- /bin/bash

curl http://************:11434/api/generate -d '{
  "model": "deepseek-r1:70b",
  "prompt": "请介绍一下你自己",
  "stream": false
}'

curl http://************:11434/v1/chat/completions -d '{
  "model": "deepseek-r1:70b",
  "prompt": "请介绍一下你自己",
  "stream": false
}'

curl http://************:11434/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:70b",
    "messages": [
      {"role": "system", "content": "你是一个有帮助的AI助手"},
      {"role": "user", "content": "请介绍一下你自己"}
    ],
    "temperature": 0.7
  }'

# 删除所有服务
kubectl delete svc --all -n oa-llm

# 删除所有pod
kubectl delete pod --all -n oa-llm

# 删除所有deployment
kubectl delete deployment --all -n oa-llm --force

# 删除所有statefulset
kubectl delete statefulset --all -n oa-llm --force

# 删除所有configmap
kubectl delete configmap --all -n oa-llm

# 删除所有secret
kubectl delete secret --all -n oa-llm --force

# 删除所有pv
kubectl delete pv --all

# 删除所有pvc
kubectl delete pvc --all -n oa-llm

# 删除所有job
kubectl delete job --all -n oa-llm

kubectl apply -f /mnt/e/workspaces/oa-llm/oa-llm-ops/k8s/yml/work/storage/nfs-storage-class.yml
kubectl apply -f e:/workspaces/oa-llm/oa-llm-ops/k8s/yml/work/middleware/elasticsearch-nostorage.yml

docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0
docker tag officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0 10.7.202.240:8080/native_common/poc-intelligence-view-write:arm64-1.0.0
docker push 10.7.202.240:8080/native_common/poc-intelligence-view-write:arm64-1.0.0


docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
docker tag officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0 10.7.202.240:8080/native_common/poc-intelligence-view:arm64-1.0.0
docker push 10.7.202.240:8080/native_common/poc-intelligence-view:arm64-1.0.0

curl http://10.7.202.253:8000/v1/chat/completions \
-H "Content-Type: application/json" \
-d '{ 
  "model": "/opt/vllm_models/Qwen/Qwen2.5-72B-Instruct",
  "messages": [{"role": "user", "content": "请以《关于召开2024年度信息化建设推进会议的通知》为题生成一篇通知类型的公文，子类型为会议（活动）类通知。主送单位为：请输入主送单位。其他：需要补充的内容。会议目的为：为进一步推进公司信息化建设工作，提升管理效率,会议名称为：2024年度信息化建设推进会议,会议时间为：2024年9月6日（周五） 上午9:15,会场信息为：主会场设在公司总部三楼会议室，分会场设在各分公司视频会议室。,参会人员为：（一）主会场参会人员  1. 集团公司领导  2. 信息化办公室负责人  3. 各部门信息化专员及相关人员  （二）分会场参会人员  1. 各分公司负责人  2. 各分公司信息化专员及相关人员,会议议程为：（一）通报2024年上半年信息化建设进展情况  （二）部署下半年重点信息化项目  （三）交流信息化建设经验  （四）答疑与讨论,参会要求为:（一）请各单位高度重视，安排相关人员准时参会。  （二）参会人员须遵守会议纪律，保持会场安静，手机调至静音。  （三）会议期间请勿随意走动，不得录音录像。  （四）请着正装出席会议。  （五）如有特殊情况不能参会，请提前报备。,联系方式为：联系人：王伟  电话：010-12345678  邮箱：<EMAIL>。字数要求```1800```字以上。"}]
}'


















