#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# 创建结果数组
declare -A results

# 读取test.txt文件内容
TEXT=$(cat test.txt)
PROPMT_TEXT=$(cat prompt.txt)
# 将prompt.txt中放到TEXT内容的前面
TEXT="$PROPMT_TEXT\n$TEXT"

# 打印表头
echo -e "\n${YELLOW}大模型性能测试报告${NC}"
echo -e "${BLUE}===============================================${NC}"
echo -e "${BLUE}模型名称\t\t\t执行时间\t响应状态${NC}"
echo -e "${BLUE}-----------------------------------------------${NC}"

# 测试 deepseek-r1:70b
echo -e "\n${GREEN}测试 deepseek-r1:70b${NC}"
START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST http://10.7.202.252:11434/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d "$(jq -n \
    --arg text "$TEXT" \
    '{
      "model": "deepseek-r1:70b",
      "messages": [
        {"role": "user", "content": $text}
      ],
      "temperature": 0.7
    }')")
END_TIME=$(date +%s)
EXEC_TIME=$((END_TIME - START_TIME))
results["deepseek-r1:70b_time"]=$EXEC_TIME

# 检查响应状态
if echo "$RESPONSE" | grep -q "error"; then
    STATUS="${RED}失败${NC}"
else
    STATUS="${GREEN}成功${NC}"
fi
results["deepseek-r1:70b_status"]=$STATUS

echo -e "deepseek-r1:70b\t\t${EXEC_TIME}s\t\t${STATUS}"

# 测试 QwQ-32B
echo -e "\n${GREEN}测试 QwQ-32B${NC}"
START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST http://10.7.202.252:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d "$(jq -n \
    --arg text "$TEXT" \
    '{
      "model": "/opt/vllm_models/Qwen/QwQ-32B",
      "messages": [
        {"role": "user", "content": $text}
      ],
      "temperature": 0.7
    }')")
END_TIME=$(date +%s)
EXEC_TIME=$((END_TIME - START_TIME))
results["QwQ-32B_time"]=$EXEC_TIME

# 检查响应状态
if echo "$RESPONSE" | grep -q "error"; then
    STATUS="${RED}失败${NC}"
else
    STATUS="${GREEN}成功${NC}"
fi
results["QwQ-32B_status"]=$STATUS

echo -e "QwQ-32B\t\t\t${EXEC_TIME}s\t\t${STATUS}"

# 测试 Qwen2.5-72B-Instruct
echo -e "\n${GREEN}测试 Qwen2.5-72B-Instruct${NC}"

START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST   http://10.7.202.253:8000/v1/chat/completions  \
  -H "Content-Type: application/json" \
  -d "$(jq -n \
    --arg text "$TEXT" \
    '{
      "model": "/opt/vllm_models/Qwen/Qwen2.5-72B-Instruct",
      "messages": [
        {"role": "user", "content": $text}
      ],
      "temperature": 0.7
    }')")
END_TIME=$(date +%s)
EXEC_TIME=$((END_TIME - START_TIME))
results["Qwen2.5-72B-Instruct_time"]=$EXEC_TIME

# 检查响应状态
if echo "$RESPONSE" | grep -q "error"; then
    STATUS="${RED}失败${NC}"
else
    STATUS="${GREEN}成功${NC}"
fi
results["Qwen2.5-72B-Instruct_status"]=$STATUS

echo -e "Qwen2.5-72B-Instruct\t${EXEC_TIME}s\t\t${STATUS}"

# 打印统计报告
echo -e "\n${YELLOW}性能统计报告${NC}"
echo -e "${BLUE}===============================================${NC}"

# 计算平均响应时间
total_time=0
count=0
for model in "deepseek-r1:70b" "QwQ-32B" "Qwen2.5-72B-Instruct"; do
    if [ "${results[${model}_status]}" = "${GREEN}成功${NC}" ]; then
        total_time=$((total_time + results[${model}_time]))
        count=$((count + 1))
    fi
done

if [ $count -gt 0 ]; then
    avg_time=$((total_time / count))
    echo -e "平均响应时间: ${avg_time}秒"
else
    echo -e "平均响应时间: N/A"
fi

# 找出最快和最慢的模型
fastest_time=999999
fastest_model=""
slowest_time=0
slowest_model=""

for model in "deepseek-r1:70b" "QwQ-32B" "Qwen2.5-72B-Instruct"; do
    if [ "${results[${model}_status]}" = "${GREEN}成功${NC}" ]; then
        if [ ${results[${model}_time]} -lt $fastest_time ]; then
            fastest_time=${results[${model}_time]}
            fastest_model=$model
        fi
        if [ ${results[${model}_time]} -gt $slowest_time ]; then
            slowest_time=${results[${model}_time]}
            slowest_model=$model
        fi
    fi
done

if [ ! -z "$fastest_model" ]; then
    echo -e "最快响应模型: ${fastest_model} (${fastest_time}秒)"
    echo -e "最慢响应模型: ${slowest_model} (${slowest_time}秒)"
else
    echo -e "无法计算最快/最慢模型（所有模型都失败）"
fi

# 统计成功/失败数量
success_count=0
fail_count=0
for model in "deepseek-r1:70b" "QwQ-32B" "Qwen2.5-72B-Instruct"; do
    if [ "${results[${model}_status]}" = "${GREEN}成功${NC}" ]; then
        success_count=$((success_count + 1))
    else
        fail_count=$((fail_count + 1))
    fi
done

echo -e "\n${YELLOW}测试结果统计${NC}"
echo -e "${BLUE}===============================================${NC}"
echo -e "成功数量: ${GREEN}${success_count}${NC}"
echo -e "失败数量: ${RED}${fail_count}${NC}"
echo -e "成功率: $((success_count * 100 / (success_count + fail_count)))%"

