#!/bin/bash

# 设置变量,如果CONTAINER_NAME为空，则使用poc-intelligence-view
CONTAINER_NAME=${1:-poc-intelligence-view-write-write}
# 获取镜像名称和标签,如果IMAGE_NAME为空，则使用poc-intelligence-view    
IMAGE_NAME=${2:-poc-intelligence-view-write}
# 获取镜像标签,如果IMAGE_TAG为空，则使用1.0.0
IMAGE_TAG=${3:-1.0.0}
PORT=9981

echo "开始启动前端服务..."
# 当前目录
CURRENT_DIR=$(pwd)
# poc-intelligence-view只显示第一层 
tree $CURRENT_DIR | grep -E 'poc-intelligence-view|nginx.conf'

# 检查容器是否已存在
if [ "$(docker ps -aq -f name=$CONTAINER_NAME)" ]; then
    echo "发现已存在的容器，正在停止并删除..."
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
fi

# 检查镜像是否存在
if [ ! "$(docker images -q $IMAGE_NAME:$IMAGE_TAG)" ]; then
    echo "镜像不存在，请先运行 build.sh 构建镜像"
    exit 1
fi

# 启动容器
echo "正在启动容器..."
echo "docker run -d --name $CONTAINER_NAME -p $PORT:80 -v $CURRENT_DIR/nginx.conf:/etc/nginx/conf.d/default.conf --restart unless-stopped $IMAGE_NAME:$IMAGE_TAG"
docker run -d \
    --name $CONTAINER_NAME \
    -p $PORT:80 \
    -v $CURRENT_DIR/nginx.conf:/etc/nginx/nginx.conf \
    --restart unless-stopped \
    $IMAGE_NAME:$IMAGE_TAG

# 检查容器是否成功启动
if [ $? -eq 0 ]; then
    echo "容器启动成功！"
    echo "容器信息："
    docker ps | grep $CONTAINER_NAME
    echo "容器日志："
    docker logs $CONTAINER_NAME
else
    echo "容器启动失败"
    exit 1
fi 
echo "访问地址：curl http://localhost:9981/poc-intelligence-view-write/"
curl http://localhost:9981/poc-intelligence-view-write/