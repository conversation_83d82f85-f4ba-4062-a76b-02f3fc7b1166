# 惠州市惠阳区政务服务和数据管理局智能公文系统

## 1. 服务连接图
![服务连接图](doc/架构图-部署架构图.png)

部署架构图说明：
- 本系统所有服务均通过宿主机 IP（如 **************）和固定端口对外提供服务。
- nginx-common（20004端口）为统一网关，负责静态资源、操作手册下载等。
- 前端核稿系统（20003端口）和拟文系统（20002端口）分别对应 poc-intelligence-view 和 poc-intelligence-view-write 服务，用户通过浏览器直接访问。
- 后端服务如 ai-doc-poc、ai-redactor、article-auto-compose-server、ai-writer-nostream 分别通过 20005、8000、20006、20008 等端口进行服务调用。
- 大模型服务 DeepSeek-R1-Distill-Llama-70B 通过 1025 端口对接。
- 日志采集由 filebeat 负责，日志上传至 elasticsearch（9200端口），Kibana（5601端口）用于日志和数据可视化。
- 操作手册等静态资源通过 nginx-common 统一对外提供。
- 所有服务均采用私有网络模式，端口映射与图中一致。
- 所有容器服务使用私有网络`jiutianwensi-network`

接口一览：
- 核稿前端：http://**************:20003/poc-intelligence-view/
- 拟文前端：http://**************:20002/poc-intelligence-view-write/intelligent-writing
- Kibana监控：http://**************:20001 
- 操作手册下载：http://**************:20004/操作手册/AI核稿操作手册.docx
- 统一网关（nginx-common）：http://**************:20004

## 2. 前端登录

### 2.1 核稿登录

在浏览器输入下面的地址

```shell
http://**************:20003/poc-intelligence-view/
```

操作手册下载地址

- http://**************:20004/操作手册/AI核稿操作手册.docx
- http://**************:20004/操作手册/国务院公文测试案例.docx
- http://**************:20004/操作手册/国务院部门文测试案例.docx

### 2.2 拟文登录

在浏览器输入下面的地址

```shell
http://**************:20002/poc-intelligence-view-write/intelligent-writing
```

## 3. 部署

### 3.1 环境要求

- 操作系统：arm 架构 Linux
- Docker 24.0.9+
- Docker Compose 2.0.0+
- 服务器建议配置：8核CPU/16GB内存/100GB硬盘

### 3.2 部署步骤

1. **安装依赖**

```shell
# Windows 需先安装 Docker Desktop
# Linux 安装命令：
sudo apt-get update && sudo apt-get install -y docker.io docker-compose
```

创建目录

```shell
mkdir -p /data/jtws
# 清理目录
rm -rf /data/jtws
mkdir -p /data/jtws
```

2. 将安装jtws.tar包上传到主机 `**************:/data/jtws`
   解压目录

```shell
# 解压到当前目录
cd /data/jtws
tar -xvf jtws.tar
```

3. **修改配置（可选）**

```shell
# 修改核稿服务配置
vi ai-doc-poc/config/application-prod.yml

# 修改大模型服务配置
vi ai-redactor/config/application.properties

# 修改Elasticsearch配置
vi elasticsearch/config/elasticsearch.yml
```

4. **执行部署**

```shell
# 赋予执行权限
chmod +x deploy.sh

# 启动所有服务（首次执行会自动下载镜像）
./deploy.sh
```

5. **部署大模型服务**

```shell
cd deepseek70b
chmod +x install-ds70b.sh
./install-ds70b.sh
```

6. **验证部署**

```shell
# 查看容器状态
docker ps -a

# 查看实时日志
docker logs -f ai-doc-poc

# 访问服务
echo "Kibana监控：http://**************:20001"
echo "拟文系统：http://**************:20002/poc-intelligence-view-write/intelligent-writing"
echo "核稿系统：http://**************:20003/poc-intelligence-view/"
```

7. 进入容器
   使用 `docker exec -it <服务名> bash` 所有服务信息的请参照服务台账
   以 ai-doc-poc 为例

```shell
docker exec -it ai-doc-poc bash
```

## 4. 服务台账

### 4.1 AI核稿前端 (poc-intelligence-view)

- 服务名称：poc-intelligence-view
- 镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0
- IP地址：**************
- 端口：20003（宿主端口）
- 网络模式：私有网络
- 配置文件路径：poc-intelligence-view/config/nginx.conf
- 日志文件路径：poc-intelligence-view/logs
- 数据文件路径：poc-intelligence-view/html
- 容器内端口：80
- 容器内配置文件路径：/etc/nginx/nginx.conf
- 容器内日志文件路径：/var/log/nginx
- 容器内数据文件路径：/usr/share/nginx/html
- 容器内测试脚本路径：/test.sh

### 4.2 拟文前端 (poc-intelligence-view-write)

- 服务名称：poc-intelligence-view-write
- 镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0
- IP地址：**************
- 端口：20002（宿主端口）
- 网络模式：私有网络
- 配置文件路径：poc-intelligence-view-write/config/nginx.conf
- 日志文件路径：poc-intelligence-view-write/logs
- 数据文件路径：poc-intelligence-view-write/html
- 容器内端口：80
- 容器内配置文件路径：/etc/nginx/nginx.conf
- 容器内日志文件路径：/var/log/nginx
- 容器内数据文件路径：/usr/share/nginx/html
- 容器内测试脚本路径：/test.sh

### 4.3 核稿java端服务 (ai-doc-poc)

- 服务名称：ai-doc-poc
- 镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT
- IP地址：**************
- 端口：20005（宿主端口）
- 网络模式：私有网络
- 配置文件路径：ai-doc-poc/config
- 日志文件路径：ai-doc-poc/logs
- 数据文件路径：ai-doc-poc/shells
- 容器内配置文件路径：/app/config
- 容器内日志文件路径：/app/logs
- 容器内脚本路径：/app/shells
- 环境变量：JAVA_OPTS=-Xms512m -Xmx1g

### 4.4 核稿python端服务 (ai-redactor)

- 服务名称：ai-redactor
- 镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18
- IP地址：**************
- 端口：20006（宿主端口）
- 网络模式：私有网络
- 配置文件路径：ai-redactor/config/application.properties
- 日志文件路径：ai-redactor/logs
- 数据文件路径：ai-redactor/shells
- 容器内配置文件路径：/app/ai_hegao_plus_app/application.properties
- 容器内日志文件路径：/app/log
- 容器内脚本路径：/app/shells

### 4.5 拟文java端服务 (article-auto-compose-server)

- 服务名称：article-auto-compose-server
- 镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT
- IP地址：**************
- 端口：20007（宿主端口）
- 网络模式：私有网络
- 配置文件路径：article-auto-compose-server/config
- 日志文件路径：article-auto-compose-server/logs
- 数据文件路径：article-auto-compose-server/shells
- 容器内配置路径：/app/config
- 容器内日志文件路径：/app/logs
- 容器内脚本路径：/app/shells

### 4.6 拟文python端服务 (ai-writer-nostream)

- 服务名称：ai-writer-nostream
- 镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python:v528
- IP地址：**************
- 端口：20008（宿主端口）
- 网络模式：私有网络
- 配置文件路径：ai-writer-nostream/config/application.properties
- 日志文件路径：ai-writer-nostream/logs
- 数据文件路径：ai-writer-nostream/shells
- 容器内配置路径：/app/temp/web_source_code/backend/application.properties
- 容器内日志文件路径：/app/temp/web_source_code/log
- 容器内脚本路径：/app/shells

### 4.7 ELK监控组件 (elasticsearch)

- 服务名称：elasticsearch
- 镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
- IP地址：**************
- 端口：9200, 9300（宿主端口）
- 网络模式：私有网络
- 配置文件路径：elasticsearch/config/elasticsearch.yml
- 日志文件路径：elasticsearch/logs
- 数据存储路径：elasticsearch/data
- 容器内配置文件路径：/usr/share/elasticsearch/config/elasticsearch.yml
- 容器内日志文件路径：/usr/share/elasticsearch/logs
- 容器内数据文件路径：/usr/share/elasticsearch/data
- 环境变量：
   - discovery.type=single-node
   - ES_JAVA_OPTS=-Xms1g -Xmx1g
   - xpack.security.enabled=true
   - ELASTIC_PASSWORD=elastic@123
   - bootstrap.memory_lock=true

### 4.8 可视化监控 (kibana)

- 服务名称：kibana
- 镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14
- IP地址：**************
- 访问端口：20001（宿主端口）
- 网络模式：私有网络
- 容器内端口：5601
- 配置文件路径：kibana/config/kibana.yml
- 数据文件路径：kibana/data
- 容器内配置文件路径：/usr/share/kibana/config/kibana.yml
- 容器内数据文件路径：/usr/share/kibana/data
- 环境变量：
   - TZ=Asia/Shanghai
   - ELASTICSEARCH_USERNAME=elastic
   - ELASTICSEARCH_PASSWORD=elastic@123

### 4.9 统一网关 (nginx-common)

- 服务名称：nginx-common
- 镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4
- IP地址：**************
- 访问端口：20004
- 网络模式：私有网络
- 配置文件路径：nginx-common/config/nginx.conf
- 日志路径：nginx-common/logs
- 数据文件路径：nginx-common/shells
- 容器内配置文件路径：/etc/nginx/nginx.conf
- 容器内日志文件路径：/var/log/nginx
- 容器内测试脚本路径：/test.sh

### 4.10 日志采集 (filebeat-common)

- 服务名称：filebeat-common
- 镜像：officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
- IP地址：**************
- 网络模式：私有网络
- 配置文件路径：filebeat/config/filebeat.yml
- 日志文件路径：filebeat/logs
- 容器内配置路径：/usr/share/filebeat/filebeat.yml
- 容器内日志路径：/var/log/filebeat
- 日志采集路径：
- /data/ai-doc-poc/logs
- /data/ai-redactor/logs
- /data/ai-writer-nostream/logs
- /data/article-auto-compose-server/logs
- /data/poc-intelligence-view-write/logs
- /data/poc-intelligence-view/logs
- /data/nginx-common/logs
- 环境变量：
   - TZ=Asia/Shanghai
   - strict.perms=false

### 4.11 大模型服务 (deepseek70b-server)

- 服务名称：deepseek70b-server  
- 主目录：/data/deepseek70b
- 镜像：swr.cn-south-1.myhuaweicloud.com/ascendhub/mindie:2.0.T3.1-800I-A2-py311-openeuler24.03-lts  
- IP地址：**************  
- 端口：1025（宿主端口）  
- 网络模式：私有网络  
- 配置文件路径：/data/deepseek70b/config  
- 日志文件路径：/data/deepseek70b/init-logs    
- 容器内配置文件路径：/usr/local/Ascend/mindie/latest/mindie-service/conf/config.json
- 容器内日志文件路径：/root/mindie/log/debug/  
- 容器内启动命令：/usr/local/Ascend/mindie/latest/mindie-service/bin/start.sh
- 容器外启动脚本路径：/data/deepseek70b/shells/start.sh
- 模型宿主机路径：/home/<USER>/DeepSeek-R1-Distill-Llama-70B
- 模型容器内路径：/data/DeepSeek-R1-Distill-Llama-70B
- 发布脚本：install-ds70b.sh


## 5. 配置文件说明

### 5.1 elasticsearch/config/elasticsearch.yml

| 参数名                 | 说明     | 默认值             |
| ---------------------- | -------- | ------------------ |
| cluster.name           | 集群名称 | jtws-elasticsearch |
| node.name              | 节点名称 | node-1             |
| network.host           | 绑定主机 | 0.0.0.0            |
| http.port              | HTTP端口 | 9200               |
| discovery.type         | 发现类型 | single-node        |
| xpack.security.enabled | 安全认证 | true               |

### 5.2 kibana/config/kibana.yml

| 参数名                 | 说明     | 默认值                        |
| ---------------------- | -------- | ----------------------------- |
| server.port            | 服务端口 | 5601                          |
| server.host            | 绑定主机 | 0.0.0.0                       |
| elasticsearch.hosts    | ES地址   | ["http://elasticsearch:9200"]     |
| elasticsearch.username | ES用户名 | elastic                       |
| elasticsearch.password | ES密码   | elastic@123                   |

### 5.3 ai-doc-poc/config/application-prod.yml

| 参数名                                    | 说明           | 默认值                                |
| ----------------------------------------- | -------------- | ------------------------------------- |
| server.port                               | 服务端口       | 8080                                  |
| server.servlet.context-path               | 上下文路径     | /ai-doc-poc                           |
| server.shutdown                           | 关闭方式       | graceful                              |
| spring.application.name                   | 应用名称       | ai-doc-poc                                 |
| spring.servlet.multipart.location         | 文件上传路径   | /app/files                            |
| spring.servlet.multipart.max-request-size | 总文件大小限制 | 100MB                                 |
| spring.servlet.multipart.max-file-size    | 单文件大小限制 | 100MB                                 |
| ai-service.providers[0].base-url          | 模型服务地址   | http://ai-redactor:8000/spellcheck/oa   |
| ai-service.providers[1].base-url          | 模型服务地址   | http://ai-redactor:8000/spellcheck/oa   |
| logging.config                            | 日志配置路径   | /app/config/logback.xml               |

### 5.4 filebeat/config/filebeat.yml

| 参数名               | 说明         | 默认值                        |
| -------------------- | ------------ | ----------------------------- |
| filebeat.inputs      | 日志输入配置 | type: log, paths: [...]       |
| output.elasticsearch | ES输出配置   | hosts: ["http://elasticsearch:9200"] |
| setup.kibana         | Kibana设置   | host: "http://kibana:5601" |

### 5.5 ai-redactor/config/application.properties

| 参数名     | 说明          | 默认值                                    |
| ---------- | ------------- | ----------------------------------------- |
| model-url  | 大模型API地址 | http://deepseek70b-server:1025/v1/chat/completions |
| api-key    | API密钥       | -                                         |
| model-name | 模型名称      | DeepSeek-70B             |

### 5.6 ai-writer-nostream/config/application.properties

| 参数名                 | 说明            | 默认值                                                                  |
| ---------------------- | --------------- | ----------------------------------------------------------------------- |
| model.url              | 大模型API地址   | http://deepseek70b-server:1025/v1/chat/completions                               |
| model.appid            | 应用ID          | tyrknosa                                                                |
| model.appKey           | 应用密钥        | 5ab43270aecc9f482b79c965ab81d411                                        |
| model.capabilityname   | 能力名称        | semantic0000000000000000                                                |
| model.model-name       | 模型名称        | DeepSeek-70B                                           |
| path.sensitiveWords    | 敏感词文件路径  | /app/temp/web_source_code/backend/sensitive_words_all.txt               |
| path.rulesFile         | 规则文件路径    | /app/temp/web_source_code/backend/re.json                               |
| path.templatesDir      | 模板目录路径    | /app/temp/web_source_code/backend/writing_template                      |
| generation.maxTokens   | 最大生成token数 | 1024                                                                    |
| generation.temperature | 模型温度参数    | 0.2                                                                     |
| system.prompt          | 系统提示词      | 你是一个公文写作专家，公文内不要出现"我们"、"我"、"你们"等口语化词汇... |

### 5.7 article-auto-compose-server/config/application-prod.yml

| 参数名                                    | 说明                   | 默认值                                                |
| ----------------------------------------- | ---------------------- | ----------------------------------------------------- |
| server.port                               | 服务端口               | 8080                                                  |
| server.servlet.context-path               | 上下文路径             | /ai-compose-poc                                       |
| server.shutdown                           | 关闭方式               | graceful                                              |
| spring.application.name                   | 应用名称               | ai-compose                                            |
| spring.servlet.multipart.location         | 文件上传路径           | /app/files                                            |
| spring.servlet.multipart.max-request-size | 总文件大小限制         | 100MB                                                 |
| spring.servlet.multipart.max-file-size    | 单文件大小限制         | 100MB                                                 |
| ai-service.providers[0].base-url          | 模型服务地址           | http://ai-writer-nostream:8080/mubanwriter/v1/service         |
| moa.check.checkUnitId                     | 核稿使用的模型ID       | SQUARE                                                |
| moa.check.attachmentExpire                | 核稿结果文件有效期(天) | 7                                                     |
| logging.config                            | 日志配置路径           | /app/config/logback.xml                               |

### 5.8 nginx-common/config/nginx.conf

| 配置块   | 参数               | 说明         | 示例值                 |
| -------- | ------------------ | ------------ | ---------------------- |
| http     | worker_connections | 工作连接数   | 1024                   |
| http     | keepalive_timeout  | 连接保持时间 | 65                     |
| server   | listen             | 监听端口     | 80                     |
| server   | server_name        | 服务器名称   | localhost              |
| location | proxy_pass         | 代理转发地址 | http://ai-doc-poc:8080 |
| location | proxy_set_header   | 代理头设置   | Host $host             |

### 5.9 poc-intelligence-view/config/nginx.conf

| 配置块   | 参数      | 说明         | 示例值                   |
| -------- | --------- | ------------ | ------------------------ |
| http     | sendfile  | 启用零拷贝   | on                       |
| server   | listen    | 监听端口     | 80                       |
| server   | root      | 网站根目录   | /usr/share/nginx/html    |
| server   | index     | 默认索引     | index.html               |
| location | try_files | 文件查找规则 | $uri $uri/ /index.html |

### 5.10 poc-intelligence-view-write/config/nginx.conf

| 配置块                                                        | 参数      | 说明         | 示例值                   |
| ------------------------------------------------------------- | --------- | ------------ | ------------------------ |
| http                                                          | sendfile  | 启用零拷贝   | on                       |
| server                                                        | listen    | 监听端口     | 80                       |
| server                                                        | root      | 网站根目录   | /usr/share/nginx/html    |
| server                                                        | index     | 默认索引     | index.html               |
| location                                                      | try_files | 文件查找规则 | $uri $uri/ /index.html |

### 5.11 deepseek70b/config/config.json

| 配置项 | 说明 | 示例/默认值 |
|----------------------|------------------------------|---------------------------------------------|
| Version | 配置文件版本 | 1.0.0 |
| LogConfig.logLevel | 日志级别 | Info（可选：Debug/Info/Warn/Error） |
| LogConfig.logFileSize | 单个日志文件最大MB | 20 |
| LogConfig.logFileNum | 日志文件最大保留数量 | 20 |
| LogConfig.logPath | 日志文件目录 | /usr/local/Ascend/mindie/latest/mindie-service/logs |
| ServerConfig.ipAddress | 服务监听IP | 0.0.0.0 |
| ServerConfig.port | 服务主端口 | 1025 |
| ServerConfig.metricsPort | 监控端口 | 1027 |
| ServerConfig.allowAllZeroIpListening | 允许0.0.0.0监听 | true |
| ServerConfig.maxLinkNum | 最大连接数 | 1000 |
| ServerConfig.httpsEnabled | 是否启用HTTPS | false |
| BackendConfig.backendName | 后端引擎名称 | mindieservice_llm_engine |
| BackendConfig.modelInstanceNumber | 模型实例数 | 1 |
| BackendConfig.npuDeviceIds | NPU设备ID列表 | [[0,1,2,3,4,5,6,7]] |
| BackendConfig.ModelDeployConfig.maxSeqLen | 最大序列长度 | 32768 |
| BackendConfig.ModelDeployConfig.ModelConfig[].modelName | 模型名称 | DeepSeek-70B |
| BackendConfig.ModelDeployConfig.ModelConfig[].modelWeightPath | 模型权重路径 | /data/DeepSeek-R1-Distill-Llama-70B |
| BackendConfig.ModelDeployConfig.ModelConfig[].worldSize | 并行数 | 8 |

**说明：**
- 日志相关配置建议关注 `LogConfig` 部分，便于排查和运维。
- 服务监听端口、IP、最大连接数等建议与部署网络环境匹配。
- `ModelDeployConfig` 及 `ModelConfig` 关系到模型加载和推理性能，需根据实际硬件资源调整。
- 其他如 TLS、KMC、调度等参数，建议保持默认或由专业人员调整。

## 6. 运维

### 6.1 服务管理脚本

系统提供了 `main.sh` 脚本用于服务管理，支持以下功能：

#### 6.1.1 主菜单功能
1. 加载所有镜像
2. 启动所有服务
3. 停止所有服务
4. 重启所有服务
5. 单个服务操作
6. 查看服务状态
7. 查看端口映射
8. 查看网络信息
0. 退出

#### 6.1.2 单个服务操作菜单
1. 加载服务镜像
2. 启动服务
3. 停止服务
4. 重启服务
5. 修改服务配置
6. 进入容器
7. 查看日志
8. 执行测试脚本
9. 查看服务状态
10. 查看端口映射
11. 查看网络信息
12. 测试端口连通性
0. 返回服务列表

#### 6.1.3 功能说明

##### ******* 服务状态查看
- 显示所有容器的运行状态
- 显示资源使用情况（CPU、内存等）
- 显示最近10行日志

##### ******* 端口映射查看
- 显示所有容器的端口映射
- 显示配置的端口映射
- 显示实际运行的端口映射
- 测试端口连通性

##### ******* 网络信息查看
- 显示主机IP地址
- 显示服务网络模式
- 显示端口映射
- 显示服务访问地址
- 显示环境变量
- 测试网络连通性

##### ******* 服务配置修改
- 支持修改服务配置文件
- 修改后自动提示是否重启服务

##### ******* 日志查看
- 支持实时查看服务日志
- 支持查看最近日志

##### ******* 测试功能
- 支持执行服务测试脚本
- 支持测试端口连通性
- 支持测试HTTP/HTTPS服务响应

### 6.2 服务连接图

核稿：浏览器->poc-intelligence-view->ai-doc-poc->ai-redactor->大模型
拟文：浏览器->poc-intelligence-view-write->article-auto-compose-server->ai-writer-nostream->大模型

### 6.3 日志查询

#### 6.3.1 EFK日志

1.登录kibana：http://**************:20001 用户名/密码：elastic/elastic@123
2.然后转到：http://**************:20001/app/discover，里面有每个服务的日志，查询语法，请参照ELK官网的KQL语法

#### 6.3.2 容器内查询

1.进入容器

```shell
docker exec -it <服务名称> bash
```

2.根据服务台账上的信息，找到日志的路径

```shell
tail -f <日志路径>/<日志文件名>
```

### 6.4 服务管理

#### 6.4.1 服务重启

```shell
docker restart <服务名称>
```

#### 6.4.2 服务停止

```shell
docker stop <服务名称>
```

#### 6.4.3 服务删除

```shell
docker rm -f <服务名称>
```

#### 6.4.4 接口测试
根据服务台账上的信息，找到测试脚本的路径，执行测试脚本
```shell
#进入容器
docker exec -it <服务名称> bash
#执行测试脚本
cd <测试脚本路径>
sh test.sh
```

### 6.5 更新版本
每当有版本更新，按照邮件中的更新说明，更新版本
