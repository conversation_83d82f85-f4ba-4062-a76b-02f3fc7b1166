{"description": {"description": "字段说明", "source-image-repo-url": "源镜像仓库地址", "source-image-repo-user": "源镜像仓库用户名", "source-image-repo-password": "源镜像仓库密码", "source-image-namespace": "源镜像仓库命名空间", "target-image-repo-url": "目标镜像仓库地址", "target-image-repo-user": "目标镜像仓库用户名", "target-image-repo-password": "目标镜像仓库密码", "target-image-namespace": "目标镜像仓库命名空间", "cpu-arch": "cpu架构", "yml-file-name": "yml文件名", "type": "类型:application/middleware", "storage-class": "目标集群的存储类名称", "proxy-model": "代理模式:nginx/forward"}, "base-config": {"cpu-arch": "arm64", "target-image-repo-url": "************:8080", "target-image-repo-user": "admin", "target-image-repo-password": "Admin123", "target-image-namespace": "native_common", "source-image-repo-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn", "source-image-repo-user": "zglyjt", "source-image-repo-password": "Znwd0415@", "source-image-namespace": "jtws"}, "storage-config": {"storage-class": "standard", "server": "10.7.202.243", "path": "/nfs/data", "enabled": "true"}, "filebeat-config": {"source-images-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14", "target-images-url": "************:8080/native_common/filebeat:arm64-7.17.14"}, "model-config": {"url": "http://10.7.202.252:11434/v1/chat/completions", "api-key": "", "model-name": "deepseek-r1:70b"}, "proxy-config": {"proxy-model": "nginx", "nginx-config": {"port": "30001", "service-name-suffix": "oa-llm.jtws.com", "image-url": "************:8080/native_common/nginx:arm64-1.27.4"}}, "replace-config": [{"source-images-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14", "target-images-url": "************:8080/native_common/elasticsearch:arm64-7.17.14", "yml-file-name": "elasticsearch-all.yml", "description": "elasticsearch", "service-name": "elasticsearch", "type": "middleware", "proxy-port": "20001"}, {"source-images-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14", "target-images-url": "************:8080/native_common/kibana:arm64-7.17.14", "yml-file-name": "kibana.yml", "description": "kibana", "service-name": "kibana", "type": "middleware", "proxy-port": "20002"}, {"source-images-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-doc-poc:arm64-0.0.1-SNAPSHOT", "target-images-url": "************:8080/native_common/ai-doc-poc:arm64-0.0.1-SNAPSHOT", "yml-file-name": "ai-doc-poc.yml", "description": "核稿java端", "service-name": "ai-doc-poc", "type": "application", "proxy-port": "20003"}, {"source-images-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-redactor:arm64-v2025.05.18", "target-images-url": "************:8080/native_common/ai-redactor:arm64-v2025.05.18", "yml-file-name": "ai-redactor.yml", "description": "核稿python端", "service-name": "ai-redactor", "type": "application", "proxy-port": "20004"}, {"source-images-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/ai-writer-nostream-python-arm:v10.0", "target-images-url": "************:8080/native_common/ai-writer-nostream-python-arm:v10.0", "yml-file-name": "ai-writer-nostream.yml", "description": "拟稿python端", "service-name": "ai-writer-nostream", "type": "application", "proxy-port": "20005"}, {"source-images-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/article-auto-compose-server:arm64-0.0.1-SNAPSHOT", "target-images-url": "************:8080/native_common/article-auto-compose-server:arm64-0.0.1-SNAPSHOT", "yml-file-name": "article-auto-compose-server.yml", "description": "拟稿java端", "service-name": "article-auto-compose-server", "type": "application", "proxy-port": "20006"}, {"source-images-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view-write:arm64-1.0.0", "target-images-url": "************:8080/native_common/poc-intelligence-view-write:arm64-1.0.0", "yml-file-name": "poc-intelligence-view-write.yml", "description": "拟稿前端", "service-name": "poc-intelligence-view-write", "type": "application", "proxy-port": "20007"}, {"source-images-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/poc-intelligence-view:arm64-1.0.0", "target-images-url": "************:8080/native_common/poc-intelligence-view:arm64-1.0.0", "yml-file-name": "poc-intelligence-view.yml", "description": "核稿前端", "service-name": "poc-intelligence-view", "type": "application", "proxy-port": "20008"}, {"source-images-url": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4", "target-images-url": "************:8080/native_common/nginx:arm64-1.27.4", "yml-file-name": "jtws.yml", "description": "系统入口", "service-name": "jtws", "type": "application", "proxy-enable": "false", "proxy-port": "20009"}]}