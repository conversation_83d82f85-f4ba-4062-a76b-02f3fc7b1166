# 下载hyzhw镜像

TAR_DIR=/mnt/e/jtws/ops

mkdir -p $TAR_DIR

cd $TAR_DIR

echo "登录镜像仓库"
echo "docker login officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn --username=zglyjt --password=Znwd0415@"
docker login officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn --username=zglyjt --password=Znwd0415@

echo "下载ops镜像"
echo "docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14"
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
echo "docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14"
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14
echo "docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14"
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
echo "docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4"
docker pull officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4

echo "保存ops镜像"
echo "docker save -o elasticsearch.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14"
docker save -o elasticsearch.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/elasticsearch:arm64-7.17.14
echo "docker save -o kibana.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14"
docker save -o kibana.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/kibana:arm64-7.17.14
echo "docker save -o filebeat.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14"
docker save -o filebeat.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/filebeat:arm64-7.17.14
echo "docker save -o nginx.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4"
docker save -o nginx.tar officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/nginx:arm64-1.27.4

echo "查看镜像"
echo "docker images"
docker images
