{"app_name": "query-parser", "module_name": "znwd", "ip_address": "{{ip_address}}", "base_dir": "{{base_dir}}", "version": "v1", "image_name": "officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/query-parser:v1", "container_port": "7890", "host_port": "8084", "app_data_dir": "/home/<USER>/data", "app_logs_dir": "/home/<USER>/logs", "app_config_dir": "/home/<USER>/config", "host_data_dir": "{{base_dir}}/znwd/query-parser/data", "host_logs_dir": "{{base_dir}}/znwd/query-parser/logs", "host_config_dir": "{{base_dir}}/znwd/query-parser/config", "restart_script": "{{base_dir}}/znwd/query-parser/restart.sh", "test_script": "{{base_dir}}/znwd/query-parser/test_curl.sh", "runtime": "python", "env_vars": [{"name": "PYTHONPATH", "value": "/home/<USER>"}, {"name": "TZ", "value": "Asia/Shanghai"}], "external_dependencies": [{"type": "service", "name": "embedding-service-bge-m3", "url": "http://embedding-service-bge-m3:8080"}], "test_commands": ["curl -s http://localhost:8084/health | grep OK"]}