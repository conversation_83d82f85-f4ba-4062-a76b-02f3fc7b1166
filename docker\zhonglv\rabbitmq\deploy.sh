
                                                                                                                  
# 创建持久化目录（防止数据丢失）
echo "创建持久化目录"
echo "mkdir -p /data/rabbitmq/{data,conf,log}"
mkdir -p /data/rabbitmq/{data,conf,log}
echo "设置权限"
echo "chmod -R 755 /data/rabbitmq"
chmod -R 755 /data/rabbitmq
# 启动容器
echo "启动容器"
echo "docker run -itd --name rabbitmq -p 5672:5672 -p 15672:15672 -v /data/rabbitmq/data:/var/lib/rabbitmq -v /data/rabbitmq/log:/var/log/rabbitmq -e RABBITMQ_DEFAULT_USER=admin   -e RABBITMQ_DEFAULT_PASS=admin123  --restart always  m.daocloud.io/docker.io/rabbitmq:3.12-management"
docker run -itd --name rabbitmq -p 5672:5672 -p 15672:15672 -v /data/rabbitmq/data:/var/lib/rabbitmq -v /data/rabbitmq/log:/var/log/rabbitmq -e RABBITMQ_DEFAULT_USER=admin   -e RABBITMQ_DEFAULT_PASS=admin123  --restart always  m.daocloud.io/docker.io/rabbitmq:3.12-management
echo "查看容器状态"
echo "docker ps | grep rabbitmq"
docker ps | grep rabbitmq
# 启用管理插件
echo "启用管理插件"
echo "docker exec -it rabbitmq rabbitmq-plugins enable rabbitmq_management"
docker exec -it rabbitmq rabbitmq-plugins enable rabbitmq_management
# 查看插件列表
echo "查看插件列表"
echo "docker exec -it rabbitmq rabbitmq-plugins list"
docker exec -it rabbitmq rabbitmq-plugins list
# 查看管理控制台
echo "管理控制台地址：http://192.168.1.100:15672"
# 登录管理控制台
echo "用户名：admin"
echo "密码：admin123"   
# 创建一个测试队列
echo "创建一个测试队列"
echo "docker exec -it rabbitmq rabbitmqadmin declare queue name=test"
docker exec -it rabbitmq rabbitmqadmin declare queue name=test
# 发送消息到测试队列
echo "发送消息到测试队列"
echo "docker exec -it rabbitmq rabbitmqadmin publish routing_key=test payload='hello'"
docker exec -it rabbitmq rabbitmqadmin publish routing_key=test payload='hello'
# 查看消息
echo "查看消息"
echo "docker exec -it rabbitmq rabbitmqadmin get queue=test"
docker exec -it rabbitmq rabbitmqadmin get queue=test
# 删除消息
echo "删除消息"
echo "docker exec -it rabbitmq rabbitmqadmin delete queue name=test"
docker exec -it rabbitmq rabbitmqadmin delete queue name=test



