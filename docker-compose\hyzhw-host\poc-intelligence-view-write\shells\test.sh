# ====================== 文档检查API测试 ======================
echo -e "${BLUE}=== 文档检查API测试 ===${NC}"
echo -e "${YELLOW}正在测试文档检查接口...${NC}"

# 测试网络是否连通
echo -e "${YELLOW}正在测试网络是否连通...${NC}"
# 测试直连
echo -e "${YELLOW}正在测试直连...${NC}"
curl -v -X POST http://192.168.1.100:20007/ai-compose-poc/api/v1/compose \
-H 'Content-Type: application/json' \
-d '{
    "composeUnitId": "glmwriter",
    "text": "请以《中国移动公司关于开展成本管理专项活动通知》为主题生成一篇通知类型的公文。字数要求100字以上"
}'
echo

# 测试文档为test.txt，请确保该文件存在
echo -e "${YELLOW}正在测试文档检查代理接口...${NC}"
curl -v -X POST http://127.0.0.1:20002/ai-compose-poc/api/v1/compose \
-H 'Content-Type: application/json' \
-d '{
    "composeUnitId": "glmwriter",
    "text": "请以《中国移动公司关于开展成本管理专项活动通知》为主题生成一篇通知类型的公文。字数要求100字以上"
}'

echo
echo -e "${GREEN}=== 文档检查API测试结束 ===${NC}"