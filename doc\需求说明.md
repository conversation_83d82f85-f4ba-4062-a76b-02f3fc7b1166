# 九天.文思系统运维说明手册
## 背景
九天.文思系统已经在中铝服务器上部署，部署的方案，参照九天文思-中铝部署方案.md，后续要将这套部署方法应用到其他公司。需要建立一套标准化、规范化的自动部署系统，减少人工操作，提高部署效率和稳定性。

## 前提条件
- 客户主机上已经安装好docker（推荐版本20.10.x或更高）
- 登录的主机的账户有docker权限和sudo权限
- 主机具备部署目录和应用目录，具备足够的存储空间（建议至少500GB可用空间）
- 登录的账户有部署目录和应用目录的755权限
- 客户主机为X86架构（ARM64架构需单独适配）
- 主机满足最低配置要求：CPU 64核、内存128GB
- 目标主机SSH服务正常开启，支持密钥或密码认证

## 整体目标
- 通过ansible批量部署九天.文思的中间件，向量及排序模型，应用程序
- 每个应用，中间件等在主机上都有一个部署目录，目录下有deploy.sh，配置文件，restart.sh，test_curl.sh
- 每个应用，中间件等在主机上有一个应用目录，目录下有config目录，logs目录，shells目录（启动脚本），数据目录(如果需要),app.jar
- 前端应用通过Nginx容器映射到主机目录
- 实现灵活的部署策略，支持全量部署和选择性部署
- 提供完善的健康检查和自动化测试功能
- 建立标准化的部署结果验证机制
- 支持多节点分布式部署和单节点全量部署两种模式

## 实现方案
整个自动化部署流程分为三个主要阶段：资源收集与准备阶段、打包与规范化阶段、部署与验证阶段。

### 一、资源收集与准备阶段
#### 1. 镜像收集
- 从中铝已部署主机上下载所有Docker镜像，转换为tar格式
- 使用命令 `docker save -o 镜像名称.tar 镜像名称:标签` 导出镜像
- 对镜像进行版本标记和归类管理
- 保存每个镜像的版本号、大小、SHA256值等信息，建立镜像版本台账
- 镜像分类存放在`oa-llm-ops/download/images`目录下，便于后续统一管理

#### 2. 部署脚本收集
- 从`documents/deploy-zhonglv/shells`目录收集所有中间件和应用的部署脚本
- 分析每个deploy.sh和test_curl.sh脚本，提取通用部署模式
- 对每类组件（中间件、向量模型、Java应用、Python应用、前端）的部署特点进行梳理
- 创建部署脚本模板库，用于后续生成标准化部署脚本

#### 3. 配置文件处理
- 收集所有组件的配置文件
- 将IP地址等环境相关参数转换为变量，实现配置的可重用性
- 将IP用主机名替代，为每个应用设计一个主机名，如redis→redis.jiutian.wensi.com
- 获取主机清单后使用JSON配置文件存储主机映射关系
- 在部署过程中，获取客户的主机清单后转为JSON格式，读取JSON后用字符串替换方式处理配置文件
- 设计主机映射JSON格式如下：
```json
{
  "hosts": [
    {
      "hostname": "redis.jiutian.wensi.com",
      "ip": "*************",
      "description": "Redis服务器"
    },
    {
      "hostname": "postgresql.jiutian.wensi.com",
      "ip": "*************",
      "description": "PostgreSQL数据库服务器"
    }
  ]
}
```

### 二、打包与规范化阶段
#### 1. 修改部署脚本
- 修改原有deploy.sh脚本，适配预下载镜像模式
- 调整脚本流程为：检查→准备环境→加载本地镜像→创建容器→验证
- 移除镜像在线拉取逻辑，改为从本地tar包加载
- 增加镜像完整性校验和错误处理逻辑

#### 2. 标准化目录结构
对不同类型组件设计统一的目录结构规范：

##### Java应用目录结构
```
{app_name}/
├── build/
│   ├── image.tar
│   ├── deploy.sh
│   ├── test.sh
│   ├── Dockerfile
├── config/
│   ├── application.yml
│   └── logback.xml
├── app.jar
```

##### 前端目录结构
```
frontend/
├── build/
│   ├── nginx.tar
│   ├── deploy.sh
│   ├── nginx_template.conf
├── web_assistant/
│   └── dist/
├── web_assistant_admin/
│   └── dist/
├── 其他前端包...
```

##### 中间件目录结构
```
{middleware_name}/
├── build/
│   ├── image.tar
│   ├── deploy.sh
│   ├── test_curl.sh
├── config/
    └── {middleware_specific_configs}
```

##### 向量模型目录结构
```
{model_name}/
├── build/
│   ├── image.tar
│   ├── deploy.sh
│   ├── test.sh
├── config/
│   └── model_config.yaml
```

##### Python应用目录结构
```
{python_app}/
├── build/
│   ├── image.tar
│   ├── deploy.sh
│   ├── test.sh
├── config/
└── source/
```

#### 3. 打包工具开发
- 开发本地打包脚本（cmd或shell），将镜像、配置文件和部署脚本打包成标准tar
- 生成部署清单和组件依赖关系图
- 实现批量打包功能，一键生成所有组件的部署包
- 创建checksum文件，用于后续部署时验证包完整性

#### 4. 通用部署脚本模板
设计deploy-common.sh脚本作为部署基础模板：
```bash
#!/bin/bash
# 通用部署脚本模板

# 输入参数
APP_NAME=${1:-"app"}
MODULE_NAME=${2:-"module"}
IMAGE_NAME=${3:-"image:latest"}
HOST_PORT=${4:-"8080"}
CONTAINER_PORT=${5:-"8080"}

# 应用目录结构
APP_BASE_DIR="/data/${MODULE_NAME}/${APP_NAME}"
APP_CONFIG_DIR="${APP_BASE_DIR}/config"
APP_LOG_DIR="${APP_BASE_DIR}/logs"
APP_DATA_DIR="${APP_BASE_DIR}/data"
APP_SHELLS_DIR="${APP_BASE_DIR}/shells"

# 部署逻辑
function deploy() {
  # 创建目录结构
  mkdir -p ${APP_CONFIG_DIR} ${APP_LOG_DIR} ${APP_DATA_DIR} ${APP_SHELLS_DIR}
  
  # 设置权限
  chmod -R 755 ${APP_BASE_DIR}
  chmod 777 ${APP_LOG_DIR}
  
  # 检查网络
  docker network inspect jiutianwensi >/dev/null 2>&1 || docker network create jiutianwensi
  
  # 停止并删除已存在的容器
  docker rm -f ${APP_NAME} >/dev/null 2>&1 || true
  
  # 加载镜像
  if ! docker image inspect ${IMAGE_NAME} >/dev/null 2>&1; then
    docker load -i image.tar
  fi
  
  # 启动容器
  docker run -d --name ${APP_NAME} \
    --network jiutianwensi \
    -v ${APP_CONFIG_DIR}:/app/config \
    -v ${APP_LOG_DIR}:/app/logs \
    -v ${APP_DATA_DIR}:/app/data \
    -v ${APP_SHELLS_DIR}:/app/shells \
    -p ${HOST_PORT}:${CONTAINER_PORT} \
    --restart always \
    ${IMAGE_NAME}
  
  # 验证部署
  docker ps | grep ${APP_NAME}
}

# 执行部署
deploy
```

#### 5. 组件配置JSON化
使用JSON格式定义各组件的部署配置，数据来源规则如下：
- 主机部署信息（ip, app_name, base_dir等）来自于CSV格式的规划表
- host_*相关字段由base_dir/app_name+特定目录组成
- 其他配置字段从"九天文思-中铝部署方案.md"文档中提取

JSON结构示例：
```json
{
  "app_name": "qa",
  "module_name": "znwd",
  "ip_address": "**********", 
  "container_port": "8080",
  "host_port": "8083",
  "app_data_dir": "/app/files",
  "app_log_dir": "/app/logs",
  "app_config_dir": "/app/config",
  "image_name": "znwd/qa:0.0.1-SNAPSHOT",
  "host_data_dir": "/data/znwd/qa/data",
  "host_log_dir": "/data/znwd/qa/log",
  "host_config_dir": "/data/znwd/qa/config",
  "restart_script": "/data/znwd/qa/restart.sh",
  "test_script" : "/data/znwd/qa/test.sh",
  "env_vars": [
    {"name": "JAVA_OPTS", "value": "-Xms512m -Xmx1g"}
  ]
}
```

数据处理流程：
1. 读取CSV规划表，提取ip、app_name、base_dir等基础信息
2. 根据规则生成各目录路径：
   - host_data_dir = "${base_dir}/${app_name}/data"
   - host_log_dir = "${base_dir}/${app_name}/log"
   - host_config_dir = "${base_dir}/${app_name}/config"
3. 从"九天文思-中铝部署方案.md"提取镜像名称、端口映射、环境变量等配置信息
4. 生成完整的JSON配置文件，用于后续部署脚本生成

### 三、部署与验证阶段
#### 1. Ansible控制环境
- 制作自包含的Ansible Docker镜像，避免客户环境依赖问题
- 镜像中预装所有必要工具：Ansible、Python、SSH客户端、Shell工具等
- 支持挂载本地目录便于部署文件交换
- 提供统一的部署入口脚本

#### 2. Ansible部署架构
##### 目录结构
```
oa-llm-ops/
├── ansible/
│   ├── inventory/
│   │   ├── hosts.yml
│   │   ├── group_vars/
│   │   └── host_vars/
│   ├── roles/
│   │   ├── common/
│   │   ├── middleware/
│   │   ├── vector_models/
│   │   ├── java_apps/
│   │   ├── python_apps/
│   │   └── frontend/
│   ├── playbooks/
│   │   ├── site.yml
│   │   ├── middleware.yml
│   │   ├── apps.yml
│   │   └── frontend.yml
│   └── ansible.cfg
├── download/
│   ├── images/
│   └── packages/
└── doc/
    └── 部署手册.md
```

##### 核心Playbook定义
- `site.yml`: 全量部署整个系统
- `middleware.yml`: 只部署中间件
- `apps.yml`: 只部署应用程序
- `frontend.yml`: 只部署前端应用

##### 角色职责划分
- `common`: 通用基础环境准备，检查前提条件
- `middleware`: 中间件部署和配置
- `vector_models`: 向量模型服务部署
- `java_apps`: Java应用部署
- `python_apps`: Python应用部署
- `frontend`: 前端应用部署

#### 3. 部署流程执行
1. **前置检查**
   - 检查目标主机Docker安装状态
   - 验证网络连通性
   - 检查磁盘空间

2. **基础环境准备**
   - 创建基础目录结构
   - 设置Docker私有网络
   - 配置hosts文件

3. **中间件部署**
   - 按照依赖顺序依次部署中间件
   - Redis → PostgreSQL → RabbitMQ → Zookeeper → Elasticsearch → OpenSearch → MinIO

4. **向量模型服务部署**
   - 部署embedding服务
   - 部署reranker服务

5. **应用程序部署**
   - 部署后端服务
   - 部署Python应用

6. **前端应用部署**
   - 部署Nginx
   - 部署各前端应用

7. **部署验证**
   - 执行组件健康检查
   - 验证各组件之间的网络连通性
   - 执行基本功能测试

#### 4. 批量部署工具
- 开发批量部署界面（Web或命令行），便于操作
- 支持选择性部署和全量部署
- 可视化展示部署进度和结果
- 支持多目标主机同时部署

### 安全策略
1. **密码管理**
   - 使用Ansible Vault加密保存所有密码
   - 支持密码随机生成和自定义密码两种模式
   - 密码强度要求：12位以上，包含大小写字母、数字和特殊字符

2. **网络安全**
   - 仅开放必要的端口
   - 使用Docker网络隔离内部服务
   - 对外服务统一通过Nginx网关访问

3. **数据安全**
   - 敏感配置参数加密存储
   - 部署日志脱敏
   - 数据卷权限控制

### 监控与维护
1. **日志管理**
   - 继续使用EFK系统统一收集日志
   - 自动配置Filebeat采集新部署组件的日志
   - 提供标准化的日志查询接口

2. **健康检查**
   - 为每个组件提供标准化的健康检查脚本
   - 定期执行健康检查并记录结果
   - 提供健康状态可视化界面

3. **备份与恢复**
   - 实现关键数据的自动备份机制
   - 提供一键式恢复功能
   - 支持定时备份和手动备份两种模式

### 版本管理与迭代
1. **版本迭代管理**
   - 设计版本升级策略
   - 支持增量更新和全量更新
   - 提供回滚机制

2. **多环境支持**
   - 支持开发、测试、生产多环境配置
   - 环境隔离与参数差异化配置
   - 跨环境部署流程标准化

 