#!/bin/bash

# 获取当前脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
echo "SCRIPT_DIR: ${SCRIPT_DIR}"

# 加载所有工具函数
source "${SCRIPT_DIR}/check_k8s_resource.sh"
source "${SCRIPT_DIR}/wait_for_service.sh"
source "${SCRIPT_DIR}/handle_service_timeout.sh"
source "${SCRIPT_DIR}/diagnose_resource_issues.sh"
source "${SCRIPT_DIR}/show_resource_status.sh"
source "${SCRIPT_DIR}/view_pod_logs.sh"
source "${SCRIPT_DIR}/enter_pod_container.sh"

# 导出所有函数
export -f check_k8s_resource
export -f wait_for_service
export -f handle_service_timeout
export -f diagnose_resource_issues
export -f show_resource_status
export -f view_pod_logs
export -f enter_pod_container 