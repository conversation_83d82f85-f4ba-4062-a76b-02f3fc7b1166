#!/bin/bash
echo "===== 部署Elasticsearch和Kibana 7.x版本 ====="
APP_BASE_DIR=$1
# 设置ES用户名和密码
ES_USERNAME="elastic"
ES_PASSWORD="Elastic20250417@#"
#如果TARGET_DIR为空，则使用当前目录
# 判断elasticsearch和kibana的镜像是否存在
echo "0. 判断elasticsearch和kibana的镜像是否存在"
if ! docker images | grep -q "elasticsearch:7.17.14"; then
  echo "Elasticsearch镜像不存在，开始load镜像"
  # 判断elasticsearch*.tar是否存在
  if [ ! -f "${APP_BASE_DIR}/images/elasticsearch*.tar" ]; then
    echo "❌ Elasticsearch镜像文件不存在"
    exit 1
  fi
  # 从images中load镜像
  echo "docker load -i ${APP_BASE_DIR}/images/elasticsearch*.tar docker.elastic.co/elasticsearch/elasticsearch:7.17.14"
  docker load -i ${APP_BASE_DIR}/images/elasticsearch*.tar docker.elastic.co/elasticsearch/elasticsearch:7.17.14
fi

if ! docker images | grep -q "kibana:7.17.14"; then
  echo "Kibana镜像不存在，开始load镜像"
  # 判断kibana*.tar是否存在
  if [ ! -f "${APP_BASE_DIR}/images/kibana*.tar" ]; then
    echo "❌ Kibana镜像文件不存在"
    exit 1
  fi
  # 从images中load镜像
  echo "docker load -i ${APP_BASE_DIR}/images/kibana*.tar docker.elastic.co/kibana/kibana:7.17.14"
  docker load -i ${APP_BASE_DIR}/images/kibana*.tar docker.elastic.co/kibana/kibana:7.17.14
fi
#查看镜像是否存在
docker images | grep "elasticsearch:7.17.14"
docker images | grep "kibana:7.17.14"
# 查看容器是否存在
docker ps | grep "elasticsearch"
docker ps | grep "kibana"
# 如果存在，则停止并删除
echo "1. 停止并删除已存在的容器"
if docker ps | grep "elasticsearch"; then
  echo "Elasticsearch容器存在，开始停止并删除"
  docker stop elasticsearch
  docker rm -f elasticsearch
fi
if docker ps | grep "kibana"; then
  echo "Kibana容器存在，开始停止并删除"
  docker stop kibana
  docker rm -f kibana
fi




# 清理数据目录
echo "2. 清理数据目录 (必须清理以进行版本降级)"
rm -rf ${APP_BASE_DIR}/elasticsearch/data/*
rm -rf ${APP_BASE_DIR}/elasticsearch/logs/*
rm -rf ${APP_BASE_DIR}/kibana/data/*

# 创建必要的目录
echo "3. 创建必要的目录"
mkdir -p ${APP_BASE_DIR}/elasticsearch/data
mkdir -p ${APP_BASE_DIR}/elasticsearch/logs
mkdir -p ${APP_BASE_DIR}/elasticsearch/config
mkdir -p ${APP_BASE_DIR}/kibana/data
mkdir -p ${APP_BASE_DIR}/kibana/config

# 设置权限
echo "3. 设置目录权限"
chmod -R 777 ${APP_BASE_DIR}/elasticsearch
chmod -R 777 ${APP_BASE_DIR}/kibana

# 创建Elasticsearch配置文件
echo "4. 创建Elasticsearch配置文件"
cat >${APP_BASE_DIR}/elasticsearch/config/elasticsearch.yml <<EOF
cluster.name: es-docker-cluster
node.name: node-1
network.host: 0.0.0.0
discovery.type: single-node
xpack.security.enabled: true
http.cors.enabled: true
http.cors.allow-origin: "*"
EOF

# 创建Kibana配置文件，使用elastic用户
echo "5. 创建Kibana配置文件"
cat >${APP_BASE_DIR}/kibana/config/kibana.yml <<EOF
server.name: kibana
server.host: "0.0.0.0"
elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.username: "${ES_USERNAME}"
elasticsearch.password: "${ES_PASSWORD}"
i18n.locale: "zh-CN"
EOF

# 创建Docker Compose文件 - 使用7.17.14版本
echo "6. 创建Docker Compose文件"
mkdir -p ${APP_BASE_DIR}/elasticsearch_kibana
cat >${APP_BASE_DIR}/elasticsearch_kibana/docker-compose.yml <<EOF
version: '3'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.14
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=${ES_PASSWORD}
      - bootstrap.memory_lock=true
    volumes:
      - /data/elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - /data/elasticsearch/data:/usr/share/elasticsearch/data
      - /data/elasticsearch/logs:/usr/share/elasticsearch/logs
    ports:
      - 9200:9200
      - 9300:9300
    networks:
      - elasticsearch_kibana_elastic
    restart: always
    ulimits:
      memlock:
        soft: -1
        hard: -1

  kibana:
    image: docker.elastic.co/kibana/kibana:7.17.14
    container_name: kibana
    environment:
      - ELASTICSEARCH_USERNAME=${ES_USERNAME}
      - ELASTICSEARCH_PASSWORD=${ES_PASSWORD}
    volumes:
      - /data/kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml
      - /data/kibana/data:/usr/share/kibana/data
    ports:
      - 5601:5601
    networks:
      - elasticsearch_kibana_elastic
    restart: always
    depends_on:
      - elasticsearch

networks:
  elasticsearch_kibana_elastic:
    driver: bridge
EOF

# 启动所有服务
echo "7. 启动Elasticsearch和Kibana"
cd /data/elasticsearch_kibana && docker-compose up -d

# 等待Elasticsearch启动
echo "8. 等待Elasticsearch启动..."
MAX_TRIES=30
COUNTER=0
DELAY=10

while [ $COUNTER -lt $MAX_TRIES ]; do
  echo "尝试 $((COUNTER + 1))/$MAX_TRIES: 检查Elasticsearch是否可访问..."

  if curl -s -m 10 -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://localhost:9200/" | grep -q "version"; then
    echo "✅ Elasticsearch服务已成功启动！"
    break
  fi

  COUNTER=$((COUNTER + 1))

  if [ $COUNTER -eq $MAX_TRIES ]; then
    echo "❌ 在 $((MAX_TRIES * DELAY)) 秒内无法连接到Elasticsearch。"
    echo "请检查日志以了解更多信息:"
    echo "docker logs elasticsearch"
    exit 1
  else
    echo "⏳ Elasticsearch尚未准备好，等待 ${DELAY} 秒后重试..."
    sleep $DELAY
  fi
done

# 显示Elasticsearch基本信息
echo "9. Elasticsearch基本信息:"
curl -s -u ${ES_USERNAME}:${ES_PASSWORD} -X GET "http://localhost:9200/"

# 等待Kibana启动
echo "10. 等待Kibana启动..."
MAX_TRIES=20
COUNTER=0
DELAY=10

while [ $COUNTER -lt $MAX_TRIES ]; do
  echo "尝试 $((COUNTER + 1))/$MAX_TRIES: 检查Kibana是否可访问..."

  if curl -s -m 10 -I "http://localhost:5601" | grep -q "200 OK"; then
    echo "✅ Kibana服务已成功启动！"
    break
  fi

  COUNTER=$((COUNTER + 1))

  if [ $COUNTER -eq $MAX_TRIES ]; then
    echo "⚠️ 在 $((MAX_TRIES * DELAY)) 秒内无法连接到Kibana。"
    echo "Kibana可能需要更多时间初始化，请稍后手动检查。"
    echo "可以通过以下命令查看日志:"
    echo "docker logs kibana"
  else
    echo "⏳ Kibana尚未准备好，等待 ${DELAY} 秒后重试..."
    sleep $DELAY
  fi
done

# 检查服务状态
echo "10. 检查服务状态"
docker ps | grep "elasticsearch\|kibana"

echo "11. 访问信息:"
echo "   Elasticsearch地址: http://服务器IP:9200"
echo "   Kibana访问地址: http://服务器IP:5601"
echo "   用户名和密码: ${ES_USERNAME} / ${ES_PASSWORD}"

echo "===== 部署完成 ====="
echo "查看日志命令:"
echo "  Elasticsearch: docker logs elasticsearch"
echo "  Kibana: docker logs kibana"
echo ""
echo "重启服务命令: cd /data/${APP_BASE_DIR}/elasticsearch_kibana && docker-compose restart"
echo "停止服务命令: cd /data/${APP_BASE_DIR}/elasticsearch_kibana && docker-compose stop"
