accelerate==0.34.2
aiofiles==23.1.0
aiohttp==3.8.4
aiosignal==1.3.1
alembic==1.10.4
altair==5.0.1
annotated-types==0.7.0
anyio==3.7.0
argon2-cffi==21.3.0
argon2-cffi-bindings==21.2.0
arrow==1.2.3
asgiref==3.8.1
asttokens==2.2.1
async-generator==1.10
async-timeout==4.0.2
attrs==23.1.0
av==14.0.1
Babel==2.12.1
backcall==0.2.0
backports.lzma==0.0.14
beautifulsoup4==4.12.2
bleach==6.0.0
blinker==1.6.2
cachetools==5.3.1
certifi==2022.12.7
cffi==1.15.1
charset-normalizer==2.1.1
click==8.1.3
cloudpickle==3.1.0
cmake==3.25.0
cn2an==0.5.22
comm==0.1.3
configurable-http-proxy==0.2.3
contourpy==1.0.7
cos-python-sdk-v5==1.9.33
cpm-kernels==1.0.11
crcmod==1.7
cycler==0.11.0
datasets==3.0.0
debugpy==1.6.7
decorator==5.1.1
defusedxml==0.7.1
dill==0.3.8
diskcache==5.6.3
Django==5.1.1
djangorestframework==3.15.2
docstring_parser==0.16
einops==0.8.0
et_xmlfile==2.0.0
exceptiongroup==1.1.1
executing==1.2.0
fastapi==0.115.2
fastjsonschema==2.16.3
ffmpy==0.3.0
filelock==3.9.0
fire==0.7.0
fonttools==4.39.4
fqdn==1.5.1
frozenlist==1.3.3
fsspec==2023.6.0
gitdb==4.0.10
GitPython==3.1.31
GPUtil==1.4.0
gradio==4.44.1
gradio_client==1.3.0
greenlet==2.0.2
h11==0.14.0
httpcore==0.17.2
httptools==0.6.4
httpx==0.24.1
huggingface-hub==0.25.0
icetk==0.0.7
idna==3.4
importlib-metadata==6.6.0
importlib_resources==6.4.5
interegular==0.3.3
ipykernel==6.23.1
ipython==8.13.2
ipython-genutils==0.2.0
isoduration==20.11.0
jedi==0.18.2
jieba==0.42.1
Jinja2==3.1.2
joblib==1.4.2
json5==0.9.14
jsonpointer==2.3
jsonschema==4.17.3
jupyter-server==1.24.0
jupyter_client==8.2.0
jupyter_core==5.3.0
jupyterhub==0.9.6
jupyterlab==3.2.9
jupyterlab-language-pack-zh-CN==3.3.post4
jupyterlab-pygments==0.2.2
jupyterlab_server==2.22.1
# kenlm包需要CMake 3.5+和其他编译依赖
# kenlm==0.2.0
kiwisolver==1.4.4
lark==1.2.2
latex2mathml==3.76.0
linkify-it-py==2.0.2
lit==15.0.7
llamafactory==0.9.2
llvmlite==0.43.0
loguru==0.7.2
Mako==1.2.4
Markdown==3.4.3
markdown-it-py==2.2.0
MarkupSafe==2.1.2
matplotlib==3.7.1
matplotlib-inline==0.1.6
mdit-py-plugins==0.3.3
mdtex2html==1.2.0
mdurl==0.1.2
mistune==2.0.5
modelscope==1.18.1
mpmath==1.2.1
msgpack==1.1.0
multidict==6.0.4
multiprocess==0.70.16
nbclassic==0.4.5
nbclient==0.7.4
nbconvert==7.4.0
nbformat==5.8.0
nest-asyncio==1.5.6
networkx==3.0
ninja==********
nltk==3.9.1
notebook==6.5.1
notebook_shim==0.2.3
numba==0.60.0
numpy==1.24.3
nvidia-cublas-cu11==*********
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu11==11.8.87
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu11==11.8.89
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu11==11.8.89
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu11==********
nvidia-cudnn-cu12==********
nvidia-cufft-cu11==*********
nvidia-cufft-cu12==*********
nvidia-curand-cu11==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu11==*********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu11==*********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu11==2.20.5
nvidia-nccl-cu12==2.18.1
nvidia-nvjitlink-cu12==12.6.77
nvidia-nvtx-cu11==11.8.86
nvidia-nvtx-cu12==12.1.105
openpyxl==3.1.5
orjson==3.9.1
outlines==0.0.34
packaging==23.1
pamela==1.0.0
pandas==2.0.2
pandocfilters==1.5.0
parso==0.8.3
peewee==3.17.8
peft==0.12.0
pexpect==4.8.0
pickleshare==0.7.5
Pillow==9.3.0
platformdirs==3.5.1
proces==0.1.7
prometheus_client==0.21.0
prompt-toolkit==3.0.38
protobuf==3.18.3
psutil==5.9.5
ptyprocess==0.7.0
pure-eval==0.2.2
py-cpuinfo==9.0.0
pyahocorasick==2.1.0
pyarrow==17.0.0
pycorrector==1.0.0
pycparser==2.21
pycryptodome==3.21.0
pydantic==2.9.2
pydantic_core==2.23.4
pydeck==0.8.1b0
pydub==0.25.1
Pygments==2.15.1
Pympler==1.0.1
pynvml==11.5.0
pyparsing==3.0.9
pypinyin==0.53.0
pyrsistent==0.19.3
pysbd==0.3.4
python-dateutil==2.8.2
python-dotenv==1.0.1
python-json-logger==2.0.7
python-multipart==0.0.19
python-oauth2==1.1.1
pytz==2023.3
PyYAML==6.0
pyzmq==25.0.2
ray==2.37.0
referencing==0.35.1
regex==2023.6.3
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.4.1
rouge-chinese==1.0.3
rpds-py==0.20.0
ruff==0.8.2
safetensors==0.4.5
scipy==1.14.1
semantic-version==2.10.0
semver==3.0.0
Send2Trash==1.8.2
sentencepiece==0.1.99
shellingham==1.5.4
shtab==1.7.1
six==1.16.0
smmap==5.0.0
sniffio==1.3.0
soupsieve==2.4.1
SQLAlchemy==1.3.3
sqlparse==0.5.1
sse-starlette==2.1.3
stack-data==0.6.2
starlette==0.40.0
streamlit==1.17.0
swanboard==0.1.7b1
swankit==0.1.2b6
swanlab==0.4.6
sympy==1.11.1
termcolor==2.5.0
terminado==0.17.1
tiktoken==0.6.0
tinycss2==1.2.1
tokenizers==0.20.0
toml==0.10.2
tomlkit==0.12.0
toolz==0.12.0
torch==2.1.2
tornado==6.3.2
tqdm==4.66.5
traitlets==5.9.0
transformers==4.45.0
triton==2.1.0
trl==0.9.6
typeguard==4.4.1
typer==0.15.1
typing_extensions==4.12.2
tyro==0.8.14
tzdata==2023.3
tzlocal==5.0.1
uc-micro-py==1.0.2
ujson==5.10.0
unzip==1.0.0
uri-template==1.2.0
urllib3==2.2.3
uvicorn==0.22.0
uvloop==0.21.0
validators==0.20.0
vllm==0.4.0
watchdog==3.0.0
watchfiles==0.24.0
wcwidth==0.2.6
webcolors==1.13
webencodings==0.5.1
websocket-client==1.5.1
websockets==11.0.3
xformers==0.0.23.post1
xmltodict==0.14.2
xxhash==3.5.0
yarl==1.9.2
zipp==3.15.0
