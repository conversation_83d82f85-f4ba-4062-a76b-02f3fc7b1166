
#!/bin/bash
set -e

# 配置参数
DASHBOARD_VERSION="2.9.0"  # 2025年4月最新稳定版‌:ml-citation{ref="2" data="citationList"}
BASE_DIR="/data/opensearch-dashboard"
NETWORK_NAME="opensearch-net"
IMAGE_URL="public.ecr.aws/opensearchproject/opensearch-dashboards:${DASHBOARD_VERSION}"
docker pull ${IMAGE_URL}
# 创建目录结构
mkdir -p ${BASE_DIR}/{config,logs}
chown -R 1000:1000 ${BASE_DIR}  # 确保容器用户权限‌:ml-citation{ref="4" data="citationList"}

# 创建 Docker 私有网络（若不存在）
docker network inspect ${NETWORK_NAME} >/dev/null 2>&1 || \
  docker network create ${NETWORK_NAME}

# 启动 Dashboard 容器
docker run -d --name opensearch-dashboard \
  --network ${NETWORK_NAME} \
  -p 5601:5601 \
  -e "OPENSEARCH_HOSTS=https://opensearch:9200"  \
  -v ${BASE_DIR}/config:/usr/share/opensearch-dashboards/config \
  -v ${BASE_DIR}/logs:/usr/share/opensearch-dashboards/logs \
  ${IMAGE_URL}

echo "OpenSearch-Dashboard 部署完成！"
echo "访问地址：http://localhost:5601 (默认用户 admin:admin)"
echo "配置文件：${BASE_DIR}/config"
echo "日志目录：${BASE_DIR}/logs"