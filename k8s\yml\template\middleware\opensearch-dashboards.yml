apiVersion: apps/v1
kind: Deployment
metadata:
  name: opensearch-dashboards
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: opensearch-dashboards
  template:
    metadata:
      labels:
        app: opensearch-dashboards
    spec:
      containers:
      - name: opensearch-dashboards
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/opensearch-dashboards:2.9.0
        ports:
        - containerPort: 5601
        env:
        - name: OPENSEARCH_HOSTS
          value: "https://opensearch.oa-llm:9200"
        securityContext:
          runAsUser: 1000
          runAsGroup: 1000
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
---
apiVersion: v1
kind: Service
metadata:
  name: opensearch-dashboards
  namespace: oa-llm
spec:
  ports:
  - port: 5601
    targetPort: 5601
  selector:
    app: opensearch-dashboards
