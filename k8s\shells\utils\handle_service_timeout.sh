#!/bin/bash

# 处理服务启动超时
# 参数: $1 - 服务名称, $2 - 资源类型, $3 - 命名空间
function handle_service_timeout() {
    local service_name=$1
    local resource_type=$2
    local namespace=$3
    
    while true; do
        read -p "选项: [1]继续等待 [2]深入诊断 [3]继续执行: " WAIT_OPTION
        
        case $WAIT_OPTION in
            1)
                read -p "继续等待多少秒? (默认30): " ADDITIONAL_WAIT
                ADDITIONAL_WAIT=${ADDITIONAL_WAIT:-30}
                wait_for_service $service_name $resource_type $namespace $ADDITIONAL_WAIT
                if [ "$IS_RUNNING" = "true" ]; then
                    return
                fi
                ;;
            2)
                # 深入诊断
                diagnose_resource_issues $service_name $resource_type $namespace
                ;;
            3)
                echo "继续执行后续步骤..."
                return
                ;;
            *)
                echo "无效选项，请重新选择"
                ;;
        esac
    done
}

# 导出函数
export -f handle_service_timeout 