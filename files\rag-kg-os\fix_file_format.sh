#!/bin/bash

# 修复脚本文件行尾符号问题的脚本
# 将Windows格式的CRLF（\r\n）转换为Unix格式的LF（\n）

echo "开始修复文件格式问题..."

# 检查是否安装了dos2unix
if command -v dos2unix > /dev/null 2>&1; then
    echo "使用dos2unix修复文件..."
    # 修复所有.sh文件
    find ./ -name "*.sh" -type f -exec dos2unix {} \;
    # 修复可能的其他脚本文件
    find ./ -name "*.py" -type f -exec dos2unix {} \;
    find ./ -name "*.json" -type f -exec dos2unix {} \;
    find ./ -name "*.yaml" -type f -exec dos2unix {} \;
    find ./ -name "*.yml" -type f -exec dos2unix {} \;
    echo "使用dos2unix修复完成"
else
    echo "dos2unix未安装，使用sed命令修复文件..."
    # 使用sed命令修复
    find ./ -name "*.sh" -type f -exec sed -i 's/\r$//' {} \;
    find ./ -name "*.py" -type f -exec sed -i 's/\r$//' {} \;
    find ./ -name "*.json" -type f -exec sed -i 's/\r$//' {} \;
    find ./ -name "*.yaml" -type f -exec sed -i 's/\r$//' {} \;
    find ./ -name "*.yml" -type f -exec sed -i 's/\r$//' {} \;
    echo "使用sed命令修复完成"
fi

# 设置可执行权限
echo "设置脚本文件可执行权限..."
find ./ -name "*.sh" -type f -exec chmod +x {} \;
echo "权限设置完成"

# 列出处理过的.sh文件
echo "已处理的脚本文件："
find ./ -name "*.sh" -type f | sort

echo "文件格式修复完成" 