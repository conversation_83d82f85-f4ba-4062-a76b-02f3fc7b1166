apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-conf
  namespace: oa-llm
data:
  redis.conf: |
    bind 0.0.0.0
    protected-mode no
    requirepass admin123
    appendonly yes
    # maxmemory 256mb
    # maxclients 1000
    # loglevel notice
    # save 900 1
    # save 300 10
    # save 60 10000
    # notify-keyspace-events Ex
    # 你可以在这里添加更多 Redis 配置项
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: artifactory.dep.devops.cmit.cloud:20101/oallm_middleware/redis:7.4.1
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-conf
          mountPath: /etc/redis/redis.conf
          subPath: redis.conf
        - name: redis-data
          mountPath: /data
        env:
        - name: TZ
          value: Asia/Shanghai
        command: ["redis-server", "/etc/redis/redis.conf"]
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      volumes:
      - name: redis-conf
        configMap:
          name: redis-conf
      - name: redis-data
        hostPath:
          path: /data/redis/data
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: oa-llm
spec:
  ports:
  - port: 6379
    targetPort: 6379
  selector:
    app: redis
