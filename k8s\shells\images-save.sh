#bin/bash
# 获取当前目录
CURRENT_DIR=$(pwd)
# 获取父目录
PARENT_DIR=$(dirname ${CURRENT_DIR})
# 读取config.json文件
CONFIG_FILE="${CURRENT_DIR}/config.json"
if [ ! -f ${CONFIG_FILE} ]; then
    echo "config.json文件不存在"
    exit 1
fi

# 遍历replace-config中的每一个节点
REPLACE_CONFIG_COUNT=$(jq '.["replace-config"] | length' ${CONFIG_FILE})
for (( i=0; i<${REPLACE_CONFIG_COUNT}; i++ )); do
    # 获取source-images-url
    SOURCE_IMAGES_URL=$(jq -r ".[\"replace-config\"][$i][\"source-images-url\"]" ${CONFIG_FILE})
    # 获取target-images-url
    TARGET_IMAGES_URL=$(jq -r ".[\"replace-config\"][$i][\"target-images-url\"]" ${CONFIG_FILE})
    # 获取description
    DESCRIPTION=$(jq -r ".[\"replace-config\"][$i][\"description\"]" ${CONFIG_FILE})
    # 获取type
    TYPE=$(jq -r ".[\"replace-config\"][$i][\"type\"]" ${CONFIG_FILE})
    # 获取service-name
    SERVICE_NAME=$(jq -r ".[\"replace-config\"][$i][\"service-name\"]" ${CONFIG_FILE})
    # 将镜像保存到本地
    echo "将镜像保存到本地"
    echo "docker save -o ${SERVICE_NAME}.tar ${TARGET_IMAGES_URL}"
    # 判断镜像是否存在，如果存在就不用保存
    if [ -f ${SERVICE_NAME}.tar ]; then
        echo "镜像：${SERVICE_NAME}.tar 已存在"
        # 询问是否需要删除
        read -p "是否需要删除镜像重新保存？(y/n): " DELETE_IMAGE
        if [ "${DELETE_IMAGE}" == "y" ]; then
            echo "删除镜像：${SERVICE_NAME}.tar"
            rm -f ${SERVICE_NAME}.tar
        else
            echo "镜像：${SERVICE_NAME}.tar 已存在，不保存"
            continue
        fi
    fi
    # 将镜像保存到本地
    echo "docker save -o ${SERVICE_NAME}.tar ${TARGET_IMAGES_URL}"
    docker save -o ${SERVICE_NAME}.tar ${TARGET_IMAGES_URL}
done
