# !/usr/bin/env python
# -*-coding:utf-8 -*-
import json
import os
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

ENV_PROFILE = os.environ.get("ENV")


def load_os_config(config_file):
    """加载ES配置信息"""
    return json.load(open(config_file, "r"))


BASE_DIR = Path(__file__).resolve().parent.parent

file_path = os.path.join(BASE_DIR, "service", "os_config.json")
OS_MAPPING = load_os_config(file_path)
OS_HOST = os.environ.get("OS_IP", "*********")
OS_PORT = os.environ.get("OS_PORT", 9200)
OS_USERNAME = "admin"
OS_PASSWORD = "admin"
OS_AUTH = (OS_USERNAME, OS_PASSWORD)
OS_URL = "https://*********:9200"

# 测试环境分词获取的url
SEG_URL = "http://query-service:8080/Rag/QueryParser"
# 测试环境vector获取的url
# 基础URL
BCE_URL = "http://*********:8100/Rag/BceEmbeddingService"
BGE_URL = "http://**********:8104/Rag/BgeEmbeddingService"
BGE_M3_URL = "http://**********:8101/Rag/BgeM3Embedding"

APPID = "ragalgor"
APPKEY = "890ef0d2fa952f41ea0b3dfada212498"

logger.info("os config === " + str(OS_MAPPING))
logger.info("SEG_URL === " + str(SEG_URL))
logger.info("BCE_URL === " + str(BCE_URL))
logger.info("BGE_URL === " + str(BGE_URL))
logger.info("BGE_M3_URL === " + str(BGE_M3_URL))
logger.info("APPID === " + str(APPID))
logger.info("APPKEY === " + str(APPKEY))