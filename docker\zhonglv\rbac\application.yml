# Tomcat
server:
  port: 8091
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 1000
      min-spare: 30
  servlet:
    context-path: /
    session:
      cookie:
        http-only: true

knife4j:
  enable: true
  basic:
    enable: false
    username: admin
    password: admin
  setting:
    enableFooter: false

spring:
  # 环境 dev|test|prod
  messages:
    encoding: UTF-8
    basename: i18n/messages
  servlet:
    #如果是想要不限制文件上传的大小，那么就把两个值都设置为-1
    multipart:
      max-file-size: 100MB  #设置单个文件的大小
      max-request-size: 100MB #设置单次请求的文件的总大小
      enabled: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  datasource:
    # PostgreSQL
    driver-class-name: org.postgresql.Driver
    initial-size: 10
    max-active: 100
    min-idle: 10
    max-wait: 6000
    pool-prepared-statements: true
    max-pool-prepared-statement-per-connection-size: 20
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 300000
    #Oracle需要打开注释
    #      validation-query: SELECT 1 FROM DUAL
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
    stat-view-servlet:
      enabled: true
      url-pattern: /druid/*
  cloud:
    nacos:
      discovery:
        namespace: 226a7c6b-830b-472f-be54-a8049027185c

#  datasource:
#    druid:
#      #MySQL
#      driver-class-name: com.mysql.cj.jdbc.Driver
#      initial-size: 10
#      max-active: 100
#      min-idle: 10
#      max-wait: 6000
#      pool-prepared-statements: true
#      max-pool-prepared-statement-per-connection-size: 20
#      time-between-eviction-runs-millis: 60000
#      min-evictable-idle-time-millis: 300000
#      #Oracle需要打开注释
#      #      validation-query: SELECT 1 FROM DUAL
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      stat-view-servlet:
#        enabled: true
#        url-pattern: /druid/*

  # redis配置
  redis:
    database: 0
    password:    # 密码（默认为空）
    timeout: 60000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接

# 是否开启redis缓存  true开启   false关闭
model:
  redis:
    open: true

# mybatis配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.yangzh.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型
      id-type: ASSIGN_ID
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  configuration-properties:
    prefix:
    blobType: BLOB
    boolValue: TRUE

redis:
  base:
    user:
      key: web:userInfo
    token:
      key: web:token

system:
  password: 888888
  jwt:
    secret: 3dnq4x68asx8ax89alda96a9pq3f0c5s23u2h436vq2m9t8d14up85dfs7m6c93q
  # rsa
  rsa:
    privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDha19FWcUxTfh5pB+3OF5LnAistQYfexn0UNau0HcgczkmKZ692X9tZvZnNiOZIMz/DobDAsjTQQIXCKjxR/eWFSr3Xq5+rTFaS4rrargqpj55rWsAuLZGBEmorGy9OWZFSiZpyLKhGhvaVtF7MPJrX8uX4oftOAyXRkWvecgnRrjKiJnltSpz6tXOBhTTTfoO8/+1MGbhtltYr1ttO5ZjHB6qja/5LzMPwV9rKNwNrmi2KWWDudonX9ZppyMjW3YMOl9nRnUBxlCJ66A2uDPIZknXO0uoFpln8O493JP23T9riWH34wx+wJjP25xrtckhtR0pN/80lzVxRMSOHXe1AgMBAAECggEBAL2NMWa6rxPfsdJvzW+fScOgqLa53jU4qcGE/JGxoIq7+VF67A5Ox1VyoD54CwUnzNmpGG8Bd2gVITHHSXDjCHG+yQuMJYVGE9sO7Pk7GzRZ6O0eg3zu1e8Yiev5WdLgYnxfdt+7MWJA9yzplmtV5HOf8S5+AbjbSdMm91AwQruqq5GmFtu9bPLoiTr44ardOjS8HOUuPjQ0UQpGTYafU12yBmFJ8qKhT4ULCfinXkDnGOm7HeeQ9DznWlcUV0vVOiSl+4d9KBfVt8PPFuYl/AP7lsTgv6f5zFxhT0sYj2n2vACTHP8G16wXlcV7G4hZgDB/wHg1M8PwyXbuOs2RPyECgYEA8lTPCfyLvSTwo76l8Xl7TeZACKgR/y241TV5StDhIdc1+r0+WqSuYM+bIlEcdjp+Ej55hCxTlCzImThjqPRFkkIZGtyhmgAZWTcipysfYcZbts2laCP0cA4EDyTmb+/6eyctZFSxS79sM5eCSaKKa24gYoLs/1DpKy6MCP3dNVkCgYEA7iJcdmnO+9JByyPku091rVf+5pY4fdTW+c7yolJLG2vAXTUL3WFOVvYt1XAS6mRzm5dKBNjiZKEljEEMyAQKE6o+ZwkiJGmISdJ43+YjMegRAAFxyF2MEa2D2eXBsBYJyHSoCXa/nRqfklEXLte7fWRZr94xHaki/6D2iRaSHb0CgYEAtoiFTztosrdhf6W6b3KXG21lLGAU9kalZAwcaet+uTPXWCA2GKbVJt0aOLYqHzAeCHYAqDuPVRyKmUctYnVk9i9No7svCsYqUdZDBvJneAmmycKLZi83DzSvgAzGeuijnyI6gcl9ptXwlcf4/qgtZZCVWj7Ob96tU7aDCr7WYjkCgYB4ggECl4b8XqmW8GIW/B4UC7zd2dVasDPeFQEqbbS+eqfNQ0Pu0wg+77PAGEgqP41VhpkZnNvsirQipuSd75Vd4EBWCgQyIwmqOg2bWK4k2kiIFTMMb3Krm4Bm62M6Bd0LiSVkLKjJCykHdWvi859Y1EiivDFkziv86ur38ukcjQKBgAFu/Of3z1lYS9eZ0gV69Z17PJuolh6f9n5O8/Kt7XhRsONMmxtw6KlMC7J3RgbFF11TwtPC4OYOgVeLbESONuFY3DHp3JZrk+8/gUpAKptwHFWEnjRz51V+H/86oIJBMCcrl1ztwxJcWH8E5EEFa0t92vLxo7E6Vo51+dSHYnuV

jasypt:
  encryptor:
    # 加密方式
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator

#邮箱配置
mail:
  code:
    codeExpirationSeconds: 300 #验证码在300秒内使用都有效
    emailLockDurationSeconds: 30 #发一次至少要等30秒才能再发一次验证码
    validateLockDurationSeconds: 2 #得到验证码后，每2秒只能尝试用一次
    size: 4
    characterSet: "`~1!2@3#4$5%6^7&8*9(0)-_=+qwertyuiop[{]}\\|asdfghjkl;:'\"zxcvbnm,<.>/?"
    subject: 验证码
    template: |
      验证码是
      {{code}}

  # 是否启用邮箱扫描定时任务
  enable: false
  smtp:
    # 指定发邮件服务器地址
    host: smtp.chinamobile.com
    # 端口
    port: 465
    # 默认编码
    default-encoding: UTF-8
    # 使用的协议
    protocol: smtp
    # 登录账户
    username: <EMAIL>
    # 登录密码
    password: ENC(FGA/jRG0qk1K5UZ7w63010rWHmEojOu3iaYO4A9N+0U=)
  # 收件配置
  imap:
    host: imap.chinamobile.com
    # 文件夹
    folder: inbox
    # 端口
    port: 993
    # 使用的协议
    protocol: imap
    ssl:
      enable: true
    username: <EMAIL>
    # 登录密码
    password: ENC(FGA/jRG0qk1K5UZ7w63010rWHmEojOu3iaYO4A9N+0U=)
sms:
  vfcode:
    codeExpirationSeconds: 300 #验证码在300秒内使用都有效
    mobileLockDurationSeconds: 30 #发一次至少要等30秒才能再发一次验证码
    validateLockDurationSeconds: 2 #得到验证码后，每2秒只能尝试用一次
    characterSet: "0123456789"
    size: 6
    template: |
      【某某公司X系统】验证码是{{code}}(切勿转发或告知他人确保您的账号安全)，您正在使用短信验证码登录功能，5分钟有效请在页面中输入已完成验证


#角色
user:
  role:
    #普通用户角色ID
    ordinaryId: 1715250455671054337

minio:
  # 调整分块缓冲大小（按服务器内存调整）建议4M，注意超过2047MB会内存溢出
  download:
    buffer-size: 2MB
  secure: false   # 是否HTTPS
  region: ""      # 区域留空（老版本兼容）

  
  # PageHelper 核心配置
pagehelper:
  helper-dialect: postgresql  # 数据库类型设置为 PostgreSQL（兼容 GaussDB）
  reasonable: true            # 合理化页码（超过总页数返回最后一页）
  support-methods-arguments: true  # 支持接口参数传递分页
  params: count=countSql      # COUNT查询参数别名
  auto-runtime-dialect: true  # 多数据源时可自动检测方言