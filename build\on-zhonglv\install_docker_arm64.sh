#!/bin/bash

set -e

DOCKER_VERSION=24.0.9

echo "1. 下载 Docker 二进制包..."
wget -O docker.tgz https://download.docker.com/linux/static/stable/aarch64/docker-${DOCKER_VERSION}.tgz

echo "2. 解压并安装 Docker..."
tar -xvf docker.tgz
sudo cp docker/* /usr/bin/

echo "3. 创建 systemd 服务文件..."
sudo tee /etc/systemd/system/docker.service > /dev/null <<EOF
[Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network-online.target firewalld.service
Wants=network-online.target

[Service]
Type=notify
ExecStart=/usr/bin/dockerd
ExecReload=/bin/kill -s HUP \$MAINPID
TimeoutSec=0
RestartSec=2
Restart=always

StartLimitBurst=3
StartLimitInterval=60s

LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity
TasksMax=infinity
Delegate=yes
KillMode=process
OOMScoreAdjust=-500

[Install]
WantedBy=multi-user.target
EOF

echo "4. 重新加载 systemd 并启动 Docker..."
sudo systemctl daemon-reload
sudo systemctl start docker
sudo systemctl enable docker

echo "5. 检查 Docker 版本..."
docker version

echo "6. 安装 buildx 插件..."
mkdir -p ~/.docker/cli-plugins
curl -SL https://github.com/docker/buildx/releases/latest/download/buildx-linux-arm64 -o ~/.docker/cli-plugins/docker-buildx
chmod +x ~/.docker/cli-plugins/docker-buildx

echo "7. 检查 buildx 版本..."
docker buildx version

echo "8. 创建 buildx 实例..."
docker buildx create --use --name mybuilder --driver docker-container --driver-opt image=m.daocloud.io/docker.io/moby/buildkit:buildx-stable-1

echo "9. 使用 buildx 实例..."
docker buildx use mybuilder

echo "全部完成！你现在可以使用 docker 和 buildx 了。"