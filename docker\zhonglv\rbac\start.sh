#!/bin/bash
# rbac服务启动脚本

# 环境变量设置
encryptor_password=6hsZRDhWLsEZ68niWNOjueKoZgI8LrK3TE90PJCZ4jXpx3qyIiR1ZsLUHx1XncjQ
file_path=/app/app.jar
profiles_active=prod
debug=true
logging_level_root=INFO
server_port=8080    
server_servlet_context_path=/
LOG_CONFIG_FILE=/app/config/logback-spring.xml
CONFIG_FILE=/app/config/config.properties
APP_NAME=rbac


# 应用日志
LOG_DIR="/app/logs"
mkdir -p $LOG_DIR

# 创建日志目录（旧目录兼容）
mkdir -p /app/log
chmod 777 /app/log

# 启动应用
echo "$(date) - 启动 $APP_NAME 服务..." >> $LOG_DIR/startup.log

echo "当前目录：$(pwd)"
echo "当前文件：$(basename $0)"
# 递归展示/app目录下所有的目录及文件
echo "递归展示/app目录下所有的目录及文件：ls -R /app"
ls -R /app

# 读取配置文件
echo "读取配置文件$CONFIG_FILE"

echo "--------------------------------"
if [ -f "$CONFIG_FILE" ]; then
  echo "原始配置文件内容："
  cat $CONFIG_FILE
else
  echo "配置文件 $CONFIG_FILE 不存在"
fi
echo "--------------------------------"

# 判断配置文件是否存在，如果不存在，则不读取内容，也不在启动命令后添加启动参数
PROPS_ARGS=""
if [ ! -f "$CONFIG_FILE" ]; then
  echo "配置文件 $CONFIG_FILE 不存在,不读取配置文件内容"
  PROPS_ARGS=""
else
  # 使用grep过滤注释行和空行，然后转换为启动参数
  while IFS= read -r line; do
    # 忽略空行和注释行
    if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# ]]; then
      PROPS_ARGS="$PROPS_ARGS --$line"
    fi
  done < <(grep -v "^#" $CONFIG_FILE | grep -v "^[[:space:]]*$")
fi

echo "转换后的启动参数： $PROPS_ARGS"

# 如果PROPS_ARGS为空，则不添加启动参数
STARTUP_CMD="java -Xmx256m -Ddebug=$debug \
  -Dlogging.level.root=$logging_level_root \
  -Djasypt.encryptor.password=$encryptor_password \
  -Dspring.profiles.active=$profiles_active \
  -jar $file_path \
  --server.port=$server_port \
  --server.servlet.context-path=$server_servlet_context_path \
  --logging.config=$LOG_CONFIG_FILE"

if [ ! -z "$PROPS_ARGS" ]; then
  STARTUP_CMD="$STARTUP_CMD $PROPS_ARGS"
fi

echo "启动命令： $STARTUP_CMD"

# 执行启动命令
eval $STARTUP_CMD

# 获取启动结果
RESULT=$?
if [ $RESULT -eq 0 ]; then
  echo "$(date) - $APP_NAME 服务启动成功!" >> $LOG_DIR/startup.log
else
  echo "$(date) - $APP_NAME 服务启动失败，退出码: $RESULT" >> $LOG_DIR/startup.log
fi 