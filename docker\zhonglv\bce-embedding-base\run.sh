#!/bin/bash

# 错误时退出
set -e

echo "开始启动bce-embedding-base服务..."
pwd
# 获取当前目录
current_dir=$(pwd)
APP_PATH=/workspace/mnt/bce-embedding-base_v1
SERVICE_PATH=$APP_PATH/service

echo "当前目录: $current_dir"
echo "树状展示$APP_PATH目录下的文件和子目录:"

# 打印当前目录下的所有文件和子目录
ls -R $APP_PATH

# 创建日志目录
if [ ! -d "$SERVICE_PATH/logs" ]; then
    mkdir -p $SERVICE_PATH/logs
    echo "创建日志目录: $SERVICE_PATH/logs/"
fi

# 设置代理服务器地址
PROXY_HOST="************"
PROXY_PORT="8888"
PROXY_URL="http://${PROXY_HOST}:${PROXY_PORT}"
echo "设置代理: $PROXY_URL"

# 检查与代理服务器的连接
echo "正在检查与代理服务器的连接..."
if curl -s --connect-timeout 5 -x $PROXY_URL http://www.baidu.com > /dev/null; then
    echo "代理服务器连接成功!"
    # 设置环境变量
    export http_proxy=$PROXY_URL
    export https_proxy=$PROXY_URL
    PROXY_ENABLED=true
else
    echo "警告: 无法连接到代理服务器 $PROXY_URL"
    echo "将直接尝试安装依赖"
    PROXY_ENABLED=false
fi

# 检查依赖是否安装
echo "检查并安装依赖..."
if [ "$PROXY_ENABLED" = true ]; then
    echo "使用代理安装依赖..."
    pip install -r $SERVICE_PATH/requirements.txt --proxy=$PROXY_URL || \
    pip install -r $SERVICE_PATH/requirements.txt --proxy=$PROXY_URL -i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn
else
    echo "直接安装依赖..."
    pip install -r $SERVICE_PATH/requirements.txt || \
    pip install -r $SERVICE_PATH/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn
fi

# 清除代理环境变量
if [ "$PROXY_ENABLED" = true ]; then
    echo "清除代理环境变量..."
    unset http_proxy https_proxy
fi

# 检查模型路径环境变量
# 未指定就用上一级目录的models
export MODEL_PATH=$APP_PATH
echo "MODEL_PATH: $MODEL_PATH"

# 启动服务
echo "启动HTTP服务..."
python $SERVICE_PATH/app.py

# 验证服务
echo "验证服务： curl -X POST http://localhost:8000/embeddings -H \"Content-Type: application/json\" -d '{\"texts\": [\"你好\"]}'"
curl -X POST http://localhost:8000/embeddings -H "Content-Type: application/json" -d '{"texts": ["你好"]}'

# 注意: 此脚本需要执行权限
# 使用以下命令添加执行权限:
# chmod +x run.sh 