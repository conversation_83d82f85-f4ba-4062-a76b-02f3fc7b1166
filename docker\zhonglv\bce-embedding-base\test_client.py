#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

# 服务器地址
SERVER_URL = "http://localhost:5000"

def test_health():
    """测试健康检查接口"""
    url = f"{SERVER_URL}/health"
    response = requests.get(url)
    print(f"健康检查结果: {response.status_code}")
    print(response.json())
    print("-" * 50)

def test_single_embedding():
    """测试单个文本的嵌入接口"""
    url = f"{SERVER_URL}/embed"
    payload = {"text": "这是一个测试文本，用于生成文本嵌入向量。"}
    headers = {"Content-Type": "application/json"}
    
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    print(f"单文本嵌入结果状态码: {response.status_code}")
    data = response.json()
    print(f"文本: {data['text']}")
    print(f"嵌入维度: {data['dimensions']}")
    print(f"嵌入向量前5个元素: {data['embedding'][:5]}...")
    print("-" * 50)

def test_batch_embedding():
    """测试批量文本的嵌入接口"""
    url = f"{SERVER_URL}/embed_batch"
    payload = {
        "texts": [
            "第一个测试文本",
            "第二个不同的测试文本",
            "第三个更长的测试文本，包含更多的信息和上下文。"
        ]
    }
    headers = {"Content-Type": "application/json"}
    
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    print(f"批量文本嵌入结果状态码: {response.status_code}")
    data = response.json()
    
    for i, result in enumerate(data["results"]):
        print(f"文本 {i+1}: {result['text']}")
        print(f"嵌入维度: {result['dimensions']}")
        print(f"嵌入向量前5个元素: {result['embedding'][:5]}...")
        print()
    
    print("-" * 50)

if __name__ == "__main__":
    print("开始测试BCE Embedding服务...")
    test_health()
    test_single_embedding()
    test_batch_embedding()
    print("测试完成!") 