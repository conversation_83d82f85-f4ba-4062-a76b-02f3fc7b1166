#设置基础镜像☆☆☆☆☆☆☆☆☆☆根据工程运行需要配置带有相应环境的运行用途镜像☆☆☆☆☆☆☆☆☆☆
FROM artifactory.dep.devops.cmit.cloud:20101/tools/base-images/openjdk:alpine AS final
#配置工作目录
ARG JAR_FILE
ARG APP_NAME
ARG APP_DIR

# 安装必要的工具包并创建目录
RUN apk add --no-cache bash shadow tzdata netcat-openbsd curl && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    mkdir -p /app /app/logs /app/config /app/files /app/shells /home/<USER>

# 设置中文环境变量
ENV LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8

# 创建用户和组 (Alpine使用addgroup和adduser)
RUN addgroup -g 1000 -S wensi && \
    adduser -u 1000 -S wensi -G wensi -h /home/<USER>/bin/bash && \
    chmod -R 755 /app/ && \
    chown -R wensi:wensi /app/ && \
    chown -R wensi:wensi /home/<USER>/

WORKDIR /app
RUN echo "将应用JAR文件复制到容器中，JAR文件: ${JAR_FILE}"

# 复制应用和配置文件
COPY app.jar /app/app.jar
COPY config/ /app/config/
COPY shells/ /app/shells/

# 创建启动脚本，确保使用Unix行结束符和正确的执行权限
RUN echo '#!/bin/bash' > /app/shells/start.sh && \
    echo 'java -jar /app/app.jar --spring.config.location=/app/config/ --server.port=8080 --server.servlet.context-path=/ --server.context.path=/' >> /app/shells/start.sh && \
    chmod +x /app/shells/start.sh && \
    cat /app/shells/start.sh

USER wensi
CMD ["/bin/bash", "/app/shells/start.sh"]