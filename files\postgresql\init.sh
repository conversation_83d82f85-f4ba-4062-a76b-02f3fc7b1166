#!/bin/bash
set -e

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# 配置参数
APP_DIR=${1:-$(pwd)}
POSTGRES_USER=${POSTGRES_USER:-admin}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-PgSQL@2025}

echo -e "${BLUE}=== PostgreSQL 数据库初始化脚本 ===${NC}"

# 检查容器状态
if ! docker ps | grep -q "postgres-server"; then
  echo -e "${RED}错误: PostgreSQL 容器未运行${NC}"
  exit 1
fi

# 等待PostgreSQL完全启动
echo -e "${YELLOW}等待PostgreSQL完全启动...${NC}"
for i in {1..30}; do
  if docker exec postgres-server pg_isready -U ${POSTGRES_USER} > /dev/null 2>&1; then
    echo -e "${GREEN}PostgreSQL 已准备就绪${NC}"
    break
  fi
  echo -e "${YELLOW}等待PostgreSQL启动，尝试 $i/30...${NC}"
  sleep 2
  if [ $i -eq 30 ]; then
    echo -e "${RED}PostgreSQL 未能及时启动，请检查日志${NC}"
    docker logs postgres-server | tail -n 20
    exit 1
  fi
done

# 执行SQL脚本
echo -e "${YELLOW}执行SQL脚本...${NC}"
if [ -f "${APP_DIR}/schema/postgresql.sql" ]; then
  echo -e "${GREEN}发现SQL脚本: ${APP_DIR}/schema/postgresql.sql${NC}"
  
  # 方法1: 使用容器内文件（如果已经挂载）
  if docker exec postgres-server ls /etc/postgresql/postgresql.sql > /dev/null 2>&1; then
    echo -e "${YELLOW}使用容器内文件执行SQL脚本...${NC}"
    docker exec -i postgres-server psql -U ${POSTGRES_USER} -d postgres -f /etc/postgresql/postgresql.sql
  else
    # 方法2: 使用管道传输本地文件内容到容器
    echo -e "${YELLOW}使用管道传输SQL脚本到容器...${NC}"
    cat "${APP_DIR}/schema/postgresql.sql" | docker exec -i postgres-server psql -U ${POSTGRES_USER} -d postgres
  fi
  
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}SQL脚本执行成功${NC}"
  else
    echo -e "${RED}SQL脚本执行失败${NC}"
    exit 1
  fi
  
else
  echo -e "${RED}警告: SQL脚本 ${APP_DIR}/schema/postgresql.sql 不存在${NC}"
  
  # 查找其他SQL文件
  echo -e "${YELLOW}查找其他SQL文件...${NC}"
  SQL_FILES=$(find "${APP_DIR}/schema" -name "*.sql" 2>/dev/null)
  if [ -n "$SQL_FILES" ]; then
    echo -e "${GREEN}找到以下SQL文件:${NC}"
    echo "$SQL_FILES"
    
    # 执行找到的第一个SQL文件
    FIRST_SQL=$(echo "$SQL_FILES" | head -n 1)
    echo -e "${YELLOW}执行文件: $FIRST_SQL${NC}"
    cat "$FIRST_SQL" | docker exec -i postgres-server psql -U ${POSTGRES_USER} -d postgres
    
    if [ $? -eq 0 ]; then
      echo -e "${GREEN}SQL脚本执行成功${NC}"
    else
      echo -e "${RED}SQL脚本执行失败${NC}"
      exit 1
    fi
  else
    echo -e "${RED}未找到任何SQL文件${NC}"
  fi
fi

# 验证数据库表是否创建成功
echo -e "${YELLOW}验证数据库表...${NC}"
TABLES=$(docker exec -i postgres-server psql -U ${POSTGRES_USER} -d postgres -t -c "\dt")
echo -e "${GREEN}数据库已创建的表:${NC}"
echo "$TABLES"

if docker exec -i postgres-server psql -U ${POSTGRES_USER} -d postgres -c "\dt" | grep -q "knowledge_base\|document\|document_chunk"; then
  echo -e "${GREEN}数据库表创建成功${NC}"
else
  echo -e "${YELLOW}未发现预期的表结构，但不一定表示错误${NC}"
fi

echo -e "${BLUE}=== 数据库初始化完成 ===${NC}" 