# AI Writer NoStream Python

基于非流式输出的自动文章生成系统的Docker部署方案

## 项目结构

```
ai-writer-nostream-python/
├── app/                       # 项目源代码
│   ├── temp/                  # 应用源码
│   │   ├── web_source_code/   # Web应用代码
│   │   │   ├── backend/       # 后端代码
│   │   │   │   ├── application.properties  # 配置文件
│   │   │   │   └── writing_template/       # 写作模板目录
│   │   │   ├── log/           # 日志目录 
│   │   │   └── manage.py      # Django管理脚本
├── Dockerfile                 # Docker镜像定义
├── application.properties     # 配置文件
├── deploy.sh                  # 部署脚本
├── restart.sh                 # 重启脚本
├── start.sh                   # 容器内启动脚本
├── test_curl.sh               # API测试脚本
└── requirements.txt           # 项目依赖
```

## 快速部署

### 方法1：使用部署脚本

```bash
# 添加执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

部署脚本会执行以下操作：
- 创建项目目录结构
- 复制配置文件和启动脚本到相应位置
- 构建Docker镜像
- 启动容器
- 设置日志和数据目录权限
- 执行API测试脚本

### 方法2：使用重启脚本（容器已存在）

```bash
# 添加执行权限
chmod +x restart.sh

# 运行重启脚本
./restart.sh
```

重启脚本会执行以下操作：
- 反向复制配置和脚本文件到构建目录
- 停止并删除现有容器
- 使用已有的镜像重新启动容器
- 设置日志和数据目录权限
- 执行API测试脚本

## 配置说明

配置文件位于 `application.properties`，包含以下主要配置项：
- 模型配置（URL, APPID, APPKEY, 能力名称）
- API密钥和模型名称
- 文件路径配置（敏感词文件、规则文件、模板目录）
- 生成参数配置（最大token数、温度）
- 系统提示词

配置文件通过卷挂载方式映射到容器内部，可以直接在宿主机上修改配置文件，无需重新构建镜像。

## 目录挂载

- 配置文件：`/data/ai-writer/ai-writer-nostream-python/config/application.properties:/app/temp/web_source_code/backend/application.properties`
- 日志目录：`/data/ai-writer/ai-writer-nostream-python/logs:/app/temp/web_source_code/log`
- Shell脚本目录：`/data/ai-writer/ai-writer-nostream-python/shells:/app/shells`

## 端口说明

应用默认在以下端口提供服务：
- 容器内部：8080
- 宿主机映射：8096

可以通过修改 deploy.sh 或 restart.sh 中的 PORT 变量来更改宿主机映射端口。

## API测试

项目包含一个测试脚本 `test_curl.sh`，用于验证API功能。该脚本测试两种请求方式：
- 简单请求：仅包含text字段的基本请求
- 复杂请求：包含extension扩展字段的请求，可提供更多文档生成信息

测试命令示例：
```bash
./test_curl.sh
```

请求API示例：
```
POST /mubanwriter/v1/service
```

## 网络配置

容器默认使用 `jiutianwensi` 网络，可以根据实际环境需要在部署脚本中修改。 