#!/bin/bash
# 部署根目录设置
echo "部署根目录设置"
DEPLOY_DIR="/data/nginx"
echo "创建三级目录结构"
mkdir -p ${DEPLOY_DIR}/{conf,log,html}
echo "设置权限"
chmod -R 755 ${DEPLOY_DIR}  # 创建三级目录结构:ml-citation{ref="3,7" data="citationList"}

# 创建包含auth_request模块的nginx配置文件
echo "获取最新镜像（DaoCloud 加速源）"
LATEST_TAG="1.27.4"  # 2025年4月官方最新稳定版本:ml-citation{ref="3,7" data="citationList"}
echo "拉取镜像"
docker pull m.daocloud.io/docker.io/nginx:${LATEST_TAG}
echo "删除容器"
docker rm -f nginx
echo "启动容器"
docker run -d -p 30912:80 -p 30913:8092 -p 6379:6379 -p 5672:5672 -p 2181:2181 --name nginx -v /data/nginx/conf/nginx.conf:/etc/nginx/nginx.conf  -v /data/nginx/html:/usr/share/nginx/html -v /data/nginx/log:/var/log/nginx  m.daocloud.io/docker.io/nginx:1.27.4
echo "启动成功"
echo "查看容器状态"
docker ps | grep nginx
echo "查看日志"
docker logs  nginx