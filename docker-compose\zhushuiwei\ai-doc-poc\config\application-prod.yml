server:
  port: 8080
  servlet:
    context-path: /ai-doc-poc
  shutdown: graceful

spring:
  application:
    name: ai-doc
  web:
    resources:
      static-locations: classpath:/
  servlet:
    multipart:
      location: /app/files
      max-request-size: 100MB #总文件大小
      max-file-size: 100MB #单个文件大小
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss.SSS
    time-zone: GMT+8
    defaultPropertyInclusion: non_null # always #非空属性才序列化
    deserialization:
      fail_on_unknown_properties: false #未定义的key不序列化

#配置文件加密
jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    ivGeneratorClassname: org.jasypt.iv.NoIvGenerator
    password: EbfYkitulv73I2p0mXI50JMXoaxZTKJ7

# 变更说明：原ai-service.providers.id 指移动分配应用ID。 现在大模型提供了uat环境，可能存在uat和生产环境APPID不一致情况。
#  改造为： 追加mobile-app-id作为移动分配应用ID，根据需要其值可从POM文件中根据环境获取，也可以写为固定值。
ai-service:
  providers:
    - id: square # 功能需要见CheckServiceEnum
      # 移动分配的应用Id
      mobile-app-id: square
      name: 浙江核稿大模型
      scene: REVIEW
      spi-impl: squareSpi
      base-url: http://ai-redactor:8000/spellcheck/oa
      app-id: ENC(qLwxCd+kGLASvxFMi2EonA==)
      app-key: ENC(+svIC/lQsgLR+UqbodRkzBpRUlF9/FLw1MK2rRrlRwnEdTc2W7f1OFvYK+Jg//gMwr8gRey2fvcVaLjA5snmIJtW0S54AN7kd4+AxQbo8ce0YcMaa7AIoflf+WUIBTrr176pCfsKSiaMi3lNCw2D5zJF2zcfOMosqS/SKDEpctlb0x5d5du2w9faAyIMYfp79NdU14fzryakfNuz6XmWTyKq/to2yc+Fk5aMSnzV8g5HfdsiZBPjqn71FSJNUWzxcc6ejch4Vm2NhYoQmkEJCgojE0+1fGk20gmZPGpEwQp04oQnX5blc3J+yFhgajxeHB9ejjw5DomiGN9cIgaAsGHlSNe1IIDBbnpAeOYKmHnp1PoNCP+LBe5xXQ7RMmCyJklx04d6SvadYWIj/1CqSsI1lTriiIFgY5rv4r75IFeYpgMU8xKUfM7xenPDFUzypLywIFb/EGey/uKGKuMZayJphrUav906+Hkb75Cty4p144PsUI6fV21/yCNXp7WV4KN9dLG/SilNxL4wG94Pk8YiG+hHKV8hpmuvW6Im6hBi+vM8I9abeJLxvb9jGDTVkFhPIyKv1lI7LbnUYksxvvmgr3zlBqgFnYTax8hNiVU1DyX9h1vwKeDUeT9bPqyWP0H28l6EUjikz72aqJx158KMggL97U016t+W1l/45chsJIhi+d4OmXCrnm2YFG6SekVE6N2AkF1ddIqclePzqfd1xSOBxPdzXH223+fCtO/9GX/WAq3pxRH0uKgwXQXlU+HCvf4uVAN9gE5oOn0tWtCwY9K7XTD4ZUpAYK7Y6/kl0ibyGAN8+GqBAgOLr2JQnetPvufCFJNQF/zW7A204L0ssLptEwkrywqLlkueykpCct/MGxMzXA91BYj7InyVe/x+3nj+Gfmg45IgF2/80//igN3oWkeA3wqjbyKxc0q1xw8ODtbryoa0bPMSvE9DDMCcJH2o90m30tbdyqKcROX5I/itXqLI)
      ext-params:
        record-distribute-url: ENC(F8/RkAwu8xjc/ScQ1cmFB/cJKuNaQ4MSHY2CtoqgYI5rUCYiI690xhmb2DflNBwA)
        acquire-token-sys-code: ENC(PQt66HNANhdsyKa8snj3Vg==)
        acquire-token-url: ENC(hRUBOcjLSMUDjdZ1nOgu3vTdhATbjx5PiWnqUdzZd4FTpwrx6K0hxguuJ8rypgZb4fXmW6LZzPC9CdoivtYjJ24eqU3aNfT4xJ1W/0y5jB/QWwcYoJd7/qrcmH4V9/Mh)
    - id: pncheck #功能需要自定义了该值 见CheckServiceEnum
      # 平能大模型无移动分配ID
      mobile-app-id: ""
      name: 平能核稿大模型
      scene: REVIEW
      spi-impl: balanceAbilitySpi
      base-url: http://ai-redactor:8000/spellcheck/oa
      app-id: llmz-zhl
      app-key: 551a68b15b753d4bd0789f1527cc64b7
      ext-params:
        record-distribute-url: ""

# 错误类型映射
error-type:
  # 平能大模型与通用(浙江)错误码的映射
  balanceability: '{"检查常见字词错误": 1, "检查专有名词错误": 1, "检查标点符号错误": 2, "检查日期错误": 101, "检查数字单位类错误": 102, "检查语法错误": 106, "检查结构层次标注错误": 106, "检查引用格式错误": 109, "检查格式错误": 109}'

moa:
  check:
    # moa端核稿使用的大模型id. 值需要为CheckServiceEnum枚举.
    checkUnitId: SQUARE

logging:
  config: /app/config/logback.xml
