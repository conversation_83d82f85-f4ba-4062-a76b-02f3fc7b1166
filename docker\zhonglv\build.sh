docker pull m.daocloud.io/docker.io/zookeeper:3.9.3
mkdir -p /data/zookeeper/data
docker run -itd --name zookeeper  -p 2181:2181 -v /data/zookeeper/data:/data -e TZ="Asia/Shanghai" --restart always m.daocloud.io/docker.io/zookeeper:3.9.3

docker exec -it zookeeper bash ./bin/zkCli.sh -server localhost:2181

#rabbitmq
docker pull m.daocloud.io/docker.io/rabbitmq:3.12-management  # 推荐带管理插件的镜像，支持Web控制台‌:ml-citation{ref="1,6" data="citationList"}

# 创建持久化目录（防止数据丢失）
mkdir -p /data/rabbitmq/{data,conf,log} && chmod -R 777 /data/rabbitmq

# 启动容器
docker run -itd --name rabbitmq -p 5672:5672 -p 15672:15672 -v /data/rabbitmq/data:/var/lib/rabbitmq -v /data/rabbitmq/log:/var/log/rabbitmq -e RABBITMQ_DEFAULT_USER=admin   -e RABBITMQ_DEFAULT_PASS=admin123  --restart always  m.daocloud.io/docker.io/rabbitmq:3.12-management


docker pull m.daocloud.io/docker.io/redis:7.4.1  # 推荐使用最新稳定版‌:ml-citation{ref="1,3" data="citationList"}
# 创建持久化目录和配置文件
mkdir -p /data/redis/{conf,data} && chmod -R 777 /data/redis‌
touch /data/redis/conf/redis.conf  # 创建空配置文件‌:ml-citation{ref="1,5" data="citationList"}
# 创建配置文件并写入配置内容（一步完成）
cat << EOF > /data/redis/conf/redis.conf
bind 0.0.0.0                  
protected-mode no             
requirepass admin123      
appendonly yes                
EOF
docker run -d --name redis -p 6379:6379   -v /data/redis/conf/redis.conf:/etc/redis/redis.conf -v /data/redis/data:/data   --restart always m.daocloud.io/docker.io/redis:7.4.1 redis-server /etc/redis/redis.conf 
docker ps | grep redis
docker exec -it redis redis-cli -a admin123  
> set test "hello"  
> get test


mkdir -p /data/nginx/{conf,log,html}
chmod -R 755 /data/nginx‌
cat << EOF > /data/nginx/conf/nginx.conf
# 基础配置
events {
    worker_connections  1024;
}

http {
    # 解析域名时需要（可选）
    resolver *******;

    # 通用代理配置（复用配置）
    proxy_set_header Host               $host;
    proxy_set_header X-Real-IP          $remote_addr;
    proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  $scheme;

    # 示例1：代理 app1.example.com 到本地3000端口
    server {
        listen        80;
        server_name   app1.example.com;

        location / {
            proxy_pass http://localhost:3000;  # 你的第一个后端服务地址
            # 如需WebSocket支持取消以下注释
            # proxy_http_version 1.1;
            # proxy_set_header Upgrade $http_upgrade;
            # proxy_set_header Connection "upgrade";
        }
    }

    # 示例2：代理 app2.example.com 到另一组服务器
    server {
        listen        80;
        server_name   app2.example.com api.example.com;  # 支持多域名绑定

        location / {
            proxy_pass http://backend_servers;  # 可指向负载均衡池
            # 健康检查配置示例（需Nginx Plus或第三方模块）
            # health_check interval=5s;
        }
    }

    # 负载均衡池示例（可选）
    upstream backend_servers {
        server ********:8080;
        server ********:8080;
    }

    # 默认处理未知域名请求（可返回404或重定向）
    server {
        listen 80 default_server;
        server_name _;
        return 404;  # 或 redirect到指定页面
    }
}
EOF


docker rm -f nginx
docker run -d -p 30912:80 -p 6379:6379 -p 5672:5672 -p 2181:2181 --name nginx -v /data/nginx/conf/nginx.conf:/etc/nginx/nginx.conf  -v /data/nginx/html:/usr/share/nginx/html -v /data/nginx/log:/var/log/nginx  m.daocloud.io/docker.io/nginx:1.27.4


docker pull m.daocloud.io/docker.io/mongo:8.0.5
mkdir -p /data/mongo/{conf,data}
docker rm -f mongodb
docker run -itd --name mongodb -p 27017:27017  -v /data/mongo/data:/data/db -e MONGO_INITDB_ROOT_USERNAME=admin -e MONGO_INITDB_ROOT_PASSWORD=your_password m.daocloud.io/docker.io/mongo:8.0.5 --auth

docker pull m.daocloud.io/docker.io/erlang:25
# 创建宿主机目录（示例路径，可自定义）
mkdir -p /data/erlang/{logs,config}  
chmod -R 755 /data/erlang  # 确保容器有写入权限
docker run -itd --name erlang-app -v /data/erlanglogs:/opt/app/logs   -v /data/erlang/config:/opt/app/config   m.daocloud.io/docker.io/erlang:25  

mkdir -p /data/docker
mv /var/lib/docker/* /data/docker/
rm -rf /var/lib/docker
ln -s /data/docker /var/lib/docker

systemctl start docker
docker info | grep "Docker Root Dir"  # 应显示 /data/docker






