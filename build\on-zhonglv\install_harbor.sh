#!/bin/bash

set -e

HARBOR_VERSION="v2.10.0"
HARBOR_HOSTNAME="**********"   # 修改为你的服务器IP或域名
HARBOR_ADMIN_PASSWORD="Harbor12345"          # 修改为你想要的admin密码
CERT_DIR="/data/cert"

echo "1. 安装 Docker 和 Docker Compose..."
if ! command -v docker &>/dev/null; then
  curl -fsSL https://get.docker.com | bash
fi

if ! command -v docker-compose &>/dev/null; then
  curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
  chmod +x /usr/local/bin/docker-compose
fi

echo "2. 生成带 SAN 的自签名证书..."
mkdir -p $CERT_DIR
cat > $CERT_DIR/san.cnf <<EOF
[req]
default_bits = 4096
prompt = no
default_md = sha256
req_extensions = req_ext
distinguished_name = dn

[dn]
CN = ${HARBOR_HOSTNAME}

[req_ext]
subjectAltName = @alt_names

[alt_names]
IP.1 = ${HARBOR_HOSTNAME}
DNS.1 = ${HARBOR_HOSTNAME}
EOF

openssl req -x509 -nodes -days 3650 -newkey rsa:4096 \
  -keyout $CERT_DIR/server.key \
  -out $CERT_DIR/server.crt \
  -config $CERT_DIR/san.cnf \
  -extensions req_ext

echo "3. 下载 Harbor 安装包..."
# 如果不存在，则下载
if [ ! -f harbor-online-installer-${HARBOR_VERSION}.tgz ]; then
  wget -c https://github.com/goharbor/harbor/releases/download/${HARBOR_VERSION}/harbor-online-installer-${HARBOR_VERSION}.tgz
fi

echo "4. 解压 Harbor 安装包..."
# 如果没解压，则解压  
if [ ! -d harbor ]; then
  echo "解压 harbor 安装包..."
  tar xvf harbor-online-installer-${HARBOR_VERSION}.tgz
else
  echo "harbor 已解压，跳过解压"
fi
cd harbor
echo "5. 生成配置文件..."
# 如果 harbor.yml 不存在，则生成
if [ ! -f harbor.yml ]; then
echo "生成 harbor.yml 文件..."
cp harbor.yml.tmpl harbor.yml
sed -i "s/^hostname:.*/hostname: ${HARBOR_HOSTNAME}/" harbor.yml
sed -i "s/^  harbor_admin_password:.*/  harbor_admin_password: ${HARBOR_ADMIN_PASSWORD}/" harbor.yml

# 注释掉 http 相关配置，确保只用 https
sed -i '/^# http:/,/^#$/s/^/#/' harbor.yml
# 配置 https 字段
sed -i '/^# https:/,/^#$/d' harbor.yml
cat >> harbor.yml <<EOF

https:
  port: 443
  certificate: $CERT_DIR/server.crt
  private_key: $CERT_DIR/server.key
EOF

else
  echo "harbor.yml 已存在，跳过生成配置文件"
fi
echo "修改 docker-compose.yml 文件的 harbor-nginx 容器名..."
sed -i 's/container_name: nginx/container_name: harbor-nginx/g' docker-compose.yml
#查看修改结果
cat docker-compose.yml | grep "container_name: harbor-nginx"
echo "6. 先停止 Harbor 服务..."
docker-compose down
echo "7. 安装 Harbor..."
./install.sh


echo "9. 安装完成！"
echo "请用浏览器访问：https://${HARBOR_HOSTNAME}"
echo "默认用户名：admin"
echo "默认密码：${HARBOR_ADMIN_PASSWORD}"
echo "如用自签名证书推送/拉取镜像，请将 $CERT_DIR/server.crt 拷贝到各客户端 /etc/docker/certs.d/${HARBOR_HOSTNAME}/ca.crt 并重启docker"
