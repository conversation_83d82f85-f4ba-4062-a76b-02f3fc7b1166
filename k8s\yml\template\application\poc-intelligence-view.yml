apiVersion: v1
kind: Service
metadata:
  labels:
    cmcc-gitops-file-name: poc-intelligence-view.yaml
    cmcc-gitops-project-tag: oallm
  name: poc-intelligence-view
  namespace: oa-llm
spec:
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: poc-intelligence-view
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    cmcc-gitops-file-name: poc-intelligence-view.yaml
    cmcc-gitops-project-tag: oallm
  name: poc-intelligence-view
  namespace: oa-llm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: poc-intelligence-view
  serviceName: poc-intelligence-view
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: poc-intelligence-view
    spec:
      containers:
      - image: {{poc-intelligence-view.image-url}}
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 5
          tcpSocket:
            port: 80
          timeoutSeconds: 5
        name: poc-intelligence-view
        ports:
        - containerPort: 80
          protocol: TCP
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 20
          periodSeconds: 5
          tcpSocket:
            port: 80
          timeoutSeconds: 10
        resources: {}
        volumeMounts:
        - mountPath: /etc/nginx/nginx.conf
          name: poc-intelligence-view-config
          subPath: nginx.conf
        - mountPath: /test.txt
          name: poc-intelligence-view-config
          subPath: test.txt  
        - mountPath: /test.sh
          name: poc-intelligence-view-config
          subPath: test.sh                    
        - mountPath: /var/log/nginx/
          name: poc-intelligence-view-logs
      - args:
        - -c
        - /opt/filebeat/filebeat.yml
        - -e
        image: {{filebeat.image-url}}
        imagePullPolicy: Always
        name: filebeat
        resources: {}
        terminationMessagePath: /var/log/err.log
        volumeMounts:
        - mountPath: /opt/filebeat/filebeat.yml
          name: poc-intelligence-view-filebeat-cm
          subPath: filebeat.yml
        - mountPath: /poc-intelligence-view/data/logs 
          name: poc-intelligence-view-logs
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: poc-intelligence-view-logs
      - configMap:
          defaultMode: 420
          name: poc-intelligence-view-config
        name: poc-intelligence-view-config
      - configMap:
          defaultMode: 420
          name: poc-intelligence-view-filebeat-cm
        name: poc-intelligence-view-filebeat-cm
      imagePullSecrets:
      - name: oa-llm-imagepullsecret
        
  updateStrategy: {}
status:
  replicas: 0
  availableReplicas: 0
  currentReplicas: 0
  updatedReplicas: 0
  currentRevision: ""
  updateRevision: ""
  collisionCount: 0
  conditions: []
---
apiVersion: v1
data:
  nginx.conf: |-
    events {
        worker_connections  1000;
    }

    error_log  /var/log/nginx/error.log debug;

    http {
        include       mime.types;
        default_type  application/octet-stream;
        sendfile        on;
        keepalive_timeout  3000;

        log_format  main  '$remote_addr:$remote_port - $remote_user [$time_local] '
                              '"$request_method $request_uri $server_protocol" '
                              '$status $body_bytes_sent '
                              '"$http_referer" "$http_user_agent" "$upstream_addr" ';#"$request_body"
        access_log  /var/log/nginx/access.log main;
    
      

        limit_conn_zone $server_name zone=auth_conn:20m;
        #limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
        limit_req_zone $server_name zone=auth_req:20m rate=1r/s; #确定每个请求发起后的冷却周期


        server {
            listen       80;
            server_name  localhost;

            # 开启gzip压缩
            gzip on;
            gzip_min_length 1k;
            gzip_comp_level 6;
            gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
            gzip_vary on;
            gzip_disable "MSIE [1-6]\.";

            # 静态资源缓存设置
            location / {
                root   /usr/share/nginx/html;
                index  index.html index.htm;
                try_files $uri $uri/ /index.html;
                
                # 缓存设置
                expires 7d;
            }
            location ^~ /poc-intelligence-view/ {
                alias /usr/share/nginx/html/poc-intelligence-view/;
                index index.html;
                try_files $uri $uri/ /poc-intelligence-view/index.html;
            }
            # 核稿服务
            location /ai-doc-poc {
                proxy_pass http://ai-doc-poc.oa-llm:8080/ai-doc-poc;
                proxy_http_version 1.1;
                proxy_connect_timeout 3000s;
                proxy_read_timeout 3000s;
                proxy_send_timeout 3000s;
            }
        }

    }

  test.sh: |-
    #!/bin/bash
    # 颜色设置
    GREEN='\033[0;32m'
    BLUE='\033[0;34m'
    RED='\033[0;31m'
    YELLOW='\033[1;33m'
    NC='\033[0m' # 无颜色

    # 测试文本
    test_text="中蒙经济合作区位于内蒙古自治区二连浩特市，是中国与蒙古国共同建设的跨境经济合作区。该合作区规划面积约10平方公里，重点发展国际贸易、物流仓储、加工制造等产业。合作区建成后，将成为中蒙两国经贸合作的重要平台，促进区域经济一体化发展。"

    # ====================== 直连服务测试 ======================
    echo -e "${BLUE}=== 直连服务测试 ===${NC}"
    echo -e "${YELLOW}正在测试直连文档检查服务...${NC}"

    # 构建直连请求命令
    cmd="curl -v -X POST \
      -F \"file=@/test.txt;type=text/plain\" \
      -F \"accessMode=STANDALONE\" \
      -F \"checkUnitId=BALANCEABILITY\" \
      -F \"fileType=HTML\" \
      -F \"extension={\\\"title\\\":\\\"中蒙经济合作区建设进展\\\",\\\"mainSubmit\\\":[],\\\"copySubmit\\\":[]}\" \
      \"http://ai-doc-poc.oa-llm:8080/ai-doc-poc/api/v1/check\""

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行直连请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}直连请求耗时: $((end_time - start_time)) 秒${NC}"

    # ====================== 代理服务测试 ======================
    echo -e "${BLUE}=== 代理服务测试 ===${NC}"
    echo -e "${YELLOW}正在测试代理文档检查服务...${NC}"

    # 构建代理请求命令
    cmd="curl -v -X POST \
      -F \"file=@/test.txt;type=text/plain\" \
      -F \"accessMode=STANDALONE\" \
      -F \"checkUnitId=BALANCEABILITY\" \
      -F \"fileType=HTML\" \
      -F \"extension={\\\"title\\\":\\\"中蒙经济合作区建设进展\\\",\\\"mainSubmit\\\":[],\\\"copySubmit\\\":[]}\" \
      \"http://localhost/ai-doc-poc/api/v1/check\""

    # 显示要执行的命令
    echo -e "${YELLOW}执行命令:${NC}"
    echo "$cmd"
    
    # 执行代理请求并计时
    start_time=$(date +%s)
    eval "$cmd"
    end_time=$(date +%s)
    echo
    echo -e "${GREEN}代理请求耗时: $((end_time - start_time)) 秒${NC}"
    echo
    echo -e "${GREEN}=== 所有测试完成 ===${NC}"
  test.txt: |-
    来自前端容器中的测试：请输入你的测试文本
kind: ConfigMap
metadata:
  name: poc-intelligence-view-config
  namespace: oa-llm
---
apiVersion: v1
data:
  filebeat.yml: |-
    filebeat.inputs:
    - type: filestream
      id: my-filestream-id
      paths:
        - /poc-intelligence-view/data/logs/*.log
      fields:
        envTag: "oa-llm"
        appTag: "poc-intelligence-view"
        namespace: oa-llm
    processors:
    - drop_fields:
        fields: ["agent","input","ecs"]
        ignore_missing: true
    output.elasticsearch:
      hosts: ["http://elasticsearch.oa-llm:9200"]
      index: "poc-intelligence-view-logs-%{+yyyy.MM.dd}"
    setup.template.name: "poc-intelligence-view-logs"
    setup.template.pattern: "poc-intelligence-view-logs*"
    setup.ilm.enabled: false  # 禁用 ILM，避免自动 rollover 到 filebeat-*
kind: ConfigMap
metadata:
  labels:
    cmcc-gitops-project-tag: oa-llm 
  name: poc-intelligence-view-filebeat-cm
  namespace: oa-llm