#! /bin/bash
#当前目录
CURRENT_DIR=$(cd $(dirname $0); pwd)
echo "当前目录: $CURRENT_DIR"
# 设置权限
echo "设置权限"
echo "chmod +x $CURRENT_DIR/shells/*.sh"
chmod +x $CURRENT_DIR/shells/*.sh

# 登录docker 仓库
#echo "登录docker 仓库"
#echo "docker login officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn --username=zglyjt --password=Znwd0415@"
#docker login officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn --username=zglyjt --password=Znwd0415@

# 设置日志目录的权限所有子目录中logs目录的权限
echo "设置日志目录的权限777"
echo "find $CURRENT_DIR -type d -name "logs" -exec chmod 777 {} \;"
find $CURRENT_DIR -type d -name "logs" -exec chmod 777 {} \;

echo "设置数据目录权限755"
echo "find $CURRENT_DIR -type d -name "data" -exec chmod 777 {} \;"
find $CURRENT_DIR -type d -name "data" -exec chmod 777 {} \;

#检查是否有docker-compose.yml文件
if [ ! -f "$CURRENT_DIR/docker-compose.yml" ]; then
    echo "docker-compose.yml文件不存在"
    exit 1
fi
# 检查是否有docker-compose命令
if ! command -v docker-compose &> /dev/null; then
    echo "docker-compose命令不存在"
    echo "安装docker-compose：yum install -y docker-compose"
    yum install -y docker-compose
fi
if ! command -v docker-compose &> /dev/null; then
    echo "docker-compose命令不存在"
    echo "安装docker-compose：apt-get install -y docker-compose"
    apt-get install -y docker-compose
fi

# 创建网络
echo "创建网络....."
if ! docker network ls | grep -q jiutianwensi-network; then
    echo "docker network create jiutianwensi-network"
    docker network create jiutianwensi-network
fi



# 先停止所有容器
echo "停止所有容器"
docker-compose -f $CURRENT_DIR/docker-compose.yml down -v
docker system prune -f

# 部署nginx-common
echo "部署系统"
echo "docker-compose -f $CURRENT_DIR/docker-compose.yml up -d"
docker-compose -f $CURRENT_DIR/docker-compose.yml up -d

# 进入容器
echo "进入容器 使用下面的命令"
echo "docker exec -it nginx-common bash"
echo "docker exec -it poc-intelligence-view-write bash"
echo "docker exec -it poc-intelligence-view bash"
echo "docker exec -it article-auto-compose-server bash"
echo "docker exec -it ai-doc-poc bash"
echo "docker exec -it ai-redactor bash"
echo "docker exec -it ai-writer-nostream bash"
echo "docker exec -it elasticsearch bash"
echo "docker exec -it kibana bash"
echo "docker exec -it filebeat-common bash"

#进入filebeat-common容器 查看各个应用的日志是否存在
echo "docker exec -it filebeat-common ls -la /data/ai-doc-poc/logs"
docker exec -it filebeat-common ls -la /data/ai-doc-poc/logs
echo "docker exec -it filebeat-common ls -la /data/ai-redactor/logs"
docker exec -it filebeat-common ls -la /data/ai-redactor/logs
echo "docker exec -it filebeat-common ls -la /data/ai-writer-nostream/logs"
docker exec -it filebeat-common ls -la /data/ai-writer-nostream/logs
echo "docker exec -it filebeat-common ls -la /data/article-auto-compose-server/logs"
docker exec -it filebeat-common ls -la /data/article-auto-compose-server/logs
echo "docker exec -it filebeat-common ls -la /data/poc-intelligence-view-write/logs"
docker exec -it filebeat-common ls -la /data/poc-intelligence-view-write/logs
echo "docker exec -it filebeat-common ls -la /data/poc-intelligence-view/logs"
docker exec -it filebeat-common ls -la /data/poc-intelligence-view/logs




