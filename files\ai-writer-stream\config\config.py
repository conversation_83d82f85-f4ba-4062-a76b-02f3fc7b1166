# !/usr/bin/env python
# -*-coding:utf-8 -*-
import json
import os
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# ENV_PROFILE = os.environ.get("ENV")

MODLE_APPID = "gwywznnw"
MODLE_APPKEY = "6a52abb3f1a55c41159ae9f96a2e09ad"
MODLE_CNAME = "{{model.model-name}}"
MODEL_URL = "{{model.url}}"
API_KEY = "{{model.api-key}}"
LIMIT_TIME = 60


LOCAL_DATA_PATH = "/app/web_source_code/local_data/"
FAISS_DATA_PAHT = "/app/web_source_code/local_data/公文语料库2024-12-24.xlsx"

logger.info("MODLE_APPID === " + str(MODLE_APPID))
logger.info("MODLE_APPKEY === " + str(MODLE_APPKEY))
logger.info("MODLE_CNAME === " + str(MODLE_CNAME))
logger.info("MODEL_URL === " + str(MODEL_URL))
logger.info("LIMIT_TIME === " + str(60))
logger.info("LOCAL_DATA_PATH === " + str(LOCAL_DATA_PATH))
logger.info("FAISS_DATA_PAHT === " + str(FAISS_DATA_PAHT))