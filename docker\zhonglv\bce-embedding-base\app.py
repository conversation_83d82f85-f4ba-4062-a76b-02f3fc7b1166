#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, request, jsonify
import torch
from transformers import AutoTokenizer, AutoModel
import os
import logging
import time
import json

app = Flask(__name__)

# 配置日志
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"bce_embedding_service_{time.strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()  # 同时输出到控制台
    ]
)
logger = logging.getLogger("bce-embedding")

# 模型路径，可通过环境变量配置或默认为上一级目录的models
MODEL_PATH = os.environ.get("MODEL_PATH", "../models")

# 加载模型和tokenizer
@app.route('/health', methods=['GET'])
def health_check():
    client_ip = request.remote_addr
    logger.info(f"收到健康检查请求 - 客户端IP: {client_ip}")
    
    response = {"status": "ok"}
    logger.info(f"健康检查响应: {json.dumps(response)}")
    return jsonify(response), 200

def load_model():
    logger.info("正在加载已下载的模型...")
    logger.info(f"模型路径: {os.path.abspath(MODEL_PATH)}")
    try:
        tokenizer = AutoTokenizer.from_pretrained(MODEL_PATH)
        model = AutoModel.from_pretrained(MODEL_PATH)
        if torch.cuda.is_available():
            model = model.cuda()
            logger.info("使用GPU加速模型推理")
        else:
            logger.info("使用CPU进行模型推理")
        model.eval()
        logger.info("模型加载完成!")
        return tokenizer, model
    except Exception as e:
        logger.error(f"模型加载失败: {str(e)}")
        logger.error("请确保模型已正确下载并放置在正确的路径")
        logger.error(f"如果模型路径不正确，请设置环境变量MODEL_PATH指向正确的模型目录")
        raise

tokenizer, model = load_model()

def get_embedding(text):
    logger.info(f"开始为文本生成嵌入向量，文本内容: '{text[:100]}{'...' if len(text) > 100 else ''}'")
    with torch.no_grad():
        inputs = tokenizer(text, return_tensors="pt", padding=True, truncation=True, max_length=512)
        if torch.cuda.is_available():
            inputs = {k: v.cuda() for k, v in inputs.items()}
        outputs = model(**inputs)
        embeddings = outputs.last_hidden_state[:, 0].cpu().numpy()
        embedding_result = embeddings[0].tolist()
        logger.info(f"嵌入向量生成完成，维度: {len(embedding_result)}, 向量前5个元素: {embedding_result[:5]}")
        return embedding_result

@app.route('/embed', methods=['POST'])
def embed():
    client_ip = request.remote_addr
    logger.info(f"收到单文本嵌入请求 - 客户端IP: {client_ip}")
    
    try:
        data = request.json
        logger.info(f"请求参数: {json.dumps(data)}")
        
        text = data.get('text', '')
        if not text:
            error_msg = {"error": "请提供文本内容"}
            logger.warning(f"请求参数错误: {error_msg}")
            return jsonify(error_msg), 400
        
        logger.info(f"处理单文本嵌入请求，文本长度: {len(text)}")
        embedding = get_embedding(text)
        
        response = {
            "text": text,
            "embedding": embedding,
            "dimensions": len(embedding)
        }
        logger.info(f"处理完成，返回结果: 文本长度={len(text)}, 向量维度={len(embedding)}")
        return jsonify(response)
    except Exception as e:
        error_msg = {"error": str(e)}
        logger.error(f"生成嵌入向量时出错: {str(e)}")
        return jsonify(error_msg), 500

@app.route('/embed_batch', methods=['POST'])
def embed_batch():
    client_ip = request.remote_addr
    logger.info(f"收到批量文本嵌入请求 - 客户端IP: {client_ip}")
    
    try:
        data = request.json
        logger.info(f"请求参数: {json.dumps(data)}")
        
        texts = data.get('texts', [])
        if not texts:
            error_msg = {"error": "请提供文本列表"}
            logger.warning(f"请求参数错误: {error_msg}")
            return jsonify(error_msg), 400
        
        logger.info(f"处理批量文本嵌入请求，文本数量: {len(texts)}")
        results = []
        for i, text in enumerate(texts):
            logger.info(f"处理第 {i+1}/{len(texts)} 个文本，长度: {len(text)}")
            embedding = get_embedding(text)
            results.append({
                "text": text,
                "embedding": embedding,
                "dimensions": len(embedding)
            })
        
        response = {"results": results}
        logger.info(f"批量嵌入向量生成完成，共处理 {len(texts)} 个文本，第一个结果维度: {results[0]['dimensions'] if results else 0}")
        return jsonify(response)
    except Exception as e:
        error_msg = {"error": str(e)}
        logger.error(f"生成批量嵌入向量时出错: {str(e)}")
        return jsonify(error_msg), 500

# 添加OpenAI兼容的embeddings接口
@app.route('/embeddings', methods=['POST'])
def openai_embeddings():
    client_ip = request.remote_addr
    logger.info(f"收到OpenAI兼容的嵌入请求 - 客户端IP: {client_ip}")
    
    try:
        data = request.json
        logger.info(f"请求参数: {json.dumps(data)}")
        
        # 支持input或texts作为输入参数
        texts = data.get('texts', data.get('input', []))
        if isinstance(texts, str):
            texts = [texts]
            logger.info("检测到单个字符串输入，已转换为列表")
            
        if not texts:
            error_msg = {"error": "请提供文本内容，参数名为'texts'或'input'"}
            logger.warning(f"请求参数错误: {error_msg}")
            return jsonify(error_msg), 400
        
        logger.info(f"处理OpenAI兼容的嵌入请求，文本数量: {len(texts)}")
        
        embeddings = []
        for i, text in enumerate(texts):
            logger.info(f"处理第 {i+1}/{len(texts)} 个文本，长度: {len(text)}")
            embedding = get_embedding(text)
            embeddings.append(embedding)
        
        # 统计token数量（简单估算）
        token_count = sum(len(text.split()) for text in texts)
        
        # 返回OpenAI兼容的格式
        response = {
            "data": [
                {
                    "object": "embedding",
                    "embedding": emb,
                    "index": i
                } for i, emb in enumerate(embeddings)
            ],
            "model": "bce-embedding-base",
            "object": "list",
            "usage": {
                "prompt_tokens": token_count,
                "total_tokens": token_count
            }
        }
        
        logger.info(f"OpenAI兼容嵌入向量生成完成，共处理 {len(texts)} 个文本，估算token数: {token_count}")
        logger.info(f"返回 {len(response['data'])} 个嵌入向量，每个维度: {len(embeddings[0]) if embeddings else 0}")
        return jsonify(response)
    except Exception as e:
        error_msg = {"error": str(e)}
        logger.error(f"生成OpenAI兼容嵌入向量时出错: {str(e)}")
        return jsonify(error_msg), 500

if __name__ == '__main__':
    port = int(os.environ.get("PORT", 8000))
    logger.info(f"服务已启动，访问地址: http://localhost:{port}")
    app.run(host='0.0.0.0', port=port) 