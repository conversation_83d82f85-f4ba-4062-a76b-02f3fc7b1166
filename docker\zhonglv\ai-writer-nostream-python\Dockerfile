FROM m.daocloud.io/docker.io/python:3.10-slim

WORKDIR /app

# 安装网络工具 curl 和 telnet
RUN apt-get update && \
    apt-get install -y curl telnet vim && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

RUN pip uninstall -y numpy
RUN pip install numpy==1.25.0
# 复制依赖文件并安装依赖
COPY requirements.txt .
RUN pip install uv 
RUN uv pip install --system -r requirements.txt

# 复制项目文件
COPY app /app

# 确保日志目录存在
RUN mkdir -p app/log

# 复制并修正shell脚本权限
COPY start.sh /app/shells/start.sh
RUN chmod +x /app/shells/start.sh

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8080

# 设置启动命令，直接调用Python运行views.py，而不是通过shell脚本
CMD ["/bin/bash", "/app/shells/start.sh"] 