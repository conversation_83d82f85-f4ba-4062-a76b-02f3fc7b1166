#!/bin/bash

# 检测K8S资源类型脚本
# 主要用于检测Pod关联的资源类型，优化了对StatefulSet的检测
# 用法: ./detect_k8s_resource.sh POD_NAME NAMESPACE
# 返回: 通过stdout返回JSON格式的结果，通过exit code返回成功/失败状态

# 设置默认值
NAMESPACE=${2:-oa-llm}
POD_NAME=$1

if [ -z "$POD_NAME" ]; then
    echo "用法: $0 POD_NAME [NAMESPACE]" >&2
    echo "NAMESPACE默认为oa-llm" >&2
    exit 1
fi

# 颜色定义
GREEN="\033[0;32m"
BLUE="\033[0;34m"
RED="\033[0;31m"
YELLOW="\033[1;33m"
NC="\033[0m" # 恢复默认颜色

echo -e "${BLUE}开始检测Pod \"${POD_NAME}\"的资源类型...${NC}" >&2

# 1. 从Pod标签中获取控制器信息
echo -e "${YELLOW}从Pod标签中获取控制器信息...${NC}" >&2
OWNER_REFERENCE=$(kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.metadata.ownerReferences[0].kind}' 2>/dev/null)
OWNER_NAME=$(kubectl get pod $POD_NAME -n $NAMESPACE -o jsonpath='{.metadata.ownerReferences[0].name}' 2>/dev/null)

if [ -n "$OWNER_REFERENCE" ] && [ -n "$OWNER_NAME" ]; then
    echo -e "${GREEN}发现直接控制器: ${OWNER_REFERENCE} - ${OWNER_NAME}${NC}" >&2
    
    # 如果是ReplicaSet，需要进一步查找Deployment
    if [ "$OWNER_REFERENCE" == "ReplicaSet" ]; then
        echo -e "${YELLOW}检测ReplicaSet的父控制器...${NC}" >&2
        DEPLOYMENT_NAME=$(kubectl get rs $OWNER_NAME -n $NAMESPACE -o jsonpath='{.metadata.ownerReferences[0].name}' 2>/dev/null)
        
        if [ -n "$DEPLOYMENT_NAME" ]; then
            echo -e "${GREEN}发现最终控制器: Deployment - ${DEPLOYMENT_NAME}${NC}" >&2
            RESOURCE_TYPE="deployment"
            RESOURCE_NAME=$DEPLOYMENT_NAME
        else
            echo -e "${YELLOW}未找到ReplicaSet的父控制器，使用ReplicaSet作为控制器${NC}" >&2
            RESOURCE_TYPE="replicaset"
            RESOURCE_NAME=$OWNER_NAME
        fi
    else
        # 直接使用所有者引用
        RESOURCE_TYPE=$(echo $OWNER_REFERENCE | tr '[:upper:]' '[:lower:]')
        RESOURCE_NAME=$OWNER_NAME
    fi
else
    echo -e "${YELLOW}无法从Pod元数据中获取控制器信息，尝试从命名模式推断...${NC}" >&2
    
    # 2. 从Pod名称推断资源类型
    # StatefulSet格式: <statefulset-name>-<ordinal>
    # Deployment格式: <deployment-name>-<replicaset-hash>-<pod-hash>
    
    # 检查是否符合StatefulSet命名模式（以数字结尾）
    if [[ $POD_NAME =~ -[0-9]+$ ]]; then
        POTENTIAL_STATEFULSET_NAME=$(echo $POD_NAME | sed 's/-[0-9]\+$//')
        echo -e "${YELLOW}可能是StatefulSet Pod，检查StatefulSet \"${POTENTIAL_STATEFULSET_NAME}\"...${NC}" >&2
        
        # 检查该StatefulSet是否存在
        kubectl get statefulset $POTENTIAL_STATEFULSET_NAME -n $NAMESPACE &>/dev/null
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}确认为StatefulSet: ${POTENTIAL_STATEFULSET_NAME}${NC}" >&2
            RESOURCE_TYPE="statefulset"
            RESOURCE_NAME=$POTENTIAL_STATEFULSET_NAME
        else
            echo -e "${RED}推断的StatefulSet不存在${NC}" >&2
        fi
    fi
    
    # 如果还没找到资源类型，尝试提取Deployment名称
    if [ -z "$RESOURCE_TYPE" ]; then
        # 尝试提取可能的Deployment名称（删除后两个连字符及其后面的内容）
        POTENTIAL_DEPLOYMENT_NAME=$(echo $POD_NAME | sed 's/\(.*\)-[^-]*-[^-]*$/\1/')
        echo -e "${YELLOW}检查可能的Deployment \"${POTENTIAL_DEPLOYMENT_NAME}\"...${NC}" >&2
        
        # 检查该Deployment是否存在
        kubectl get deployment $POTENTIAL_DEPLOYMENT_NAME -n $NAMESPACE &>/dev/null
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}确认为Deployment: ${POTENTIAL_DEPLOYMENT_NAME}${NC}" >&2
            RESOURCE_TYPE="deployment"
            RESOURCE_NAME=$POTENTIAL_DEPLOYMENT_NAME
        else
            echo -e "${RED}推断的Deployment不存在${NC}" >&2
        fi
    fi
fi

# 3. 如果依然没找到，则检查所有可能的资源类型
if [ -z "$RESOURCE_TYPE" ]; then
    echo -e "${YELLOW}尝试从所有资源类型中查找...${NC}" >&2
    
    # 提取可能的基础名称（去掉末尾的数字和连字符）
    BASE_NAME=$(echo $POD_NAME | sed 's/\(.*\)-[^-]*-[^-]*$/\1/' | sed 's/-[0-9]\+$//')
    
    # 检查各种可能的资源类型
    for TYPE in deployment statefulset daemonset job; do
        echo -e "${YELLOW}检查${TYPE}: ${BASE_NAME}${NC}" >&2
        kubectl get $TYPE $BASE_NAME -n $NAMESPACE &>/dev/null
        if [ $? -eq 0 ]; then
            RESOURCE_TYPE=$TYPE
            RESOURCE_NAME=$BASE_NAME
            echo -e "${GREEN}发现${TYPE}: ${BASE_NAME}${NC}" >&2
            break
        fi
    done
fi

# 输出最终结果
if [ -n "$RESOURCE_TYPE" ] && [ -n "$RESOURCE_NAME" ]; then
    echo -e "${GREEN}=====================================${NC}" >&2
    echo -e "${GREEN}检测完成!${NC}" >&2
    echo -e "${GREEN}Pod: ${POD_NAME}${NC}" >&2
    echo -e "${GREEN}资源类型: ${RESOURCE_TYPE}${NC}" >&2
    echo -e "${GREEN}资源名称: ${RESOURCE_NAME}${NC}" >&2
    echo -e "${GREEN}命名空间: ${NAMESPACE}${NC}" >&2
    echo -e "${GREEN}=====================================${NC}" >&2
    
    # 输出用于重启的命令
    echo -e "${BLUE}重启命令:${NC}" >&2
    if [[ "$RESOURCE_TYPE" == "deployment" || "$RESOURCE_TYPE" == "statefulset" || "$RESOURCE_TYPE" == "daemonset" ]]; then
        echo -e "${YELLOW}kubectl rollout restart ${RESOURCE_TYPE} ${RESOURCE_NAME} -n ${NAMESPACE}${NC}" >&2
    else
        echo -e "${RED}此资源类型 (${RESOURCE_TYPE}) 无法使用rollout restart命令${NC}" >&2
    fi
    
    # 将结果以JSON格式输出到标准输出，便于脚本捕获
    echo "{\"found\":true,\"type\":\"$RESOURCE_TYPE\",\"name\":\"$RESOURCE_NAME\",\"namespace\":\"$NAMESPACE\"}"
    exit 0
else
    echo -e "${RED}=====================================${NC}" >&2
    echo -e "${RED}未能确定Pod \"${POD_NAME}\"的资源类型${NC}" >&2
    echo -e "${RED}只能通过删除Pod方式重启:${NC}" >&2
    echo -e "${RED}kubectl delete pod ${POD_NAME} -n ${NAMESPACE}${NC}" >&2
    echo -e "${RED}=====================================${NC}" >&2
    
    # 将结果以JSON格式输出到标准输出
    echo "{\"found\":false,\"type\":\"\",\"name\":\"\",\"namespace\":\"$NAMESPACE\",\"pod\":\"$POD_NAME\"}"
    exit 1
fi 