#!/bin/bash

# 当前目录
CURRENT_DIR=$(cd $(dirname $0); pwd)
echo "当前目录: $CURRENT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 从docker-compose.yml获取服务列表
get_services() {
    if ! command -v yq &> /dev/null; then
        echo -e "${RED}yq命令不存在，正在安装...${NC}"
        # 获取系统架构
        ARCH=$(uname -m)
        case $ARCH in
            x86_64)
                YQ_ARCH="amd64"
                ;;
            aarch64)
                YQ_ARCH="arm64"
                ;;
            *)
                echo -e "${RED}不支持的CPU架构: $ARCH${NC}"
                return 1
                ;;
        esac
        
        # 检测系统类型并安装yq
        if [ -f /etc/os-release ] && grep -q "openEuler" /etc/os-release; then
            # openEuler
            echo -e "${GREEN}检测到 openEuler 系统，CPU架构: $ARCH${NC}"
            curl -L https://github.com/mikefarah/yq/releases/latest/download/yq_linux_${YQ_ARCH} -o /usr/local/bin/yq
            chmod +x /usr/local/bin/yq
        elif [ -f /etc/redhat-release ]; then
            # CentOS/RHEL
            echo -e "${GREEN}检测到 CentOS/RHEL 系统，CPU架构: $ARCH${NC}"
            curl -L https://github.com/mikefarah/yq/releases/latest/download/yq_linux_${YQ_ARCH} -o /usr/local/bin/yq
            chmod +x /usr/local/bin/yq
        elif [ -f /etc/debian_version ]; then
            # Debian/Ubuntu
            echo -e "${GREEN}检测到 Debian/Ubuntu 系统，CPU架构: $ARCH${NC}"
            wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_${YQ_ARCH} -O /usr/local/bin/yq
            chmod +x /usr/local/bin/yq
        else
            echo -e "${RED}不支持的系统类型，请手动安装yq${NC}"
            return 1
        fi
    fi
    
    # 使用yq获取服务列表
    yq e '.services | keys | .[]' $CURRENT_DIR/docker-compose.yml
}

# 显示主菜单
show_menu() {
    clear
    echo -e "${GREEN}=== 服务管理菜单 ===${NC}"
    echo "1. 加载所有镜像"
    echo "2. 启动所有服务"
    echo "3. 停止所有服务"
    echo "4. 重启所有服务"
    echo "5. 单个服务操作"
    echo "6. 查看服务状态"
    echo "7. 查看端口映射"
    echo "8. 查看网络信息"
    echo "0. 退出"
    echo
    echo -e "${YELLOW}请选择操作 [0-8]:${NC}"
}

# 显示服务操作菜单
show_service_operations() {
    local service=$1
    clear
    echo -e "${GREEN}=== 服务 [$service] 操作菜单 ===${NC}"
    echo "1. 加载服务镜像"
    echo "2. 启动服务"
    echo "3. 停止服务"
    echo "4. 重启服务"
    echo "5. 修改服务配置"
    echo "6. 进入容器"
    echo "7. 查看日志"
    echo "8. 执行测试脚本"
    echo "9. 查看服务状态"
    echo "10. 查看端口映射"
    echo "11. 查看网络信息"
    echo "12. 测试端口连通性"
    echo "0. 返回服务列表"
    echo
    echo -e "${YELLOW}请选择操作 [0-12]:${NC}"
}

# 加载单个镜像
load_single_image() {
    local service=$1
    cd $CURRENT_DIR
    # 获取所有tar文件
    tar_files=($(ls *.tar 2>/dev/null))
    
    if [ ${#tar_files[@]} -eq 0 ]; then
        echo -e "${RED}当前目录下没有找到tar镜像文件${NC}"
        return
    fi
    
    echo -e "${GREEN}可用的镜像文件:${NC}"
    for i in "${!tar_files[@]}"; do
        echo "$((i+1)). ${tar_files[$i]}"
    done
    echo "0. 返回上级菜单"
    echo -e "${YELLOW}请选择要加载的镜像 [0-${#tar_files[@]}]:${NC}"
    read choice
    
    if [ "$choice" = "0" ]; then
        return
    fi
    
    if [ "$choice" -ge 1 ] && [ "$choice" -le "${#tar_files[@]}" ]; then
        image_file=${tar_files[$((choice-1))]}
        echo -e "${GREEN}正在加载镜像: $image_file${NC}"
        docker load -i $image_file
        echo -e "${GREEN}镜像加载完成${NC}"
    else
        echo -e "${RED}无效的选择${NC}"
    fi
}

# 启动单个服务
start_single_service() {
    local service=$1
    echo -e "${GREEN}正在启动服务: $service${NC}"
    docker-compose -f $CURRENT_DIR/docker-compose.yml up -d $service
    echo -e "${GREEN}服务启动完成${NC}"
}

# 停止单个服务
stop_single_service() {
    local service=$1
    echo -e "${YELLOW}正在停止服务: $service${NC}"
    docker-compose -f $CURRENT_DIR/docker-compose.yml stop $service
    echo -e "${GREEN}服务已停止${NC}"
}

# 重启单个服务
restart_single_service() {
    local service=$1
    echo -e "${YELLOW}正在重启服务: $service${NC}"
    docker-compose -f $CURRENT_DIR/docker-compose.yml restart $service
    echo -e "${GREEN}服务已重启${NC}"
}

# 修改服务配置
modify_service_config() {
    local service=$1
    config_dir="$CURRENT_DIR/$service/config"
    if [ -d "$config_dir" ]; then
        echo -e "${GREEN}可用的配置文件:${NC}"
        config_files=($(ls $config_dir))
        for i in "${!config_files[@]}"; do
            echo "$((i+1)). ${config_files[$i]}"
        done
        echo "0. 返回上级菜单"
        echo -e "${YELLOW}请选择要修改的配置文件 [0-${#config_files[@]}]:${NC}"
        read config_choice
        
        if [ "$config_choice" != "0" ] && [ "$config_choice" -ge 1 ] && [ "$config_choice" -le "${#config_files[@]}" ]; then
            config_file=${config_files[$((config_choice-1))]}
            echo -e "${GREEN}正在编辑配置文件: $config_file${NC}"
            if command -v vi &> /dev/null; then
                vi "$config_dir/$config_file"
            else
                nano "$config_dir/$config_file"
            fi
            echo -e "${YELLOW}是否需要重启服务以应用新配置？(y/n)${NC}"
            read restart_choice
            if [ "$restart_choice" = "y" ] || [ "$restart_choice" = "Y" ]; then
                restart_single_service $service
            fi
        fi
    else
        echo -e "${RED}未找到配置文件目录: $config_dir${NC}"
    fi
}

# 进入容器
enter_container() {
    local service=$1
    echo -e "${GREEN}正在尝试进入容器: $service${NC}"
    
    # 尝试不同的 shell 命令
    local shells=("bash" "/bin/bash" "sh" "/bin/sh")
    local success=false
    
    for shell in "${shells[@]}"; do
        echo -e "${YELLOW}尝试使用 $shell 进入容器...${NC}"
        if docker exec -it $service $shell 2>/dev/null; then
            success=true
            break
        fi
    done
    
    if [ "$success" = false ]; then
        echo -e "${RED}无法进入容器，尝试使用替代方案...${NC}"
        echo -e "${YELLOW}1. 使用 docker exec 执行命令${NC}"
        echo -e "${YELLOW}2. 查看容器日志${NC}"
        echo -e "${YELLOW}3. 重启容器${NC}"
        echo -e "${YELLOW}请选择操作 [1-3]:${NC}"
        read choice
        
        case $choice in
            1)
                echo -e "${GREEN}请输入要执行的命令:${NC}"
                read cmd
                docker exec $service $cmd
                ;;
            2)
                docker logs $service
                ;;
            3)
                docker restart $service
                echo -e "${GREEN}容器已重启，请稍后重试进入容器${NC}"
                ;;
            *)
                echo -e "${RED}无效的选择${NC}"
                ;;
        esac
    fi
}

# 查看日志
view_logs() {
    local service=$1
    echo -e "${GREEN}正在查看服务日志: $service${NC}"
    
    # 检查日志目录是否存在
    local log_dir="/data/jtws/$service/logs"
    if [ ! -d "$log_dir" ]; then
        echo -e "${RED}日志目录不存在: $log_dir${NC}"
        return
    fi
    
    # 获取所有日志文件并按修改时间倒序排列
    local log_files=($(ls -t "$log_dir"/*.log 2>/dev/null))
    
    if [ ${#log_files[@]} -eq 0 ]; then
        echo -e "${YELLOW}未找到日志文件${NC}"
        return
    fi
    
    # 显示日志文件列表
    echo -e "${YELLOW}可用的日志文件:${NC}"
    for i in "${!log_files[@]}"; do
        local file_name=$(basename "${log_files[$i]}")
        local file_size=$(du -h "${log_files[$i]}" | cut -f1)
        local file_time=$(stat -c "%y" "${log_files[$i]}")
        echo "$((i+1)). $file_name (大小: $file_size, 修改时间: $file_time)"
    done
    echo "0. 返回"
    
    # 选择要查看的日志文件
    echo -e "${YELLOW}请选择要查看的日志文件 [0-${#log_files[@]}]:${NC}"
    read choice
    
    if [ "$choice" = "0" ]; then
        return
    fi
    
    if [ "$choice" -ge 1 ] && [ "$choice" -le "${#log_files[@]}" ]; then
        selected_file=${log_files[$((choice-1))]}
        echo -e "${GREEN}正在查看日志文件: $(basename "$selected_file")${NC}"
        tail -n 200 "$selected_file"
    else
        echo -e "${RED}无效的选择${NC}"
    fi
}

# 执行测试脚本
run_test_script() {
    local service=$1
    echo -e "${GREEN}正在执行测试脚本: $service${NC}"
    
    # 从 docker-compose.yml 中获取测试脚本的挂载路径
    local test_script_path=""
    local volumes=$(yq e ".services.$service.volumes" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
    
    if [ "$volumes" != "null" ] && [ -n "$volumes" ]; then
        # 查找包含 test.sh 的挂载路径
        test_script_path=$(echo "$volumes" | grep -o ".*test.sh:.*" | cut -d':' -f2)
    fi
    
    if [ -z "$test_script_path" ]; then
        echo -e "${RED}未找到测试脚本路径${NC}"
        return
    fi
    
    # 执行测试脚本
    docker exec -it $service bash -c "sh $test_script_path"
}

# 加载所有镜像
load_images() {
    echo -e "${GREEN}开始加载镜像...${NC}"
    cd $CURRENT_DIR
    for image in *.tar; do
        if [ -f "$image" ]; then
            echo "加载镜像: $image"
            docker load -i $image
        fi
    done
    echo -e "${GREEN}镜像加载完成${NC}"
}

# 启动所有服务
start_services() {
    echo -e "${GREEN}启动所有服务...${NC}"
    docker-compose -f $CURRENT_DIR/docker-compose.yml up -d
    echo -e "${GREEN}服务启动完成${NC}"
}

# 停止所有服务
stop_services() {
    echo -e "${YELLOW}停止所有服务...${NC}"
    docker-compose -f $CURRENT_DIR/docker-compose.yml down
    echo -e "${GREEN}服务已停止${NC}"
}

# 重启所有服务
restart_services() {
    stop_services
    start_services
}

# 查看服务状态
show_services_status() {
    echo -e "${GREEN}=== 服务状态 ===${NC}"
    echo -e "${YELLOW}容器状态:${NC}"
    docker-compose -f $CURRENT_DIR/docker-compose.yml ps
    
    echo -e "\n${YELLOW}资源使用情况:${NC}"
    docker stats --no-stream
    
    echo -e "\n${YELLOW}日志最后10行:${NC}"
    services=($(get_services))
    for service in "${services[@]}"; do
        echo -e "\n${GREEN}服务 [$service] 日志:${NC}"
        docker-compose -f $CURRENT_DIR/docker-compose.yml logs --tail=10 $service
    done
}

# 查看端口映射
show_port_mappings() {
    echo -e "${GREEN}=== 端口映射信息 ===${NC}"
    echo -e "${YELLOW}所有容器的端口映射:${NC}"
    docker ps --format "table {{.Names}}\t{{.Ports}}"
    
    echo -e "\n${YELLOW}详细端口信息:${NC}"
    services=($(get_services))
    for service in "${services[@]}"; do
        echo -e "\n${GREEN}服务 [$service] 端口映射:${NC}"
        # 从docker-compose.yml中提取端口映射
        ports=$(yq e ".services.$service.ports" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
        if [ "$ports" != "null" ] && [ -n "$ports" ]; then
            echo "$ports" | while read -r port; do
                if [ -n "$port" ]; then
                    echo -e "${GREEN}$port${NC}"
                fi
            done
        else
            echo "无端口映射"
        fi
    done
}

# 查看网络信息
show_network_info() {
    echo -e "${GREEN}=== 网络信息 ===${NC}"
    
    # 获取主机IP地址
    host_ip=$(hostname -I | awk '{print $1}')
    
    echo -e "${YELLOW}主机IP地址:${NC}"
    echo -e "${GREEN}$host_ip${NC}"
    
    echo -e "\n${YELLOW}服务网络信息:${NC}"
    services=($(get_services))
    for service in "${services[@]}"; do
        if docker ps | grep -q $service; then
            echo -e "\n${GREEN}服务 [$service]:${NC}"
            
            # 获取网络模式
            network_mode=$(yq e ".services.$service.network_mode" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
            if [ "$network_mode" = "null" ] || [ -z "$network_mode" ]; then
                # 如果没有指定 network_mode，则检查 networks 配置
                networks=$(yq e ".services.$service.networks" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
                if [ "$networks" != "null" ] && [ -n "$networks" ]; then
                    echo -e "网络模式: bridge (使用自定义网络)"
                    echo -e "网络名称: $networks"
                else
                    echo -e "网络模式: bridge (默认)"
                fi
            else
                echo -e "网络模式: $network_mode"
            fi
            
            # 获取端口映射
            ports=$(yq e ".services.$service.ports" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
            if [ "$ports" != "null" ] && [ -n "$ports" ]; then
                echo -e "端口映射:"
                echo "$ports" | while read -r port; do
                    if [ -n "$port" ]; then
                        echo -e "  $port"
                    fi
                done
            fi
            
            # 显示服务访问地址
            if [ "$ports" != "null" ] && [ -n "$ports" ]; then
                echo -e "访问地址:"
                echo "$ports" | while read -r port; do
                    if [ -n "$port" ]; then
                        host_port=$(echo $port | cut -d':' -f1)
                        container_port=$(echo $port | cut -d':' -f2)
                        case $service in
                            "poc-intelligence-view")
                                if [ "$container_port" = "80" ]; then
                                    echo -e "  核稿系统: http://$host_ip:$host_port/poc-intelligence-view/"
                                fi
                                ;;
                            "poc-intelligence-view-write")
                                if [ "$container_port" = "80" ]; then
                                    echo -e "  拟文系统: http://$host_ip:$host_port/poc-intelligence-view-write/intelligent-writing"
                                fi
                                ;;
                            "kibana")
                                if [ "$container_port" = "5601" ]; then
                                    echo -e "  Kibana监控: http://$host_ip:$host_port"
                                fi
                                ;;
                            "nginx-common")
                                if [ "$container_port" = "80" ]; then
                                    echo -e "  统一网关: http://$host_ip:$host_port"
                                    echo -e "  操作手册: http://$host_ip:$host_port/操作手册/AI核稿操作手册.docx"
                                fi
                                ;;
                        esac
                    fi
                done
            fi
            
            # 显示环境变量
            env_vars=$(yq e ".services.$service.environment" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
            if [ "$env_vars" != "null" ] && [ -n "$env_vars" ]; then
                echo -e "环境变量:"
                echo "$env_vars" | while read -r env_var; do
                    if [ -n "$env_var" ]; then
                        echo -e "  $env_var"
                    fi
                done
            fi
        fi
    done
    
    # 显示网络连接测试
    echo -e "\n${YELLOW}网络连接测试:${NC}"
    for service in "${services[@]}"; do
        if [ "$service" != "$service" ] && docker ps | grep -q $service; then
            echo -e "\n测试连接到 $service:"
            if ping -c 2 $host_ip >/dev/null 2>&1; then
                echo -e "${GREEN}✓ 可以连接到 $service${NC}"
            else
                echo -e "${RED}✗ 无法连接到 $service${NC}"
            fi
        fi
    done
}

# 查看单个服务状态
show_single_service_status() {
    local service=$1
    echo -e "${GREEN}=== 服务 [$service] 状态 ===${NC}"
    
    echo -e "${YELLOW}容器状态:${NC}"
    docker-compose -f $CURRENT_DIR/docker-compose.yml ps $service
    
    echo -e "\n${YELLOW}资源使用情况:${NC}"
    docker stats --no-stream $service
    
    echo -e "\n${YELLOW}最近日志:${NC}"
    docker-compose -f $CURRENT_DIR/docker-compose.yml logs --tail=20 $service
}

# 查看单个服务端口映射
show_single_service_ports() {
    local service=$1
    
    # 检查容器是否运行
    if ! docker ps | grep -q $service; then
        echo -e "${RED}容器未运行，无法查看端口映射${NC}"
        return
    fi
    
    echo -e "${GREEN}=== 服务 [$service] 端口映射 ===${NC}"
    
    # 从docker-compose.yml中获取端口映射
    echo -e "${YELLOW}配置的端口映射:${NC}"
    ports=$(yq e ".services.$service.ports" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
    if [ "$ports" != "null" ] && [ -n "$ports" ]; then
        echo "$ports" | while read -r port; do
            if [ -n "$port" ]; then
                echo -e "${GREEN}$port${NC}"
            fi
        done
    else
        echo -e "${YELLOW}该服务无端口映射${NC}"
    fi
    
    # 显示实际运行的端口映射
    echo -e "\n${YELLOW}实际运行端口映射:${NC}"
    docker ps --format "table {{.Names}}\t{{.Ports}}" | grep $service
    
    # 显示端口监听状态
    echo -e "\n${YELLOW}端口监听状态:${NC}"
    if [ "$ports" != "null" ] && [ -n "$ports" ]; then
        echo "$ports" | while read -r port; do
            if [ -n "$port" ]; then
                host_port=$(echo $port | cut -d':' -f1)
                container_port=$(echo $port | cut -d':' -f2)
                echo -e "\n${GREEN}测试端口 $host_port (容器端口 $container_port):${NC}"
                
                # 测试容器内部端口
                echo -e "容器内部测试:"
                if docker exec $service nc -zv localhost $container_port 2>/dev/null; then
                    echo -e "${GREEN}✓ 容器内部端口 $container_port 可访问${NC}"
                else
                    echo -e "${RED}✗ 容器内部端口 $container_port 无法访问${NC}"
                fi
                
                # 测试主机端口
                echo -e "主机端口测试:"
                if nc -zv localhost $host_port 2>/dev/null; then
                    echo -e "${GREEN}✓ 主机端口 $host_port 可访问${NC}"
                else
                    echo -e "${RED}✗ 主机端口 $host_port 无法访问${NC}"
                fi
                
                # 测试HTTP/HTTPS服务
                if [[ $container_port == "80" || $container_port == "443" || $container_port == "8080" || $container_port == "8443" ]]; then
                    echo -e "HTTP/HTTPS服务测试:"
                    protocol="http"
                    if [[ $container_port == "443" || $container_port == "8443" ]]; then
                        protocol="https"
                    fi
                    if curl -s -o /dev/null -w "%{http_code}" $protocol://localhost:$host_port 2>/dev/null; then
                        echo -e "${GREEN}✓ HTTP/HTTPS服务响应正常${NC}"
                    else
                        echo -e "${RED}✗ HTTP/HTTPS服务无响应${NC}"
                    fi
                fi
            fi
        done
    fi
    
    # 显示服务访问地址
    echo -e "\n${YELLOW}服务访问地址:${NC}"
    if [ "$ports" != "null" ] && [ -n "$ports" ]; then
        host_ip=$(hostname -I | awk '{print $1}')
        echo "$ports" | while read -r port; do
            if [ -n "$port" ]; then
                host_port=$(echo $port | cut -d':' -f1)
                container_port=$(echo $port | cut -d':' -f2)
                case $service in
                    "poc-intelligence-view")
                        if [ "$container_port" = "80" ]; then
                            echo -e "${GREEN}核稿系统: http://$host_ip:$host_port/poc-intelligence-view/${NC}"
                        fi
                        ;;
                    "poc-intelligence-view-write")
                        if [ "$container_port" = "80" ]; then
                            echo -e "${GREEN}拟文系统: http://$host_ip:$host_port/poc-intelligence-view-write/intelligent-writing${NC}"
                        fi
                        ;;
                    "kibana")
                        if [ "$container_port" = "5601" ]; then
                            echo -e "${GREEN}Kibana监控: http://$host_ip:$host_port${NC}"
                        fi
                        ;;
                    "nginx-common")
                        if [ "$container_port" = "80" ]; then
                            echo -e "${GREEN}统一网关: http://$host_ip:$host_port${NC}"
                            echo -e "${GREEN}操作手册: http://$host_ip:$host_port/操作手册/AI核稿操作手册.docx${NC}"
                        fi
                        ;;
                esac
            fi
        done
    fi
}

# 查看单个服务网络信息
show_single_service_network() {
    local service=$1
    
    # 检查容器是否运行
    if ! docker ps | grep -q $service; then
        echo -e "${RED}容器未运行，无法查看网络信息${NC}"
        return
    fi
    
    echo -e "${GREEN}=== 服务 [$service] 网络信息 ===${NC}"
    
    # 获取主机IP地址
    host_ip=$(hostname -I | awk '{print $1}')
    
    # 显示网络模式
    echo -e "${YELLOW}网络模式:${NC}"
    network_mode=$(yq e ".services.$service.network_mode" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
    if [ "$network_mode" = "null" ] || [ -z "$network_mode" ]; then
        # 如果没有指定 network_mode，则检查 networks 配置
        networks=$(yq e ".services.$service.networks" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
        if [ "$networks" != "null" ] && [ -n "$networks" ]; then
            echo -e "${GREEN}bridge (使用自定义网络)${NC}"
            echo -e "${YELLOW}网络名称:${NC}"
            echo -e "${GREEN}$networks${NC}"
        else
            echo -e "${GREEN}bridge (默认)${NC}"
        fi
    else
        echo -e "${GREEN}$network_mode${NC}"
    fi
    
    # 显示主机IP地址
    echo -e "\n${YELLOW}主机IP地址:${NC}"
    echo -e "${GREEN}$host_ip${NC}"
    
    # 显示端口映射
    echo -e "\n${YELLOW}端口映射:${NC}"
    ports=$(yq e ".services.$service.ports" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
    if [ "$ports" != "null" ] && [ -n "$ports" ]; then
        echo "$ports" | while read -r port; do
            if [ -n "$port" ]; then
                echo -e "${GREEN}$port${NC}"
            fi
        done
    else
        echo -e "${YELLOW}该服务无端口映射${NC}"
    fi
    
    # 显示服务访问地址
    echo -e "\n${YELLOW}服务访问地址:${NC}"
    if [ "$ports" != "null" ] && [ -n "$ports" ]; then
        echo "$ports" | while read -r port; do
            if [ -n "$port" ]; then
                host_port=$(echo $port | cut -d':' -f1)
                container_port=$(echo $port | cut -d':' -f2)
                case $service in
                    "poc-intelligence-view")
                        if [ "$container_port" = "80" ]; then
                            echo -e "${GREEN}核稿系统: http://$host_ip:$host_port/poc-intelligence-view/${NC}"
                        fi
                        ;;
                    "poc-intelligence-view-write")
                        if [ "$container_port" = "80" ]; then
                            echo -e "${GREEN}拟文系统: http://$host_ip:$host_port/poc-intelligence-view-write/intelligent-writing${NC}"
                        fi
                        ;;
                    "kibana")
                        if [ "$container_port" = "5601" ]; then
                            echo -e "${GREEN}Kibana监控: http://$host_ip:$host_port${NC}"
                        fi
                        ;;
                    "nginx-common")
                        if [ "$container_port" = "80" ]; then
                            echo -e "${GREEN}统一网关: http://$host_ip:$host_port${NC}"
                            echo -e "${GREEN}操作手册: http://$host_ip:$host_port/操作手册/AI核稿操作手册.docx${NC}"
                        fi
                        ;;
                esac
            fi
        done
    fi
    
    # 显示环境变量
    env_vars=$(yq e ".services.$service.environment" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
    if [ "$env_vars" != "null" ] && [ -n "$env_vars" ]; then
        echo -e "\n${YELLOW}环境变量:${NC}"
        echo "$env_vars" | while read -r env_var; do
            if [ -n "$env_var" ]; then
                echo -e "${GREEN}$env_var${NC}"
            fi
        done
    fi
    
    # 显示网络连接测试
    echo -e "\n${YELLOW}网络连接测试:${NC}"
    services=($(get_services))
    for other_service in "${services[@]}"; do
        if [ "$other_service" != "$service" ] && docker ps | grep -q $other_service; then
            echo -e "\n测试连接到 $other_service:"
            if ping -c 2 $host_ip >/dev/null 2>&1; then
                echo -e "${GREEN}✓ 可以连接到 $other_service${NC}"
            else
                echo -e "${RED}✗ 无法连接到 $other_service${NC}"
            fi
        fi
    done
}

# 测试端口连通性
test_port_connectivity() {
    local service=$1
    
    # 检查容器是否运行
    if ! docker ps | grep -q $service; then
        echo -e "${RED}容器未运行，无法测试端口${NC}"
        return
    fi
    
    echo -e "${GREEN}=== 测试服务 [$service] 端口连通性 ===${NC}"
    
    # 从docker-compose.yml中获取端口映射
    ports=$(yq e ".services.$service.ports" $CURRENT_DIR/docker-compose.yml 2>/dev/null)
    if [ "$ports" = "null" ] || [ -z "$ports" ]; then
        echo -e "${YELLOW}未找到端口映射${NC}"
        return
    fi
    
    # 获取容器的IP地址
    local container_ip=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $service)
    
    echo -e "${YELLOW}容器IP: $container_ip${NC}"
    echo -e "${YELLOW}端口映射:${NC}"
    echo "$ports"
    
    # 测试每个端口
    echo -e "\n${YELLOW}开始端口测试:${NC}"
    echo "$ports" | while read -r port; do
        if [ -n "$port" ]; then
            # 提取主机端口和容器端口
            host_port=$(echo $port | cut -d':' -f1)
            container_port=$(echo $port | cut -d':' -f2)
            
            echo -e "\n${GREEN}测试端口 $container_port (映射到主机端口 $host_port):${NC}"
            
            # 测试容器内部端口
            echo -e "容器内部测试:"
            if docker exec $service nc -zv localhost $container_port 2>/dev/null; then
                echo -e "${GREEN}✓ 容器内部端口 $container_port 可访问${NC}"
            else
                echo -e "${RED}✗ 容器内部端口 $container_port 无法访问${NC}"
            fi
            
            # 测试主机端口
            echo -e "主机端口测试:"
            if nc -zv localhost $host_port 2>/dev/null; then
                echo -e "${GREEN}✓ 主机端口 $host_port 可访问${NC}"
            else
                echo -e "${RED}✗ 主机端口 $host_port 无法访问${NC}"
            fi
            
            # 测试HTTP/HTTPS服务
            if [[ $container_port == "80" || $container_port == "443" || $container_port == "8080" || $container_port == "8443" ]]; then
                echo -e "HTTP/HTTPS服务测试:"
                protocol="http"
                if [[ $container_port == "443" || $container_port == "8443" ]]; then
                    protocol="https"
                fi
                if curl -s -o /dev/null -w "%{http_code}" $protocol://localhost:$host_port 2>/dev/null; then
                    echo -e "${GREEN}✓ HTTP/HTTPS服务响应正常${NC}"
                else
                    echo -e "${RED}✗ HTTP/HTTPS服务无响应${NC}"
                fi
            fi
        fi
    done
}

# 处理单个服务操作
handle_single_service() {
    local service=$1
    while true; do
        show_service_operations $service
        read operation_choice
        
        case $operation_choice in
            1)
                load_single_image $service
                ;;
            2)
                start_single_service $service
                ;;
            3)
                stop_single_service $service
                ;;
            4)
                restart_single_service $service
                ;;
            5)
                modify_service_config $service
                ;;
            6)
                enter_container $service
                ;;
            7)
                view_logs $service
                ;;
            8)
                run_test_script $service
                ;;
            9)
                show_single_service_status $service
                ;;
            10)
                show_single_service_ports $service
                ;;
            11)
                show_single_service_network $service
                ;;
            12)
                test_port_connectivity $service
                ;;
            0)
                return
                ;;
            *)
                echo -e "${RED}无效的选择，请重试${NC}"
                ;;
        esac
        
        echo
        echo -e "${YELLOW}按回车键继续...${NC}"
        read
    done
}

# 显示服务列表并选择
show_service_list() {
    while true; do
        clear
        echo -e "${GREEN}=== 服务列表 ===${NC}"
        services=($(get_services))
        for i in "${!services[@]}"; do
            echo "$((i+1)). ${services[$i]}"
        done
        echo "0. 返回主菜单"
        echo
        echo -e "${YELLOW}请选择服务 [0-${#services[@]}]:${NC}"
        read choice
        
        if [ "$choice" = "0" ]; then
            return
        fi
        
        if [ "$choice" -ge 1 ] && [ "$choice" -le "${#services[@]}" ]; then
            selected_service=${services[$((choice-1))]}
            handle_single_service $selected_service
        else
            echo -e "${RED}无效的选择${NC}"
            echo -e "${YELLOW}按回车键继续...${NC}"
            read
        fi
    done
}

# 主循环
while true; do
    show_menu
    read choice
    
    case $choice in
        1)
            load_images
            ;;
        2)
            start_services
            ;;
        3)
            stop_services
            ;;
        4)
            restart_services
            ;;
        5)
            show_service_list
            ;;
        6)
            show_services_status
            ;;
        7)
            show_port_mappings
            ;;
        8)
            show_network_info
            ;;
        0)
            echo -e "${GREEN}退出程序${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}无效的选择，请重试${NC}"
            ;;
    esac
    
    echo
    echo -e "${YELLOW}按回车键继续...${NC}"
    read
done
