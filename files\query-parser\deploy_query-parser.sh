#!/bin/bash
APP_NAME=query-parser
MODULE_NAME=znwd
APP_DIR=/data/$MODULE_NAME/$APP_NAME
APP_LOG_DIR=/data/$MODULE_NAME/$APP_NAME/logs
APP_PORT=8084
IMAGE_NAME=officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/query-parser:v1

CONTAINER_NAME=$APP_NAME
CONTAINER_PORT=7890
CONTAINER_LOG_DIR=/home/<USER>/log
CONTAINER_START_CMD="python -u /home/<USER>/service.py"
echo "===========容器中的信息==========="
echo "容器名称: $CONTAINER_NAME"
echo "容器端口: $CONTAINER_PORT"
echo "容器日志目录: $CONTAINER_LOG_DIR"
echo "容器启动命令: $CONTAINER_START_CMD"
echo "===========容器中的信息==========="

echo "===========应用宿主机信息==========="
echo "应用名称: $APP_NAME"
echo "应用目录: $APP_DIR"
echo "应用日志目录: $APP_LOG_DIR"
echo "应用端口: $APP_PORT"
echo "镜像名称: $IMAGE_NAME"
echo "===========应用宿主机信息==========="

# 创建目录
echo "创建目录: $APP_DIR"
sudo mkdir -p $APP_DIR
echo "清理目录: rm -rf $APP_DIR"
sudo rm -rf $APP_DIR
echo "创建目录: $APP_DIR/logs"
mkdir -p $APP_DIR/logs
echo "设置权限: chmod -R 755 $APP_DIR"
sudo chmod -R 755 $APP_DIR

tree $APP_DIR




# 查看容器是否存在，存在则删除
CONTAINER_ID=$(docker ps -a -q --filter "name=$APP_NAME")
if [ -n "$CONTAINER_ID" ]; then
    echo "容器存在，删除容器"
    docker rm -f $APP_NAME
fi  

CONTAINER_CMD="docker run -d --name $APP_NAME  \
    --restart=always \
    --network=jiutianwensi \
    -p $APP_PORT:$CONTAINER_PORT \
    -v $APP_LOG_DIR:$CONTAINER_LOG_DIR \
    $IMAGE_NAME $CONTAINER_START_CMD"
echo "启动容器命令: $CONTAINER_CMD"

# 执行启动命令
$CONTAINER_CMD

# 获取容器id
CONTAINER_ID=$(docker ps -q --filter "name=$APP_NAME")
echo "容器id: $CONTAINER_ID"
# 查看 容器状态
docker ps  --filter "name=$APP_NAME"

# 等待10秒
echo "等待10秒"
sleep 10

# 获取容器日志
echo "获取容器日志"
docker logs  $APP_NAME

# 验证容器是否启动成功
echo "验证容器是否启动成功"
docker ps  --filter "name=$APP_NAME"

# 验证接口
echo '验证接口：curl -X POST -H "Content-Type: application/json" -d {"texts": ["我在BeiJing天安门。"]} http://localhost:8084/Rag/QueryParser'
curl -X POST -H "Content-Type: application/json" \
     -d '{"texts": ["我在BeiJing天安门。"]}' \
     http://localhost:8084/Rag/QueryParser

echo -e "\n接口验证完成"
