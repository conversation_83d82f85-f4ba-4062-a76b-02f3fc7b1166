spring:
  datasource:
    url: *************************************
    username: admin
    password: PgSQL@2025

  # redis配置
  redis:
    host: ********
    port: 6379
    password: admin123

minio:
  endpoint: http://*********:9000
  access-key: admin
  secret-key: MiniO@2025
  bucket-name: ai-knowledge  # 存储桶名称（需存在或自动创建）

#知识库上传
knowledge:
  document:
    upload-url: http://ai-knowledge-controller:8080/openapi/v1/document/upload/fileIds
    delete-url: http://ai-knowledge-controller:8080/openapi/v1/document/delete
    app_id: 1
    app_key: 2
    system_id: afsoprmv
  manage:
    save-url: http://ai-knowledge-controller:8080/openapi/v1/knowledge/create
    update-url: http://ai-knowledge-controller:8080/openapi/v1/knowledge/update
    delete-url: http://ai-knowledge-controller:8080/openapi/v1/knowledge/delete
    system_id: afsoprmv
logging:
  config: /app/config/logback-spring.xml