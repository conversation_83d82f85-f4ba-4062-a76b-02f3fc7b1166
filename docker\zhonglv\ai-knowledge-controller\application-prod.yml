server:
  port: 8080
  tomcat:
    accept-count: 200
    min-spare-threads: 30
    max-threads: 200
    max-connections: 10000

spring:
  application:
    name: ai-knowledge-controller
  # db数据源
  datasource:
    driverClassName: org.postgresql.Driver
    url: **********************************************************
    username: admin
    password: PgSQL@2025
  data:
    # spring-data-redis
    redis:
      host: ********
      port: 6379
      password: admin123
      timeout: 6000ms  # 连接超时时长（毫秒）
      lettuce:
        pool:
          max-active: 150  # 连接池最大连接数（使用负值表示没有限制）
          max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-idle: 16      # 连接池中的最大空闲连接
          min-idle: 16       # 连接池中的最小空闲连接
          time-between-eviction-runs: 120s

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      maxFileSize: 200MB
      maxRequestSize: 200MB

logging:
  config: /app/config/logback-spring.xml
  level:
    com.chinamobile.cmit.ai: debug
    org.springframework: info

capabilityapi:
  # 多模型配置
  models:
    deepseek32b:
      docChatUrl: http://rag-qa-online:8080/Rag/QAOnlineSSE        # 智能问答
      docChatAppid: ragcw-ds                                                                         # 对话服务应用ID
      docChatAppkey: 232a0ab2fde842493b187115605f1846                                                # 对话服务密钥                                          # 对话服务密钥
  # 文档解析参数 贵阳国产化集群
  docParserChunkUrl: http://rag-doc-parser:8000/doc_generate_qa/v1/service   # 文档解析服务地址
  docParserAppid: ragonlin                                                                           # 文档解析服务应用ID
  docParserAppkey: fe4a7c95f91bdd452e99699bc981bbbd                                                  # 文档解析服务密钥
  # 向量库参数 宁波国产化集群
  knowledgeBaseApiUrl: http://rag-kg-os:8080/kg/aud               # 向量库基础API地址，新增，删除，修改知识库
  qaChangeApiUrl: http://rag-kg-os:8080/qa/aud                    # 问答对批量删除接口地址
  qaBatchAddApiUrl: http://rag-kg-os:8080/qa/add/batch            # 问答对批量添加接口地址
  knowledgeBaseAppKey: b8d8a50b547e204e3479c39d7eeb71be                                              # 向量库访问密钥
  knowledgeBaseAppId: ragkgdata                                                                       # 向量库应用ID
  # 统一用户能力平台接口参数
  tyyhBaseUrl: http://wnsp-prd-fw-gw.wnsp-prd-fw-gw:8099/tyyh/bap/gw/oppf/                           # 统一用户能力平台接口地址
  tywdBaseUrl: http://wnsp-prd-fw-gw.wnsp-prd-fw-gw:8099/tyyh/bap/gw/updown/iuMZZr/                  # 统一文档能力平台接口地址
  uploadPath: /app/files                                                # 文件上传路径
  gatewayTokenUrl: http://wnsp-prd-fw-gw.wnsp-prd-fw-gw:8099/tyyh/bap/gw/aopoauth/oauth/token        # 网关token地址
  tyyhBaseAppId: 10640                                                                               # 统一用户能力平台应用ID
  tyyhBaseAppKey: e9f070e8f251af23a98cf4db4cbaf184                                                   # 统一用户能力平台应用密钥
  clientId: uni_621_webaia                                                                           # 客户端ID
  clientSecret: U2FsdGVkX1/XBeJAMBHCw3Vi9LUcGmsaC4Kz9afiAV8=                                         # 客户端密钥
  smsApiBaseUrl: http://wnsp-prd-fw-gw.wnsp-prd-fw-gw:8099/xxzx/sms/v1/submit                        # 短信API地址
  smsAppKey: 762abdfb4d3a40178ced1dff074092ba                                                        # 短信API应用ID
  smsAppSecret: gjTxyLPxDusP8AYP3gKUiMropCLTAOuu                                                # 短信API应用密钥
  minio:
    endpoint: http://*********:9000
    access-key-id: admin
    access-key-secret: MiniO@2025
    bucket-name: ai-knowledge

opensearch:
  hostname: *********
  port: 9200
  scheme: https
  username: admin
  password: admin
  appid: qasystem
  appkey: 425be7ea62a7684db018995f04030ca2
  segment-url:  http://query-parser:7890/Rag/QueryParser
  embedding-url: http://**********:8101/Rag/BgeM3Embedding

api:
  auth:
    systems:
      ids: 9bq3z7p2,bgdmoapi,afsoprmv  # 系统ID列表，逗号分隔
      9bq3z7p2:  # 财务家园的认证信息
        appId: 9bq3z7p2
        appKey: 6d8e1f4a9c2b7e5f3a0d8c4b9e2f7d1
        # callbackUrl: http://*************:13201/core/api/v1/knowledge/document/callback
        callbackUrl: http://wnsp-prd-fw-gw.wnsp-prd-fw-gw:8099/erp/core/api/v1/knowledge/document/callback
        capabilityName: FinancialHomeAI
      bgdmoapi:  # 供应链的认证信息
        appId: bgdmoapi
        appKey: 52c76073075b8d4f02eb596688b94679
        capabilityName: officeLM
      afsoprmv:  # 系统3的认证信息
        appId: afsoprmv
        appKey: 52c76073075b8d4f02eb596688b946d3
        callbackUrl: http://rbac:8080/knowledge/doc/callback
        capabilityName: zhonglv

management:
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: "*"
