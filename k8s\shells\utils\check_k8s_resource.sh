#!/bin/bash

# 检查K8S资源是否存在
# 参数: $1 - 服务名称, $2 - 命名空间
# 返回: FOUND_RESOURCE=true/false, RESOURCE_TYPE=deployment/statefulset/...
function check_k8s_resource() {
    local service_name=$1
    local namespace=$2
    
    # 尝试查找各种类型的资源
    FOUND_RESOURCE=false
    RESOURCE_TYPE=""
    
    # 检查Deployment
    kubectl get deployment $service_name -n $namespace &>/dev/null
    if [ $? -eq 0 ]; then
        FOUND_RESOURCE=true
        RESOURCE_TYPE="deployment"
        echo "发现 Deployment: $service_name"
        return
    fi
    
    # 检查StatefulSet
    kubectl get statefulset $service_name -n $namespace &>/dev/null
    if [ $? -eq 0 ]; then
        FOUND_RESOURCE=true
        RESOURCE_TYPE="statefulset"
        echo "发现 StatefulSet: $service_name"
        return
    fi
    
    # 检查DaemonSet
    kubectl get daemonset $service_name -n $namespace &>/dev/null
    if [ $? -eq 0 ]; then
        FOUND_RESOURCE=true
        RESOURCE_TYPE="daemonset"
        echo "发现 DaemonSet: $service_name"
        return
    fi
    
    # 检查Service
    kubectl get service $service_name -n $namespace &>/dev/null
    if [ $? -eq 0 ]; then
        FOUND_RESOURCE=true
        RESOURCE_TYPE="service"
        echo "发现 Service: $service_name"
        return
    fi
    
    # 检查ConfigMap
    kubectl get configmap $service_name -n $namespace &>/dev/null
    if [ $? -eq 0 ]; then
        FOUND_RESOURCE=true
        RESOURCE_TYPE="configmap"
        echo "发现 ConfigMap: $service_name"
        return
    fi
    
    # 检查Secret
    kubectl get secret $service_name -n $namespace &>/dev/null
    if [ $? -eq 0 ]; then
        FOUND_RESOURCE=true
        RESOURCE_TYPE="secret"
        echo "发现 Secret: $service_name"
        return
    fi

    # 检查PersistentVolumeClaim
    kubectl get pvc $service_name -n $namespace &>/dev/null
    if [ $? -eq 0 ]; then
        FOUND_RESOURCE=true
        RESOURCE_TYPE="pvc"
        echo "发现 PersistentVolumeClaim: $service_name"
        return
    fi
    
    echo "未发现任何与 $service_name 相关的资源"
}

# 导出函数
export -f check_k8s_resource 