#!/bin/bash

# 颜色设置
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# ====================== API测试命令 ======================
echo -e "${BLUE}=== 文档生成API测试 ===${NC}"

# 测试DOCX文件上传
echo -e "${YELLOW}测试DOCX文件上传...${NC}"
echo 'curl -X POST -F "file=@test.txt;type=text/plain" -F "description=This is a test txt file" http://127.0.0.1:8098/doc_generate_qa/v1/service'
curl -X POST \
  -F "file=@test.txt;type=text/plain" \
  -F "description=This is a test txt file" \
  http://127.0.0.1:8098/doc_generate_qa/v1/service

echo
echo -e "${GREEN}=== 测试结束 ===${NC}"


