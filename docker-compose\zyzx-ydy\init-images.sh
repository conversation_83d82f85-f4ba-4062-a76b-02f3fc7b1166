#!/bin/bash
echo "===== 初始化镜像 ====="
# 判断当前目录是否存在images目录，如果存在则加载镜像
# 下载中间件镜像
# 下载pgsql
echo "下载pgsql镜像"
docker pull m.daocloud.io/docker.io/postgres:12.18
docker tag m.daocloud.io/docker.io/postgres:12.18 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/postgres:arm64-12.18

# 下载redis
echo "下载redis镜像"
docker pull m.daocloud.io/docker.io/redis:7.4.1
docker tag m.daocloud.io/docker.io/redis:7.4.1 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/redis:arm64-7.4.1

# 下载opensearch
echo "下载opensearch镜像"
docker pull public.ecr.aws/opensearchproject/opensearch:2.9.0
docker tag public.ecr.aws/opensearchproject/opensearch:2.9.0 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch:arm64-2.9.0

# 下载opensearch-dashboards 
echo "下载opensearch-dashboards镜像"
docker pull public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0
docker tag public.ecr.aws/opensearchproject/opensearch-dashboards:2.9.0 officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch-dashboards:arm64-2.9.0

# 下载minio
echo "下载minio镜像"
docker pull m.daocloud.io/docker.io/minio/minio:RELEASE.2023-04-28T18-11-17Z
docker tag m.daocloud.io/docker.io/minio/minio:RELEASE.2023-04-28T18-11-17Z officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/minio:arm64-RELEASE.2023-04-28T18-11-17Z

#登录镜像仓库
echo "登录镜像仓库"
echo "docker login officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn --username=zglyjt --password=Znwd0415@"
docker login officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn --username=zglyjt --password=Znwd0415@

#推送镜像
echo "推送镜像"
echo "docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/postgres:arm64-12.18"
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/postgres:arm64-12.18
echo "docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/redis:arm64-7.4.1"
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/redis:arm64-7.4.1
echo "docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch:arm64-2.9.0"
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch:arm64-2.9.0
echo "docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch-dashboards:arm64-2.9.0"
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/opensearch-dashboards:arm64-2.9.0
echo "docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/minio:arm64-RELEASE.2023-04-28T18-11-17Z"
docker push officellm-560026dc.ecis.zs-beijing-1.cmecloud.cn/jtws/minio:arm64-RELEASE.2023-04-28T18-11-17Z