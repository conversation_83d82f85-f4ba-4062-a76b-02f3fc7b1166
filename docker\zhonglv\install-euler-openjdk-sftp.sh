pipeline {
    agent any

    parameters {
        string(
            name: 'TARGET_HOSTS',
            defaultValue: '**********,**********,**********,*********,*********',
            description: '目标主机列表（用逗号分隔）'
        )
        string(
            name: 'REMOTE_USER',
            defaultValue: 'wensi',
            description: '远程主机用户名'
        )
        string(
            name: 'SSH_CREDENTIALS_ID',
            defaultValue: '28860a4d-5536-4f23-b79d-0d011242aebd',
            description: 'Jenkins 中配置的 SSH 凭据 ID'
        )
    }

    stages {
        stage('Check Local Files') {
            steps {
                script {
                    def localFiles = sh(
                        script: 'ls /opt/data/*.tar 2>/dev/null || true',
                        returnStdout: true
                    ).trim()

                    if (localFiles.isEmpty()) {
                        error "未找到任何 .tar 文件，请确保文件已上传到 /opt/data/ 目录。"
                    } else {
                        echo "找到的本地文件：${localFiles}"
                    }
                }
            }
        }

        stage('Push to Target Hosts with SCP') {
            steps {
                script {
                    def targetHosts = params.TARGET_HOSTS.split(',')
                    def remoteUser = params.REMOTE_USER
                    def sshCredentialsId = params.SSH_CREDENTIALS_ID

                    withCredentials([usernamePassword(credentialsId: "${sshCredentialsId}", usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                        for (host in targetHosts) {
                            echo "正在向主机 ${host} 传输文件..."
                            // 捕获 SCP 退出码，不中断流程
                            def scpExitCode = sh(
                                script: "sshpass -p '${PASSWORD}' scp /opt/data/*.tar ${remoteUser}@${host}:/home/<USER>/",
                                returnStatus: true
                            )
                            if (scpExitCode != 0) {
                                echo "ERROR: 主机 ${host} 文件传输失败，退出码 ${scpExitCode}"
                            } else {
                                echo "文件传输完成。"
                            }
                        }
                    }
                }
            }
        }

        stage('Execute Remote Commands') {
            steps {
                script {
                    def targetHosts = params.TARGET_HOSTS.split(',')
                    def remoteUser = params.REMOTE_USER
                    def sshCredentialsId = params.SSH_CREDENTIALS_ID

                    withCredentials([usernamePassword(credentialsId: "${sshCredentialsId}", usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                        for (host in targetHosts) {
                            echo "正在执行远程命令（主机：${host}）..."
                            // 捕获 SSH 退出码，不中断流程
                            def sshExitCode = sh(
                        script: """
                            sshpass -p '${PASSWORD}' ssh -o StrictHostKeyChecking=no ${remoteUser}@${host} << 'EOF'
                           sudo mkdir -p /data/images
                           sudo  mv /home/<USER>/*.tar /data/images/ 2>/dev/null || true
                           sudo cd /data/images
                            for tarFile in *.tar; do
                               sudo docker load -i "\${tarFile}" || echo "加载镜像 \${tarFile} 失败"
                            done
EOF  # 注意：EOF 必须顶格写，无缩进
                        """,
                                returnStatus: true
                            )
                            if (sshExitCode != 0) {
                                echo "ERROR: 主机 ${host} 远程命令执行失败，退出码 ${sshExitCode}"
                            } else {
                                echo "远程命令执行完成。"
                            }
                        }
                    }
                }
            }
        }
    }
}
